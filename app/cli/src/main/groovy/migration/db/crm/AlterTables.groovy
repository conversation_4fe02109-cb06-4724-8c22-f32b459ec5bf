package migration.db.crm

import lib.data.DBMigrationRunnable
import lib.data.DBMigrationRunnableSet
import lib.data.RunnableReporter
import net.datatp.cli.ShellApplicationContext
import net.datatp.module.data.db.util.DBConnectionUtil

import java.sql.Connection

public class AlterTableSet extends DBMigrationRunnableSet {
  public AlterTableSet() {
    super("""Alter CRM Tables""");

    String label = """Alter CRM Tables""";
    DBMigrationRunnable alterTables = new DBMigrationRunnable(label) {
        @Override
        public void run(RunnableReporter reporter, DBConnectionUtil connUtil) {
          connUtil.execute("ALTER TABLE public.lgc_price_inquiry_request ADD saleman_branch_name varchar NULL;")
          connUtil.execute("""
            UPDATE lgc_price_inquiry_request lpir 
            SET saleman_branch_name = (
                  SELECT 
                  distinct cc.label 
                  FROM company_company cc WHERE cc.id = lpir.company_id
                )
          """)
        }
    };
    addRunnable(alterTables);
  }
}

ShellApplicationContext shellContext = (ShellApplicationContext) SHELL_CONTEXT;

Connection conn = shellContext.getPrimaryDBConnection();
DBConnectionUtil connUtil = new DBConnectionUtil(conn);

RunnableReporter reporter = new RunnableReporter("dbmigration", "latest")

AlterTableSet migration = new AlterTableSet();
migration.run(reporter, connUtil);

connUtil.close();
return "DONE!!!"