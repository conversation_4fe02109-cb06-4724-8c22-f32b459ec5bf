import React from 'react';
import * as FeatherIcon from 'react-feather';
import { util, bs, app } from '@datatp-ui/lib';
import { UIBookingList, UIBookingListPlugin } from '../booking/UIBookingList';
import { UISpecificQuotationList, UISpecificQuotationPlugin } from '../quotation';
import { UIInquiryDashboard } from '../../price/request/UIPricingDashboard';

const SESSION = app.host.DATATP_HOST.session;

type ViewName = 'inquiry-request' | 'quotations' | 'bookings';

interface UICompanyPriceDashboardProps extends bs.WindowTemplateProps, app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
export class UICompanyDashboardWindow extends bs.WindowTemplate<UICompanyPriceDashboardProps> {
  activeMainView: ViewName;

  constructor(props: UICompanyPriceDashboardProps) {
    super(props);
    this.activeMainView = 'inquiry-request';

    let groupConfig = bs.ActionConfigUtil.findFirstActionGroupConfig('tab-group', this.toolbarConfig.actionGroupConfigs);
    bs.ActionConfigUtil.toggleActionConfig(this.activeMainView, groupConfig.actions)
  }

  override createWindowToolbarConfig(): bs.WindowToolbarConfig {

    let appPermissions = SESSION.getCurrentCompanyContext().appPermissions || [];
    const hasSalesCompanyPermission = appPermissions.some(
      permission => permission.appName === "user-logistics-sales" && permission.dataScope !== "Owner"
    );

    let actions: any = [
      {
        laf: 'secondary', name: 'inquiry-request', label: 'Inquiry Requests', Icon: FeatherIcon.HelpCircle,
        onClick: () => { this.onToggleTabGroup('tab-group', 'inquiry-request') }
      },
    ];

    if (hasSalesCompanyPermission) {
      actions.push(
        {
          laf: 'secondary', name: 'quotations', label: 'Quotations', Icon: FeatherIcon.FileText,
          onClick: () => { this.onToggleTabGroup('tab-group', 'quotations') },
        },
        {
          laf: 'secondary', name: 'bookings', label: 'Bookings', Icon: FeatherIcon.Calendar,
          onClick: () => { this.onToggleTabGroup('tab-group', 'bookings') }
        },
      );
    }

    const mainTabGroupConfig: bs.ActionGroupConfig = {
      laf: 'group', smallScreenLaf: 'popover', align: 'left',
      name: 'tab-group', label: 'Group', Icon: FeatherIcon.Airplay,
      actions: actions
    };

    if (!this.activeMainView && actions.length > 0) {
      this.activeMainView = actions[0].name;
    }

    let toolbarConfig: bs.WindowToolbarConfig = {
      actionGroupConfigs: [mainTabGroupConfig]
    };

    return toolbarConfig;
  }

  onToggleTabGroup = (group: string, name: ViewName) => {
    this.activeMainView = name as ViewName;
    this.toolbarConfig = this.createWindowToolbarConfig();
    let groupConfig = bs.ActionConfigUtil.findFirstActionGroupConfig(group, this.toolbarConfig.actionGroupConfigs);
    bs.ActionConfigUtil.toggleActionConfig(name, groupConfig.actions);
    this.forceUpdate();
  }


  renderInquiryRequestTab() {
    if (this.activeMainView !== 'inquiry-request') return <></>;
    const { appContext, pageContext, space } = this.props;
    return <UIInquiryDashboard appContext={appContext} pageContext={pageContext} space={space} />
    // return <UIInquiryRequestReportPageNew appContext={appContext} pageContext={pageContext} space={space} />
  }

  renderQuotationTab() {
    if (this.activeMainView !== 'quotations') return <></>;
    const { appContext, pageContext, space } = this.props;
    return <UISpecificQuotationList appContext={appContext} pageContext={pageContext} space={space}
      plugin={new UISpecificQuotationPlugin(space)} />;
  }

  renderBookingTab() {
    const { space } = this.props;
    if (this.activeMainView !== 'bookings') return <></>;
    return <UIBookingList {...this.props} type={'page'} plugin={new UIBookingListPlugin(space)} />
  }

  renderWindowContent(): React.ReactElement {
    return (
      <div className="flex-vbox" key={util.IDTracker.next()}>
        {this.renderInquiryRequestTab()}
        {this.renderQuotationTab()}
        {this.renderBookingTab()}
      </div>
    );
  }

}


