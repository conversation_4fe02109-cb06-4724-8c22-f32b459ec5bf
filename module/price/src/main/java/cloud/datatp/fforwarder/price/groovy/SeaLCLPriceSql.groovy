package cloud.datatp.fforwarder.price.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.module.data.db.query.RangeFilter
import net.datatp.util.ds.MapObject
import net.datatp.util.text.DateUtil
import net.datatp.util.text.StringUtil
import org.springframework.context.ApplicationContext

class SeaLCLPriceSql extends Executor {

  public class SearchSeaLCLPrice extends ExecutableSqlBuilder {

    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String carrierLabel = sqlParams.getString("carrierLabel", null);
      String handlingAgentPartnerLabel = sqlParams.getString("handlingAgentPartnerLabel", null);
      RangeFilter filter = (RangeFilter) sqlParams.get("validTo");

      String filterByDate = ""
      if (filter != null) {

        if (StringUtil.isNotEmpty(filter.getFromValue())) {
          sqlParams.put("validToFrom", DateUtil.parseCompactDateTime(filter.getFromValue()));
          filterByDate = " AND (charge.valid_to >= :validToFrom)"
        }

        if (StringUtil.isNotEmpty(filter.getToValue())) {
          sqlParams.put("validToTo", DateUtil.parseCompactDateTime(filter.getToValue()));
          filterByDate += " AND (charge.valid_to <= :validToTo)"
        }

      }

      String orderBy = """
                ORDER BY charge.from_location_code, charge.to_location_code,
                         charge.created_time DESC, charge.valid_to DESC 
            """

      if (!isNotContainKey(sqlParams, "pricingCreatorId")) {
        orderBy = """ ORDER BY charge.modified_time DESC """
      }
      String query = """
                WITH frequency_data AS (
                    SELECT sea_lcl_charge_id,
                           STRING_AGG(depart_time, ', ')        AS etd,
                           STRING_AGG(cut_off_time, ', ')       AS cutoff,
                           STRING_AGG(label, ', ')              AS frequency,
                           STRING_AGG(transit_time, ', ')       AS transit_time,
                           STRING_AGG(transit_label, ', ')      AS transit_port
                    FROM lgc_price_transport_frequency
                    GROUP BY sea_lcl_charge_id
                ), add_charge_data AS (
                    SELECT sea_lcl_charge_id,
                          json_agg(
                            json_build_object(
                              'id', id, 
                              'label', label, 
                              'unit', unit, 
                              'finalCharge', final_charge,
                              'note', note
                              )) AS additional_charges
                    FROM lgc_price_transport_additional_charge
                    GROUP BY sea_lcl_charge_id
                )
                SELECT  
                  charge.id                                     AS id,
                  charge.code                                   AS code,
                  charge.label                                  AS label,
                  charge.route                                  AS route,
                  charge.carrier_route                          AS carrier_route,
                  charge.from_location_code                     AS from_location_code,
                  charge.from_location_label                    AS from_location_label,
                  charge.to_location_code                       AS to_location_code,
                  charge.to_location_label                      AS to_location_label,
                  charge.carrier_partner_id                     AS carrier_partner_id,
                  charge.carrier_label                          AS carrier_label,
                  charge.handling_agent_partner_id              AS handling_agent_partner_id,
                  charge.handling_agent_partner_label           AS handling_agent_partner_label,
                  charge.assignee_account_id                    AS assignee_account_id,
                  charge.assignee_label                         AS assignee_label,
                  charge.purpose                                AS purpose,
                  charge.feedbacks                              AS feedbacks,
            
                  -- Tariff
                  charge.freight_charge_lcl                     AS freight_charge_l_c_l,
                  charge.freight_charge_lcl_note                AS freight_charge_l_c_l_note,
                  charge.minimum_charge_lcl                     AS minimum_charge_l_c_l,
                  charge.minimum_charge_lcl_note                AS minimum_charge_l_c_l_note,
                  charge.less_2_cbm_price                       AS less_2_cbm_price,
                  charge.less_2_cbm_price_note                  AS less_2_cbm_price_note,
                  charge.less_3_cbm_price                       AS less_3_cbm_price,
                  charge.less_3_cbm_price_note                  AS less_3_cbm_price_note,
                  charge.less_5_cbm_price                       AS less_5_cbm_price,
                  charge.less_5_cbm_price_note                  AS less_5_cbm_price_note,
                  charge.less_7_cbm_price                       AS less_7_cbm_price,
                  charge.less_7_cbm_price_note                  AS less_7_cbm_price_note,
                  charge.less_10_cbm_price                      AS less_10_cbm_price,
                  charge.less_10_cbm_price_note                 AS less_10_cbm_price_note,
                  charge.geq_10_cbm_price                       AS geq_10_cbm_price,
                  charge.geq_10_cbm_price_note                  AS geq_10_cbm_price_note,
                  -- End Tariff
            
                  charge.currency                               AS currency,
                  charge.valid_from                             AS valid_from,
                  charge.valid_to                               AS valid_to,
                  charge.stuffing_note                          AS stuffing_note,
                  charge.surcharge_note                         AS surcharge_note,
                  charge.local_charge_at_dest                   AS local_charge_at_dest,
                  charge.note                                   AS note,
            
                  charge.storage_state                          AS storage_state,
                  charge.edit_mode                              AS edit_mode,
                  charge.shareable                              AS shareable,
                  charge.created_by                             AS created_by,
                  charge.created_time                           AS created_time,
                  charge.modified_by                            AS modified_by,
                  charge.modified_time                          AS modified_time,
                  charge.version                                AS version,
                  charge.company_id                             AS company_id,

                  -- frequency concatenated by comma
                  freq.etd                                      AS etd, 
                  freq.cutoff                                   AS cutoff,
                  freq.frequency                                AS frequency,
                  freq.transit_time                             AS transit_time,
                  freq.transit_port                             AS transit_port,
                  add.additional_charges                        AS additional_charges
                FROM lgc_price_sea_lcl_charge AS charge
                  LEFT JOIN frequency_data AS freq ON charge.id = freq.sea_lcl_charge_id
                  LEFT JOIN add_charge_data AS add ON charge.id = add.sea_lcl_charge_id
                WHERE ${FILTER_BY_STORAGE_STATE('charge', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.purpose', 'purpose', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.from_location_code', 'fromLocationCode', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.to_location_code', 'toLocationCode', sqlParams)}
                  ${filterByDate}
                  ${AND_FILTER_BY_PARAM('charge.assignee_account_id', 'pricingCreatorId', sqlParams)}
                  ${AND_SEARCH_BY(['charge.carrier_label'], carrierLabel)}
                  ${AND_SEARCH_BY(['charge.handling_agent_partner_id'], handlingAgentPartnerLabel)}
                  ${AND_FILTER_BY_RANGE('charge.modified_time', 'modifiedTime', sqlParams)}
                  ${AND_FILTER_BY_RANGE('charge.created_time', 'createdTime', sqlParams)}
                  AND (
                    (charge.shareable = 'COMPANY' AND charge.company_id = :companyId) OR 
                    (charge.shareable = 'DESCENDANTS' AND charge.company_id IN (:descendantsCompanyIds)) OR
                    (charge.shareable = 'ORGANIZATION')
                  )
                ${orderBy}
                ${MAX_RETURN(sqlParams)}
              """;
      return query;
    }
  }

  public SeaLCLPriceSql() {
    register(new SearchSeaLCLPrice());
  }
}