package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.price.calculator.AdditionalChargeCalculator;
import cloud.datatp.fforwarder.price.common.BaseTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge.GroupType;
import cloud.datatp.fforwarder.price.entity.SeaLclTransportCharge;
import cloud.datatp.fforwarder.price.repository.SeaFclTransportChargeRepository;
import cloud.datatp.fforwarder.price.repository.SeaLclTransportChargeRepository;
import cloud.datatp.fforwarder.settings.message.CRMMessageLogic;
import cloud.datatp.fforwarder.settings.message.entity.CRMMessageSystem;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.ExternalDataSourceManager;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.hr.entity.Employee;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;
import net.datatp.util.text.TokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Getter
@Slf4j
@Component
public class SeaPriceLogic extends TransportPriceLogic {
  @Autowired
  private SeaLclTransportChargeRepository seaLclRepo;

  @Autowired
  private SeaFclTransportChargeRepository seaFclRepo;

  @Autowired
  private ExternalDataSourceManager dataSourceManager;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  public <T extends BaseTransportCharge> List<SqlMapRecord> searchSeaTransportCharge(
    ClientInfo client, Company company, SqlQueryParams sqlParams, Class<T> entity) {
    sqlParams.addParam("companyId", company.getId());
    List<Long> companyIdPaths = company.findCompanyIdPaths();
    sqlParams.addParam("companyIds", companyIdPaths);
    
    List<Company> descendantsCompanies = companyReadLogic.findByParentIds(client, companyIdPaths);
    Set<Long> descendantsCompanyIds = descendantsCompanies.stream().map(Company::getId).collect(Collectors.toSet());
    descendantsCompanyIds.addAll(companyIdPaths);
    sqlParams.addParam("descendantsCompanyIds", descendantsCompanyIds);

    if (!sqlParams.hasParam("groupType")) sqlParams.addParam("groupType", GroupType.NONE_US.toString());
    final boolean isFCL = entity.getName().equals(SeaFclTransportCharge.class.getName());
    if (isFCL) {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/price/groovy/SeaFCLPriceSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, "SearchSeaFCLPrice", sqlParams);
    } else {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/price/groovy/SeaLCLPriceSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, "SearchSeaLCLPrice", sqlParams);
    }
  }

  public boolean deleteFclPriceByIds(ClientInfo client, Company company, List<Long> ids) {
    List<SeaFclTransportCharge> charges = findSeaFclTransportChargeByIds(client, company, ids);
    for (SeaFclTransportCharge charge : charges) {
      Objects.assertTrue(charge.isSameCompany(company.getId()), "Not allowed to delete data with the different company");
    }
    DBConnectionUtil connectionUtil = new DBConnectionUtil(getJdbcDataSource());
    DeleteGraphBuilder graph = new DeleteGraphBuilder(connectionUtil, company.getId(), SeaFclTransportCharge.class, ids);
    int count = graph.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count > 0;
  }

  public boolean deleteLclPriceByIds(ClientInfo client, Company company, List<Long> ids) {
    List<SeaLclTransportCharge> charges = findSeaLclTransportChargeByIds(client, company, ids);
    for (SeaLclTransportCharge charge : charges) {
      Objects.assertTrue(charge.isSameCompany(company.getId()), "Not allowed to delete data with the different company");
    }
    DBConnectionUtil connectionUtil = new DBConnectionUtil(getJdbcDataSource());
    DeleteGraphBuilder graph = new DeleteGraphBuilder(connectionUtil, company.getId(), SeaLclTransportCharge.class, ids);
    int count = graph.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count > 0;
  }

  // ========================= LCL =========================

  public SeaLclTransportCharge getSeaLclTransportChargeById(ClientInfo client, Company company, Long id) {
    return seaLclRepo.findById(id).get();
  }

  public SeaLclTransportCharge getSeaLclPriceByCode(ClientInfo client, Company company, String code) {
    return seaLclRepo.getByCode(code);
  }

  public SeaLclTransportCharge saveSeaLclTransportCharge(ClientInfo client, Company company, SeaLclTransportCharge charge) {
    if (charge.isNew()) {
      final String code = generateTransportPriceCode(charge);
      charge.setCode(code);
      Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
      Objects.assertNotNull(employee, "Employee not found!!!, login id: " + client.getRemoteUser());
      charge.setAssigneeAccountId(employee.getAccountId());
      charge.setAssigneeLabel(employee.getLabel());
    }
//    else {
//      Objects.assertTrue(charge.isSameCompany(company.getId()), "Not allowed to save data with the different company");
//    }
    final String fromLocCode = charge.getFromLocationCode();
    final String label = TokenUtil.labelWithToken(fromLocCode, charge.getToLocationCode());
    charge.setLabel(label.toUpperCase());
    charge.setRoute(charge.getFromLocationCode() + "-" + charge.getToLocationCode());
    if (StringUtil.isEmpty(charge.getCarrierLabel())) charge.setCarrierRoute(charge.getRoute() + "-" + "N/A");
    else charge.setCarrierRoute(charge.getRoute() + "-" + charge.getCarrierLabel());
    AdditionalChargeCalculator.calculate(charge.getAdditionalCharges());
    return saveSeaLCLTransportCharge(client, company, charge);
  }

  private SeaLclTransportCharge saveSeaLCLTransportCharge(ClientInfo client, Company company, SeaLclTransportCharge charge) {
    charge.set(client, company);
    return seaLclRepo.save(charge);
  }

  public List<MapObject> saveSeaLclModifyRecords(ClientInfo client, Company company, List<MapObject> modified) {
    long accessAccountId = client.getAccountId();
    if (Collections.isNotEmpty(modified)) {
      for (MapObject sel : modified) {
        final Long id = sel.getLong("id", null);
        SeaLclTransportCharge price = new SeaLclTransportCharge();
        if (id != null) {
          price = getSeaLclTransportChargeById(client, company, id);
          Objects.assertNotNull(price, "Sea Charge not found: id = " + id);
        }

        List<MapObject> oldFeedbacks = price.getFeedbacks();
        String myOldFeedback = null;
        if (oldFeedbacks != null) {
          for (MapObject feedback : oldFeedbacks) {
            Long ownerAccountId = feedback.getLong("salemanAccountId", null);
            if (ownerAccountId == accessAccountId) {
              myOldFeedback = feedback.getString("feedback");
              break;
            }
          }
        }

        price = price.computeFromMapObject(sel);

        if (sel.containsKey("feedbacks")) {

          List<MapObject> feedbacks = price.getFeedbacks();

          if (feedbacks != null) {
            for (MapObject feedback : feedbacks) {
              Long ownerAccountId = feedback.getLong("salemanAccountId", null);
              if (ownerAccountId == accessAccountId) {
                String myFeedback = feedback.getString("feedback", null);
                if (!myFeedback.equals(myOldFeedback)) {
                  final Long assigneeAccountId = price.getAssigneeAccountId();
                  Account assigneeAccount = accountLogic.getAccountById(client, assigneeAccountId);
                  Account salesAccount = accountLogic.getAccountById(client, accessAccountId);
                  CRMMessageSystem message = price.toPricingFeedbackUpdateMessage(assigneeAccount, salesAccount, myFeedback);
                  crmMessageLogic.scheduleMessage(client, company, message);
                }
              }
            }
          }
        }
        SeaLclTransportCharge updated = saveSeaLclTransportCharge(client, company, price);
        sel.put("id", updated.getId());
      }
    }
    return modified;
  }

  public boolean changeSeaLclStorageState(ClientInfo client, ChangeStorageStateRequest request) {
    List<Long> chargeIds = request.getEntityIds();
    if (chargeIds.isEmpty()) throw RuntimeError.IllegalArgument("No Records were selected");
    seaLclRepo.setStorageState(chargeIds, request.getNewStorageState());
    return true;
  }

  public List<SeaLclTransportCharge> findSeaLCLByCompany(ClientInfo client, Company company) {
    return seaLclRepo.findByCompanyId(company.getId());
  }

  public List<SeaLclTransportCharge> findSeaLclTransportChargeByIds(ClientInfo client, Company company, List<Long> ids) {
    return seaLclRepo.findByIds(ids);
  }

  public List<SeaLclTransportCharge> findSeaLclTransportChargeExpired(ClientInfo client, Company company, Date currentDate) {
    return seaLclRepo.findSeaLclTransportChargesExpired(company.getId(), currentDate);
  }

  // ==================== FCL ====================
  public SeaFclTransportCharge getSeaFclPriceByCode(ClientInfo client, Company company, String code) {
    return seaFclRepo.getByCode(code);
  }

  public SeaFclTransportCharge getSeaFclTransportChargeById(ClientInfo client, Company company, Long id) {
    return seaFclRepo.findById(id).get();
  }

  public List<SeaFclTransportCharge> findSeaFclTransportChargeByIds(ClientInfo client, Company company, List<Long> ids) {
    return seaFclRepo.findByIds(ids);
  }

  public List<SeaFclTransportCharge> findSeaFclByCompany(ClientInfo client, Company company) {
    return seaFclRepo.findByCompanyId(company.getId());
  }

  public SeaFclTransportCharge saveSeaFclTransportCharge(ClientInfo client, Company company, SeaFclTransportCharge charge) {
    if (charge.isNew()) {
      String code = generateTransportPriceCode(charge);
      charge.setCode(code);
      if (charge.getAssigneeAccountId() == null) {
        Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
        Objects.assertNotNull(employee, "Employee not found!!!, login id: " + client.getRemoteUser());
        charge.setAssigneeAccountId(employee.getAccountId());
        charge.setAssigneeLabel(employee.getLabel());
      }
    }
//    else {
//      Objects.assertTrue(charge.isSameCompany(company.getId()), "Not allowed to save data with the different company");
//    }
    final String fromLocCode = charge.getFromLocationCode();
    final String label = TokenUtil.labelWithToken(fromLocCode, charge.getToLocationCode(), charge.getCarrierLabel());
    charge.setLabel(label.toUpperCase());
    charge.setRoute(charge.getFromLocationCode() + "-" + charge.getToLocationCode());
    if (StringUtil.isEmpty(charge.getCarrierLabel())) charge.setCarrierRoute(charge.getRoute() + "-" + "N/A");
    else charge.setCarrierRoute(charge.getRoute() + "-" + charge.getCarrierLabel());
    AdditionalChargeCalculator.calculate(charge.getAdditionalCharges());
    return saveSeaFCLTransportCharge(client, company, charge);
  }

  private SeaFclTransportCharge saveSeaFCLTransportCharge(ClientInfo client, Company company, SeaFclTransportCharge charge) {
    charge.set(client, company);
    SeaFclTransportCharge save = seaFclRepo.save(charge);
    seaFclRepo.flush();
    return save;
  }

  public List<MapObject> saveSeaFclTransportCharges(ClientInfo client, Company company, List<MapObject> modified) {
    if (Collections.isEmpty(modified)) return modified;
    long accessAccountId = client.getAccountId();
    for (MapObject sel : modified) {
      final Long id = sel.getLong("id", null);
      SeaFclTransportCharge price = new SeaFclTransportCharge();
      if (id != null) {
        price = getSeaFclTransportChargeById(client, company, id);
        Objects.assertNotNull(price, "Sea Price not found: id = " + id);
      }

      List<MapObject> oldFeedbacks = price.getFeedbacks();
      String myOldFeedback = null;
      if (oldFeedbacks != null) {
        for (MapObject feedback : oldFeedbacks) {
          Long ownerAccountId = feedback.getLong("salemanAccountId", null);
          if (ownerAccountId == accessAccountId) {
            myOldFeedback = feedback.getString("feedback");
            break;
          }
        }
      }

      price = Objects.ensureNotNull(price, SeaFclTransportCharge::new);
      price = price.computeFromMapObject(sel);

      if (sel.containsKey("feedbacks")) {

        List<MapObject> feedbacks = price.getFeedbacks();
        if (feedbacks != null) {
          for (MapObject feedback : feedbacks) {
            Long ownerAccountId = feedback.getLong("salemanAccountId", null);
            if (ownerAccountId == accessAccountId) {
              String myFeedback = feedback.getString("feedback", null);
              if (!myFeedback.equals(myOldFeedback)) {
                final Long assigneeAccountId = price.getAssigneeAccountId();
                Account assigneeAccount = accountLogic.getAccountById(client, assigneeAccountId);
                Account salesAccount = accountLogic.getAccountById(client, accessAccountId);
                CRMMessageSystem message = price.toPricingFeedbackUpdateMessage(assigneeAccount, salesAccount, myFeedback);
                crmMessageLogic.scheduleMessage(client, company, message);
              }
            }
          }
        }
      }

      SeaFclTransportCharge updated = saveSeaFclTransportCharge(client, company, price);
      sel.put("id", updated.getId());
    }
    return modified;
  }

  public boolean changeSeaFclStorageState(ClientInfo client, ChangeStorageStateRequest request) {
    List<Long> chargeIds = request.getEntityIds();
    if (chargeIds.isEmpty()) throw RuntimeError.IllegalArgument("No Records were selected");
    seaFclRepo.setStorageState(chargeIds, request.getNewStorageState());
    return true;
  }

  public List<SeaFclTransportCharge> findSeaFclTransportChargeExpired(ClientInfo client, Company company, Date currentDate) {
    return seaFclRepo.findSeaFclTransportChargesExpired(company.getId(), currentDate);
  }

}