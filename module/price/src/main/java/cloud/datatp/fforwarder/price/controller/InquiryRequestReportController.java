package cloud.datatp.fforwarder.price.controller;

import cloud.datatp.fforwarder.price.service.InquiryRequestReportService;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controller xử lý inquiry request report với dữ liệu từ nhiều database
 */
@RestController
@RequestMapping("/api/inquiry-request")
public class InquiryRequestReportController {

    @Autowired
    private InquiryRequestReportService inquiryRequestReportService;

    /**
     * API lấy report inquiry request (thay thế cho query cũ)
     */
    @PostMapping("/report")
    public ResponseEntity<List<Map<String, Object>>> getInquiryRequestReport(
            @RequestBody Map<String, Object> requestBody) {
        
        try {
            // Tạo ExecutableContext từ request
            ExecutableContext ctx = createExecutableContext(requestBody);
            
            // Lấy dữ liệu từ service
            List<Map<String, Object>> result = inquiryRequestReportService.getInquiryRequestReport(ctx);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            // Log error và return error response
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Tạo ExecutableContext từ request body
     */
    private ExecutableContext createExecutableContext(Map<String, Object> requestBody) {
        ExecutableContext ctx = new ExecutableContext();
        
        // Convert request body thành MapObject
        MapObject sqlParams = new MapObject();
        if (requestBody.containsKey("sqlParams")) {
            Map<String, Object> params = (Map<String, Object>) requestBody.get("sqlParams");
            sqlParams.putAll(params);
        }
        
        ctx.setParam("sqlParams", sqlParams);
        
        return ctx;
    }
}
