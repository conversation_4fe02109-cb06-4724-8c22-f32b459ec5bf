package cloud.datatp.fforwarder.price.repository;

import cloud.datatp.fforwarder.price.entity.SeaLclTransportCharge;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.entity.StorageState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public interface SeaLclTransportChargeRepository extends JpaRepository<SeaLclTransportCharge, Serializable> {

  @Query("SELECT s FROM SeaLclTransportCharge s WHERE s.code = :code")
  SeaLclTransportCharge getByCode(@Param("code") String code);

  @Query("SELECT s FROM SeaLclTransportCharge s WHERE s.companyId = :companyId")
  List<SeaLclTransportCharge> findByCompanyId(@Param("companyId") Long companyId);

  @Modifying
  @Query("UPDATE SeaLclTransportCharge s SET s.storageState = :state WHERE s.id IN (:ids)")
  int setStorageState(@Param("ids") List<Long> ids, @Param("state") StorageState state);

  @Query("SELECT s FROM SeaLclTransportCharge s WHERE s.id IN (:ids)")
  List<SeaLclTransportCharge> findByIds(@Param("ids") List<Long> ids);

  @Query("SELECT s FROM SeaLclTransportCharge s WHERE s.companyId = :companyId AND s.validTo < :currentDate AND s.storageState = 'ACTIVE'")
  List<SeaLclTransportCharge> findSeaLclTransportChargesExpired(@Param("companyId") Long companyId, @Param("currentDate") Date currentDate);
}