package cloud.datatp.fforwarder.price.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject

class CBTContainerPriceSql extends Executor {
    public class SearchCBTContainerPrice extends ExecutableSqlBuilder {
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
                SELECT  
                  charge.id                                     AS id,
                  charge.code                                   AS code,
                  charge.label                                  AS label,
                  charge.route                                  AS route,
                  
                  charge.pickup_location_code                   AS pickup_location_code,
                  charge.pickup_location_label                  AS pickup_location_label,
                  charge.pickup_address                         AS pickup_address,
                  charge.delivery_location_code                 AS delivery_location_code,
                  charge.delivery_location_label                AS delivery_location_label,
                  charge.delivery_address                       AS delivery_address,
                  
                  charge.carrier_partner_id                     AS carrier_partner_id,
                  charge.carrier_label                          AS carrier_label,
                  charge.handling_agent_partner_id              AS handling_agent_partner_id,
                  charge.handling_agent_partner_label           AS handling_agent_partner_label,
                  
                  charge.assignee_account_id                    AS assignee_account_id,
                  charge.assignee_label                         AS assignee_label,
                  charge.km_2_way                               AS km2way,
                  charge.oil_price_in_effect                    AS oil_price_in_effect,
            
                  -- Tariff
                  charge.cont_high_cube_40_price                AS cont_high_cube_40_price,
                  charge.cont_high_cube_45_price                AS cont_high_cube_45_price,
                  charge.cont_reefer_40_price                   AS cont_reefer_40_price,
                  charge.cont_reefer_45_price                   AS cont_reefer_45_price,
                  charge.custom_fee_at_lang_son                 AS custom_fee_at_lang_son,
                  charge.custom_fee_at_pin_xiang                AS custom_fee_at_pin_xiang,
                  -- End Tariff
            
                  charge.transit_border                         AS transit_border,
                  charge.frequency                              AS frequency,
                  charge.transit_time                           AS transit_time,
                  charge.is_cbt_transportation                  AS is_C_B_T,
                  
                  charge.currency                               AS currency,
                  charge.valid_from                             AS valid_from,
                  charge.valid_to                               AS valid_to,
                  charge.note                                   AS note,
            
                  charge.storage_state                          AS storage_state,
                  charge.edit_mode                              AS edit_mode,
                  charge.shareable                              AS shareable,
                  charge.created_by                             AS created_by,
                  charge.created_time                           AS created_time,
                  charge.modified_by                            AS modified_by,
                  charge.modified_time                          AS modified_time,                            
                  charge.company_id                             AS company_id
                FROM lgc_price_truck_container_charge AS charge
                WHERE 
                  ${FILTER_BY_STORAGE_STATE('charge', sqlParams)} 
                  AND charge.is_cbt_transportation IS TRUE
                  ${AND_FILTER_BY_PARAM('charge.assignee_account_id', 'pricingCreatorId', sqlParams)}
                  ${AND_FILTER_BY_RANGE('charge.modified_time', 'modifiedTime', sqlParams)}
                  AND (
                    (charge.shareable = 'COMPANY' AND charge.company_id = :companyId) OR 
                    (charge.shareable = 'DESCENDANTS' AND charge.company_id IN (:companyIds)) OR
                    (charge.shareable = 'ORGANIZATION')
                  )
                ORDER BY 
                  charge.pickup_location_code, charge.delivery_location_code,
                  charge.modified_time ASC, charge.created_time DESC
                ${MAX_RETURN(sqlParams)}
            """;
            return query;
        }
    }

    public CBTContainerPriceSql() {
        register(new SearchCBTContainerPrice());
    }
}