package cloud.datatp.fforwarder.price.service;

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service để xử lý report inquiry request với dữ liệu từ nhiều database
 * Tách riêng logic khi HR database được tách ra
 */
@Service
public class InquiryRequestReportService {

    @Autowired
    private JdbcTemplate priceJdbcTemplate; // Database chính (price)
    
    @Autowired
    private JdbcTemplate hrJdbcTemplate; // Database HR (sẽ được tách ra)

    /**
     * Lấy dữ liệu report inquiry request kết hợp từ 2 database
     */
    public List<Map<String, Object>> getInquiryRequestReport(ExecutableContext ctx) {
        MapObject sqlParams = ctx.getParam("sqlParams");
        
        // Bước 1: <PERSON><PERSON>y dữ liệu inquiry request từ database chính
        List<Map<String, Object>> inquiryRequests = getInquiryRequestData(sqlParams);
        
        if (inquiryRequests.isEmpty()) {
            return inquiryRequests;
        }
        
        // Bước 2: Lấy danh sách account_id và company_id để query department
        Set<String> accountIds = inquiryRequests.stream()
            .map(row -> (String) row.get("saleman_account_id"))
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
            
        Set<Long> companyIds = inquiryRequests.stream()
            .map(row -> (Long) row.get("company_id"))
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        
        // Bước 3: Lấy thông tin department từ HR database
        Map<String, Map<String, Object>> departmentMap = getDepartmentInfo(accountIds, companyIds);
        
        // Bước 4: Kết hợp dữ liệu
        return combineInquiryRequestWithDepartment(inquiryRequests, departmentMap);
    }
    
    /**
     * Lấy dữ liệu inquiry request từ database chính
     */
    private List<Map<String, Object>> getInquiryRequestData(MapObject sqlParams) {
        // Sử dụng query đã tách từ InquiryRequestReport
        String sql = buildInquiryRequestQuery(sqlParams);
        
        // Thực hiện query với parameters
        Map<String, Object> params = buildQueryParameters(sqlParams);
        
        return priceJdbcTemplate.queryForList(sql, params);
    }
    
    /**
     * Lấy thông tin department từ HR database
     */
    private Map<String, Map<String, Object>> getDepartmentInfo(Set<String> accountIds, Set<Long> companyIds) {
        if (accountIds.isEmpty() || companyIds.isEmpty()) {
            return new HashMap<>();
        }
        
        String sql = """
            SELECT DISTINCT ON (empl.account_id, dept.company_id)
                dept.company_id AS dept_company_id,
                empl.account_id AS account_id,
                dept.id AS dept_id,
                dept.name AS department_name,
                dept."label" AS department_label,
                dept.parent_id_path AS parent_id_path,
                SPLIT_PART(dept.parent_id_path, '/', 1)::INTEGER AS parent_id,
                parent_dept."label" AS parent_department_label
            FROM company_hr_department dept
            JOIN company_hr_department_employee_rel dept_ref
                ON dept_ref.department_id = dept.id
            JOIN company_hr_employee empl
                ON empl.id = dept_ref.employee_id
            JOIN company_company cc
                ON dept.company_id = cc.id
            LEFT JOIN company_hr_department parent_dept
                ON parent_dept.id = SPLIT_PART(dept.parent_id_path, '/', 1)::INTEGER
            WHERE empl.account_id = ANY(?)
                AND dept.company_id = ANY(?)
        """;
        
        List<Map<String, Object>> departmentList = hrJdbcTemplate.queryForList(sql, 
            accountIds.toArray(new String[0]), 
            companyIds.toArray(new Long[0]));
        
        // Tạo map với key là "accountId_companyId"
        return departmentList.stream()
            .collect(Collectors.toMap(
                row -> row.get("account_id") + "_" + row.get("dept_company_id"),
                row -> row,
                (existing, replacement) -> existing // Giữ lại record đầu tiên nếu có duplicate
            ));
    }
    
    /**
     * Kết hợp dữ liệu inquiry request với department info
     */
    private List<Map<String, Object>> combineInquiryRequestWithDepartment(
            List<Map<String, Object>> inquiryRequests, 
            Map<String, Map<String, Object>> departmentMap) {
        
        return inquiryRequests.stream()
            .map(request -> {
                Map<String, Object> result = new HashMap<>(request);
                
                String accountId = (String) request.get("saleman_account_id");
                Long companyId = (Long) request.get("company_id");
                
                if (accountId != null && companyId != null) {
                    String key = accountId + "_" + companyId;
                    Map<String, Object> deptInfo = departmentMap.get(key);
                    
                    if (deptInfo != null) {
                        result.put("department_label", deptInfo.get("department_label"));
                        result.put("department_name", deptInfo.get("department_name"));
                        result.put("parent_department_label", deptInfo.get("parent_department_label"));
                    }
                }
                
                return result;
            })
            .collect(Collectors.toList());
    }
    
    /**
     * Build query string cho inquiry request (tạm thời hardcode, sau này có thể refactor)
     */
    private String buildInquiryRequestQuery(MapObject sqlParams) {
        // TODO: Implement query building logic tương tự như trong InquiryRequestReport
        // Hiện tại return query cơ bản
        return """
            SELECT
                cc."label" AS company_label,
                c.id, c.code, c.request_date, c.pricing_date,
                c.saleman_account_id, c.saleman_label,
                c.pricing_account_id, c.pricing_label,
                c.report_volume, c.report_volume_unit,
                c.from_location_code, c.from_location_label,
                c.to_location_code, c.to_location_label,
                c.mode, c.purpose, c.total_step_counting, c.status,
                c.company_id
            FROM lgc_price_inquiry_request c
            JOIN company_company cc ON cc.id = c.company_id
            WHERE c.storage_state != 'DELETED'
        """;
    }
    
    /**
     * Build parameters cho query
     */
    private Map<String, Object> buildQueryParameters(MapObject sqlParams) {
        Map<String, Object> params = new HashMap<>();
        // TODO: Implement parameter building logic
        return params;
    }
}
