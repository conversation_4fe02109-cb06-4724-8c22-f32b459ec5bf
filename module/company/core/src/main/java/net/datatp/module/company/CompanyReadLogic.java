package net.datatp.module.company;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyInfo;
import net.datatp.module.company.repository.CompanyInfoRepository;
import net.datatp.module.company.repository.CompanyRepository;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;

@Slf4j
@Component
public class CompanyReadLogic extends DAOService {
  @Autowired @Getter
  private CompanyRepository companyRepo;
  
  @Autowired
  private CompanyInfoRepository companyInfoRepo;
  
  public Company getCompany(ClientInfo clientInfo, Long id) { 
    return companyRepo.getById(id); 
  }

  public List<Company> findAll(ClientInfo clientInfo) { 
    return companyRepo.findAll(); 
  }
  
  public List<Company> findByParentIds(ClientInfo client, List<Long> parentIds) {
    return companyRepo.findByParentIds(parentIds);
  }

  public Company getCompany(ClientInfo clientInfo, String code) { 
    return companyRepo.getByCode(clientInfo, code); 
  }

  List<Company> searchCompanies(ClientInfo client, SqlQueryParams params) { 
    String[] SEARCH_FIELDS = new String[] { "code", "label", "description" };
    SqlQuery query = 
        new SqlQuery().
        ADD_TABLE(new EntityTable(Company.class).selectAllFields()).
        FILTER(
             SearchFilter.isearch(Company.class, SEARCH_FIELDS)).
        FILTER(
             OptionFilter.storageState(Company.class)).
        ORDERBY(new String[] {"code", "modifiedTime"}, "modifiedTime", "DESC");
    query.mergeValue(params);
    return query(client, query, Company.class); 
  }
  
  public CompanyInfo getCompanyInfo(ClientInfo clientInfo, String code) {
    return companyInfoRepo.getByCode(code);
  }
}