package net.datatp.module.company.repository;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.repository.DataTPRepository;

@Repository
public interface CompanyRepository extends DataTPRepository<Company, Serializable> {
	Company getByCode(String code);
	
  @Query("SELECT c FROM Company c WHERE c.id = :id")
  Company getById(@Param("id") Long id);

  @Query("SELECT c FROM Company c WHERE c.parentId IN :parentIds")
  List<Company> findByParentIds(@Param("parentIds") List<Long> parentIds);
}
