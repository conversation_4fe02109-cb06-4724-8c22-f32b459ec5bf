package cloud.datatp.fforwarder.sales.booking.repository;

import cloud.datatp.fforwarder.sales.booking.entity.Booking;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import net.datatp.module.data.db.entity.StorageState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface BookingRepository extends JpaRepository<Booking, Serializable> {

  @Query("SELECT b FROM Booking b WHERE b.companyId = :companyId AND b.bookingCaseReference = :caseRef")
  Booking getByCaseRef(@Param("companyId") Long companyId, @Param("caseRef") String caseRef);

  @Query("SELECT b FROM Booking b WHERE b.bookingProcessId = :processId")
  Booking getByProcessId(@Param("processId") Long processId);

  @Modifying
  @Query("UPDATE Booking b SET b.storageState = :state WHERE b.id IN (:ids)")
  int setStorageState(@Param("state") StorageState state, @Param("ids") List<Long> ids);

  @Modifying
  @Query("UPDATE Booking b SET b.bookingProcessId = :processId WHERE b.id = :id")
  void linkBookingProcess(@Param("id") Long bookingId, @Param("processId") Long processId);

  @Query("SELECT b FROM Booking b WHERE b.bookingDate >= :startTime AND b.bookingDate < :endTime")
  List<Booking> findBookingsInTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}