package cloud.datatp.fforwarder.sales.booking;

import jakarta.annotation.PostConstruct;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.app.AppEnv;
import net.datatp.module.bot.BotEvent;
import net.datatp.module.bot.BotService;
import net.datatp.module.bot.cron.CronJob;
import net.datatp.module.bot.cron.CronJobFrequency;
import net.datatp.module.bot.cron.CronJobLogger;
import net.datatp.module.bot.task.TaskUnitBotEvent;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.common.DeviceInfo;
import net.datatp.module.common.DeviceType;
import net.datatp.module.company.CompanyReadLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.monitor.SourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Calendar;

@Component
public class BookingUpdateFeedbackCronJob extends CronJob {

  @Autowired
  private AppEnv appEnv;

  @Autowired
  private BotService botService;

  @Autowired
  private CompanyReadLogic companyLogic;

  protected BookingUpdateFeedbackCronJob() {
    super("booking:update:twice-daily", "Booking Update Feedback Price Twice Daily");
  }

  @PostConstruct
  public void onInit() {
    if (appEnv.isDevEnv()) {
      setFrequencies(CronJobFrequency.NONE);
    } else {
      setFrequencies(CronJobFrequency.EVERY_DAY_08_AM, CronJobFrequency.EVERY_DAY_12_PM);
    }
  }

  protected List<ICompany> getTargetCompanies() {
    List<ICompany> companies = new ArrayList<>();
    ICompany company = ICompany.SYSTEM;
    companies.add(company);
    return companies;
  }

  @Override
  protected ClientInfo getClientInfo(ICompany company) {
    ClientInfo client = new ClientInfo("default", "system", "localhost");
    if (appEnv.isProdEnv()) {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Server));
    } else {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Computer));
    }
    return client;
  }

  protected Set<String> getReportToUsers(ClientInfo client, ICompany company) {
    Set<String> userSet = super.getReportToUsers(client, company);
    userSet.add("dan");
    return userSet;
  }

  @Override
  protected void run(ClientInfo client, ICompany company, CronJobLogger logger) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    Calendar now = Calendar.getInstance();
    int hour = now.get(Calendar.HOUR_OF_DAY);
    Date startTime;
    Date endTime = now.getTime();

    Calendar startCal = Calendar.getInstance();

    if (hour <= 8) {
      startCal.add(Calendar.DAY_OF_MONTH, -1);
      startCal.set(Calendar.HOUR_OF_DAY, 14);
    } else {
      startCal.set(Calendar.HOUR_OF_DAY, 8);
    }
    startCal.set(Calendar.MINUTE, 0);
    startCal.set(Calendar.SECOND, 0);
    startCal.set(Calendar.MILLISECOND, 0);

    startTime = startCal.getTime();

    ExecutableContext ctx =
      new ExecutableContext(client, company)
        .withScriptEnv(scriptDir, BookingExecutor.class, BookingExecutor.UpdateFeedbackPrice.class)
        .withParam("startTime", startTime)
        .withParam("endTime", endTime);

    BotEvent<?> botEvent =
      new TaskUnitBotEvent(client, company, ctx)
        .withProcessMode(BotEvent.ProcessMode.Immediately)
        .withReportToUsers(getReportToUsers(client, company));

    botService.broadcast(SourceType.UserBot, botEvent);
  }
}