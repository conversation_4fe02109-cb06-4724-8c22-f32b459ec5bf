package cloud.datatp.fforwarder.sales.booking;

import cloud.datatp.bfsone.partner.BFSOnePartnerLogic;
import cloud.datatp.bfsone.partner.entity.BFSOnePartner;
import cloud.datatp.fforwarder.sales.booking.dto.BookingModel;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.common.CustomerChargeLogic;
import cloud.datatp.fforwarder.sales.common.entity.QuotationCharge;
import cloud.datatp.fforwarder.settings.TransportationMode;
import groovy.lang.Binding;
import lombok.extern.slf4j.Slf4j;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.ExecutableUnit;
import net.datatp.lib.executable.Executor;
import net.datatp.module.backend.Notification;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.data.db.*;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import org.springframework.context.ApplicationContext;
import org.springframework.data.repository.query.Param;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class BookingExecutor extends Executor {

  public static class UpdateFeedbackPrice extends ExecutableUnit {
    @Override
    public Notification execute(ApplicationContext appCtx, ExecutableContext ctx) {
      Notification notification = new Notification(Notification.Type.info, "Update Feedback Price", "");
      ClientInfo client = ctx.getClient();
      CompanyLogic companyLogic = appCtx.getBean(CompanyLogic.class);
      BookingLogic bookingLogic = appCtx.getBean(BookingLogic.class);
      CustomerChargeLogic customerChargeLogic = appCtx.getBean(CustomerChargeLogic.class);
      Date startTime = ctx.getParams().getDate("startTime", new Date());
      Date endTime = ctx.getParams().getDate("endTime", new Date());
      List<Booking> bookings = bookingLogic.findBookingsInTimeRange(client, startTime, endTime);
      int count = 0;
      for (Booking booking : bookings) {
        Company company = companyLogic.getCompany(client, booking.getCompanyId());
        if (booking.getSQuotationChargeId() == null) continue;
        QuotationCharge quotationCharge = customerChargeLogic.getQuotationChargeById(client, company, booking.getSQuotationChargeId());
        if (TransportationMode.isSeaTransport(quotationCharge.getMode())) {
          quotationCharge.setConfirm(true);
          customerChargeLogic.updateFeedbackPrice(client, company, quotationCharge.getId(), booking);
          count++;
        } else if (TransportationMode.isAirTransport(quotationCharge.getMode())) {
          quotationCharge.setConfirm(true);
          customerChargeLogic.updateFeedbackPrice(client, company, quotationCharge.getId(), booking);
          count++;
        }
      }
      return notification.withMessage("Updated " + count + " Feedback Price.");
    }
  }

  public BookingExecutor() {
    register(new UpdateFeedbackPrice());
  }
}