package cloud.datatp.fforwarder.sales.booking.dto;

import cloud.datatp.bfsone.partner.entity.BFSOnePartner;
import cloud.datatp.fforwarder.price.common.ChargeTarget;
import cloud.datatp.fforwarder.sales.booking.dto.SellingRate.Type;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.booking.entity.BookingAirTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingCustomClearance;
import cloud.datatp.fforwarder.sales.booking.entity.BookingSeaTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingTruckTransportCharge;
import cloud.datatp.fforwarder.sales.common.BookingShipmentInfo;
import cloud.datatp.fforwarder.sales.common.ContainerType;
import cloud.datatp.fforwarder.sales.common.ContainerType.ContainerTypeUnit;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAirTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerCustomClearanceAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerSeaTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerSeaTransportCharge.SeaType;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportCharge.TruckType;
import cloud.datatp.fforwarder.sales.common.entity.QuotationCharge;
import cloud.datatp.fforwarder.sales.inquiry.entity.Container;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.settings.FreightTerm;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.TransportationMode;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@NoArgsConstructor
@Getter @Setter
public class BookingModel {
  private Long    id;
  private String  requestReference;
  private String  referenceNo;

  private String  bookingNo;

  private Long sQuotationChargeId;

  /* ------------ Agent/ Carrier/ Pricing Input Rate -------------- */
  private Long    clientPartnerId;
  private String  clientLabel;
  private Long    receiverAccountId;
  private String  receiverEmployeeLabel;
  private Long    senderAccountId;
  private String  senderLabel;

  /* ------------ Port/ Airport/ Location -------------- */
  private String  fromLocationCode;
  private String  fromLocationLabel;
  private String  toLocationCode;
  private String  toLocationLabel;
  private String  cargoPickupAt;
  private String  cargoDeliveryAt;

  private String  shipmentType;
  @Enumerated(EnumType.STRING)
  private FreightTerm paymentTerm;

  private int packages;
  private String unitOfPackage;

  private double grossWeight;
  private double volume;

  private String commodity;
  private String descriptionOfGoods;

  private SpecificServiceInquiry inquiry;
  private BookingShipmentInfo shipmentInfo;

  @Deprecated
  private QuotationCharge airQuote;

  private QuotationCharge seaQuote;

  private List<SellingRate> sellingRates = new ArrayList<>();

  public BookingModel(Booking booking) {
    this.inquiry = booking.getInquiry();
    this.requestReference = this.inquiry.getReferenceCode();
    this.referenceNo = booking.getExternalCaseReference();
    this.sQuotationChargeId = booking.getSQuotationChargeId();

    this.receiverAccountId = booking.getReceiverAccountId();
    this.receiverEmployeeLabel = booking.getReceiverLabel();

    this.id = booking.getId();
    this.bookingNo = booking.getBookingNumber();
    this.shipmentType = booking.getShipmentType();
    this.paymentTerm = booking.getPaymentTerm();
    this.clientPartnerId = inquiry.getClientPartnerId();
    this.clientLabel = inquiry.getClientLabel();

    this.senderAccountId = inquiry.getSalemanAccountId();
    this.senderLabel = inquiry.getSalemanLabel();

    this.fromLocationCode = inquiry.getFromLocationCode();
    this.fromLocationLabel = inquiry.getFromLocationLabel();
    this.toLocationCode = inquiry.getToLocationCode();
    this.toLocationLabel = inquiry.getToLocationLabel();

    this.cargoPickupAt = inquiry.getPickupAddress();
    this.cargoDeliveryAt = inquiry.getDeliveryAddress();
    this.unitOfPackage = inquiry.getContainerTypes();
    this.grossWeight = inquiry.getGrossWeightKg();
    this.volume = inquiry.getVolumeCbm();
    this.commodity = inquiry.getCommodity();
    this.descriptionOfGoods = inquiry.getDescOfGoods();
    this.packages = inquiry.getPackageQty();

    BookingSeaTransportCharge seaCharge = booking.getCustomerSeaTransportCharge();
    BookingAirTransportCharge airCharge = booking.getCustomerAirTransportCharge();

    if (seaCharge != null) {
      shipmentInfo = seaCharge.getShipmentInfo();
      this.sellingRates = SellingRate.sellingRateCreator(inquiry.getContainers(), seaCharge);
    } else if (airCharge != null) {
      shipmentInfo = airCharge.getShipmentInfo();
      this.sellingRates = SellingRate.sellingRateCreator(airCharge);
    }

    List<BookingTruckTransportCharge> truckCharges = booking.getCustomerTruckTransportCharges();
    List<CustomerTruckTransportAdditionalCharge> truckAddCharges = truckCharges.stream()
        .flatMap(sel -> sel.getAdditionalCharges().stream())
        .collect(Collectors.toList());
    List<SellingRate> truckSellingFees = SellingRate.sellingRateCreator(inquiry.getMode(), truckAddCharges);
    this.sellingRates.addAll(truckSellingFees);

    List<BookingCustomClearance> customClearances = booking.getCustomerCustomClearances();
    List<CustomerCustomClearanceAdditionalCharge> customAddCharges = customClearances.stream()
        .flatMap(sel -> sel.getAdditionalCharges().stream())
        .collect(Collectors.toList());
    List<SellingRate> customSellingFees = SellingRate.sellingRateCreator(inquiry.getMode(), customAddCharges);
    this.sellingRates.addAll(customSellingFees);

  }

  public Booking toBooking(Booking template) {
    template = Objects.ensureNotNull(template, Booking::new);
    SpecificServiceInquiry inquiry = template.getInquiry();

    if (inquiry == null) inquiry = this.inquiry;
    inquiry.setReferenceCode(requestReference);
    inquiry.setClientLabel(clientLabel);
    inquiry.setClientPartnerId(clientPartnerId);
    inquiry.setSalemanAccountId(senderAccountId);
    inquiry.setSalemanLabel(senderLabel);

    inquiry.setFromLocationCode(fromLocationCode);
    inquiry.setFromLocationLabel(fromLocationLabel);
    inquiry.setToLocationCode(toLocationCode);
    inquiry.setToLocationLabel(toLocationLabel);
    inquiry.setContainerTypes(unitOfPackage);
    inquiry.setGrossWeightKg(grossWeight);
    inquiry.setVolumeCbm(volume);
    inquiry.setDescOfGoods(descriptionOfGoods);
    inquiry.setCommodity(commodity);
    inquiry.setPackageQty(packages);
    inquiry.setPickupAddress(cargoPickupAt);
    inquiry.setDeliveryAddress(cargoDeliveryAt);

    template.setInquiry(inquiry);
    if (template.isNew()) {
      template.setBookingNumber(bookingNo);
      template.setBookingDate(new Date());
    }


    template.setReceiverAccountId(receiverAccountId);
    template.setReceiverLabel(receiverEmployeeLabel);

    template.setPaymentTerm(paymentTerm);
    template.setShipmentType(shipmentType);
    if (shipmentInfo != null) {
      template.setHawbNo(shipmentInfo.getPlanHBCode());
      template.setMawbNo(shipmentInfo.getPlanMBCode());
    }

    if(sQuotationChargeId != null) {
      template.setSQuotationChargeId(sQuotationChargeId);
    }else if(seaQuote != null) {
      template.setSQuotationChargeId(seaQuote.getId());
    } else if(airQuote != null) {
      template.setSQuotationChargeId(airQuote.getId());
    }

    TransportationMode mode = inquiry.getMode();

    Map<TransportationMode, List<SellingRate>> sellingRateByGroup = sellingRates.stream()
        .collect(Collectors.groupingBy(SellingRate::getGroup));

    List<SellingRate> freightRates = sellingRateByGroup.getOrDefault(mode, new ArrayList<>());
    Map<Type, List<SellingRate>> sellingGroup = freightRates.stream().collect(Collectors.groupingBy(SellingRate::getType));

    List<CustomerAdditionalCharge> addCharges = createAddCharges(sellingGroup.getOrDefault(Type.LOCAL_CHARGE, new ArrayList<>()));

    if (TransportationMode.isSeaTransport(mode)) {

      BookingSeaTransportCharge charge = computeSeaCharge(template);

      List<SellingRate> oceanFreights = sellingGroup.getOrDefault(Type.SEAFREIGHT, new ArrayList<>());
      if (!oceanFreights.isEmpty()) {
        if (TransportationMode.isSeaFCLTransport(mode)) {
          SellingRate.convertToOceanFreightFCL(charge, oceanFreights);
        } else {
          Objects.assertNotNull(freightRates.size() == 1, "Only one rate is allowed for LCL");
          SellingRate.convertToOceanFreightLCL(charge, oceanFreights.get(0));
        }
      }

      List<CustomerSeaTransportAdditionalCharge> mappedAddCharges = addCharges.stream().map(CustomerSeaTransportAdditionalCharge::new).collect(Collectors.toList());
      charge.setAdditionalCharges(mappedAddCharges);
      template.setCustomerSeaTransportCharge(charge);
    } else if (TransportationMode.isAirTransport(mode)) {
      BookingAirTransportCharge charge = computeAirCharge(template);
      List<SellingRate> rates = sellingGroup.getOrDefault(Type.AIRFREIGHT, new ArrayList<>());
      if (!rates.isEmpty()) {
        Objects.assertNotNull(rates.size() == 1, "Only one rate is allowed for Air Freight");
        SellingRate.convertToAirFreight(charge, rates.get(0));
      }
      List<CustomerAirTransportAdditionalCharge> mappedAddCharges = addCharges.stream().map(CustomerAirTransportAdditionalCharge::new).collect(Collectors.toList());
      charge.setAdditionalCharges(mappedAddCharges);
      template.setCustomerAirTransportCharge(charge);
    } else {
      throw RuntimeError.UnknownError("Not support yet!!!");
    }

    List<SellingRate> truckingRates = sellingGroup.getOrDefault(Type.TRUCKING, new ArrayList<>());

    List<BookingTruckTransportCharge> truckCharges = new ArrayList<>();
    if (Collections.isNotEmpty(truckingRates)) {
      Map<ChargeTarget, List<SellingRate>> collect = truckingRates.stream().collect(Collectors.groupingBy(SellingRate::getTarget));
      for (ChargeTarget target : collect.keySet()) {
        List<SellingRate> sellingRates = collect.get(target);
        BookingTruckTransportCharge truckCharge = new BookingTruckTransportCharge(target, template);
        for (SellingRate rate : sellingRates) {
          String unit = rate.getUnit();
          ContainerType containerType = ContainerTypeUnit.match(unit);

          CustomerTruckTransportAdditionalCharge addCharge = new CustomerTruckTransportAdditionalCharge();
          addCharge.setLabel(rate.getName());
          addCharge.setName(rate.getCode());
          addCharge.setCurrency(rate.getCurrency());
          addCharge.setUnitPrice(rate.getUnitPrice());
          addCharge.setNote(rate.getNote());
          addCharge.setExchangeRate(rate.getExchangeRate());
          if (containerType != null) {
            truckCharge.setTruckType(TruckType.CONTAINER);
            addCharge.setUnit(containerType.getLabel());
          } else {
            truckCharge.setTruckType(TruckType.REGULAR);
            addCharge.setUnit(unit);
          }
          addCharge.setTaxRate(rate.getTaxRate());
          addCharge.setFinalCharge(rate.getTotalAmount());
          addCharge.setFinalCharge(rate.getTotalAmount());
          addCharge.setDomesticCurrency(rate.getDomesticCurrency());
          addCharge.setDomesticFinalCharge(rate.getDomesticTotalAmount());
          truckCharge.withAdditionalCharge(addCharge);

        }

        truckCharges.add(truckCharge);
      }
    }
    template.setCustomerTruckTransportCharges(truckCharges);

    List<SellingRate> customRates = sellingGroup.getOrDefault(Type.CUSTOM, new ArrayList<>());
    List<BookingCustomClearance> customCharges = new ArrayList<>();
    if (Collections.isNotEmpty(customCharges)) {
      Map<ChargeTarget, List<SellingRate>> collect = customRates.stream().collect(Collectors.groupingBy(SellingRate::getTarget));
      for (ChargeTarget target : collect.keySet()) {
        List<SellingRate> sellingRates = collect.get(target);
        BookingCustomClearance customClearance = new BookingCustomClearance(target, template);
        for (SellingRate rate : sellingRates) {
          String unit = rate.getUnit();
          ContainerType containerType = ContainerTypeUnit.match(unit);
          CustomerCustomClearanceAdditionalCharge addCharge = new CustomerCustomClearanceAdditionalCharge();
          addCharge.setLabel(rate.getName());
          addCharge.setName(rate.getCode());
          addCharge.setCurrency(rate.getCurrency());
          addCharge.setUnitPrice(rate.getUnitPrice());
          addCharge.setNote(rate.getNote());
          addCharge.setTaxRate(rate.getTaxRate());
          addCharge.setFinalCharge(rate.getTotalAmount());
          addCharge.setDomesticCurrency(rate.getDomesticCurrency());
          addCharge.setDomesticFinalCharge(rate.getDomesticTotalAmount());

          double total = rate.getQuantity() * rate.getUnitPrice();
          double totalTax = total * rate.getTaxRate();
          addCharge.setTotal(total);
          addCharge.setTotalTax(totalTax);
          addCharge.setFinalCharge(total + totalTax);

          if (containerType != null) {
            addCharge.setUnit(containerType.getLabel());
          } else {
            addCharge.setUnit(unit);
          }
          customClearance.withAdditionalCharge(addCharge);
        }
        customCharges.add(customClearance);
      }
    }
    template.setCustomerCustomClearances(customCharges);
    return template;
  }

  private BookingAirTransportCharge computeAirCharge(Booking template) {
    BookingAirTransportCharge charge = template.getCustomerAirTransportCharge();
    charge = Objects.ensureNotNull(charge, BookingAirTransportCharge::new);
    charge.setShipmentInfo(shipmentInfo);
    charge.setFromLocationCode(fromLocationCode);
    charge.setFromLocationLabel(fromLocationLabel);
    charge.setToLocationCode(toLocationCode);
    charge.setToLocationLabel(toLocationLabel);
    charge.setPayerPartnerId(clientPartnerId);
    charge.setPayerFullName(clientLabel);

    if (airQuote != null) {
      charge.setCarrierLabel(airQuote.getCarrierLabel());
      charge.setChargeableWeightInKG(grossWeight);
      charge.setPurpose(airQuote.getPurpose());
      charge.setRefCurrency(airQuote.getRefCurrency());
      charge.setCurrency(airQuote.getCurrency());
      charge.setNote(airQuote.getNote());
      charge.setValidFrom(new Date());
      charge.setValidTo(airQuote.getValidity());
    }

    return charge;
  }

  private BookingSeaTransportCharge computeSeaCharge(Booking template) {
    BookingSeaTransportCharge charge = template.getCustomerSeaTransportCharge();
    if (charge == null) {
      TransportationMode mode = inquiry.getMode();
      charge = new BookingSeaTransportCharge();
      if (TransportationMode.isSeaFCLTransport(mode)) charge.setType(SeaType.FCL);
      else charge.setType(SeaType.LCL);
    }
    charge.setShipmentInfo(shipmentInfo);
    charge.setFromLocationCode(fromLocationCode);
    charge.setFromLocationLabel(fromLocationLabel);
    charge.setToLocationCode(toLocationCode);
    charge.setToLocationLabel(toLocationLabel);
    charge.setPayerPartnerId(clientPartnerId);
    charge.setPayerFullName(clientLabel);

    if (seaQuote != null) {
      charge.setCarrierLabel(seaQuote.getCarrierLabel());
      charge.setChargeableVolumeInCBM(volume);
      charge.setPurpose(inquiry.getPurpose());
      charge.setRefCurrency(seaQuote.getRefCurrency());
      charge.setCurrency(seaQuote.getCurrency());
      charge.setNote(seaQuote.getNote());
      charge.setValidFrom(new Date());
      charge.setValidTo(seaQuote.getValidity());
    }

    return charge;
  }

  public List<CustomerAdditionalCharge> createAddCharges(List<SellingRate> sellingRates) {
    List<CustomerAdditionalCharge> holder = new ArrayList<>();
    for (SellingRate sel : sellingRates) {
      CustomerAdditionalCharge addCharge = new CustomerAdditionalCharge();
      addCharge.setPayerFullName(sel.getPayerPartnerLabel());
      addCharge.setPayerPartnerId(sel.getPayerPartnerId());
      addCharge.setName(sel.getCode());
      addCharge.setLabel(sel.getName());
      addCharge.setQuantity(sel.getQuantity());
      addCharge.setUnitPrice(sel.getUnitPrice());
      addCharge.setUnit(sel.getUnit());
      addCharge.setCurrency(sel.getCurrency());
      addCharge.setTaxRate(sel.getTaxRate());
      addCharge.setNote(sel.getNote());
      addCharge.setExchangeRate(sel.getExchangeRate());

      double total = sel.getQuantity() * sel.getUnitPrice();
      addCharge.setTotal(total);
      addCharge.setTotalTax(total * sel.getTaxRate());
      addCharge.setFinalCharge(sel.getTotalAmount());
      addCharge.setDomesticFinalCharge(sel.getDomesticTotalAmount());
      holder.add(addCharge);
    }
    return holder;
  }

  public MapObject toBFSOneIBooking(BFSOnePartner customer) {
    Objects.assertNotNull(customer, "Customer is not found when create Internal Booking!");
    MapObject ibooking = new MapObject();
    if (StringUtil.isEmpty(referenceNo)) ibooking.put("CreatedDate", DateUtil.asBFSOneFormat(new Date()));
    ibooking.set("ServiceType", computeServiceType());
    ibooking.set("BkgID", referenceNo);
    ibooking.set("ReceiveUserID", "");
    ibooking.set("SendUserID", "");
    ibooking.set("CustomerID", customer.getBfsonePartnerCode());
    ibooking.set("ColoaderID", "");
    ibooking.set("AgentID", "");
    ibooking.set("ShipperName", shipmentInfo.getShipperLabel());
    ibooking.set("ConsigneeID", "");
    ibooking.set("ConsigneeName", shipmentInfo.getConsigneeLabel());

    if (StringUtil.isEmpty(shipmentInfo.getConsigneeLabel())) {
      ibooking.set("ConsigneeName", shipmentInfo.getShipperLabel());
    }

    ibooking.set("ShipmentType", shipmentType);
    ibooking.set("POLCode", fromLocationCode);
    ibooking.set("POLName", fromLocationLabel);
    ibooking.set("PODCode", toLocationCode);
    ibooking.set("PODName", toLocationLabel);
    ibooking.set("DeliveryPlace", toLocationLabel);
    ibooking.set("ETA", DateUtil.asBFSOneFormat(shipmentInfo.getPlanTimeArrival()));
    ibooking.set("ETD", DateUtil.asBFSOneFormat(shipmentInfo.getPlanTimeDeparture()));
    ibooking.set("Flight_Vessel", shipmentInfo.getTransportMethod());
    ibooking.set("Voyage", shipmentInfo.getTransportNo());
    ibooking.set("Flight_Vessel_Date", DateUtil.asBFSOneFormat(shipmentInfo.getPlanTimeArrival()));
    ibooking.set("Commodity", commodity);
    ibooking.set("DescriptionOfGoods", descriptionOfGoods);
    ibooking.set("GW", grossWeight);
    ibooking.set("Packages", 0);
    if (TransportationMode.isSeaFCLTransport(inquiry.getMode())) {
      ibooking.set("UnitOfPackage", unitOfPackage);
    } else {
      ibooking.set("UnitOfPackage", unitOfPackage);
    }
    ibooking.set("CBM", volume);
    ibooking.set("CargoPickupAt", cargoPickupAt);
    ibooking.set("CargoDeliveryAt", cargoDeliveryAt);
    ibooking.set("MAWBNO", shipmentInfo.getPlanMBCode());
    ibooking.set("HAWBNO", shipmentInfo.getPlanHBCode());
    ibooking.set("BookingNo", bookingNo);

    // container list
    List<MapObject> containers = new ArrayList<>();

    for (Container container : inquiry.getContainers()) {
      MapObject rec = new MapObject();
      ContainerType containerType = ContainerTypeUnit.match(container.getContainerType());
      if (containerType != null) {
        rec.set("ContainerType", containerType.getLabel());
      } else {
        rec.set("ContainerType", container.getContainerType());
      }
      rec.set("Quantity", container.getQuantity());
      rec.set("ContainerNo", "");
      rec.set("ContainerSeal", "");
      containers.add(rec);
    }
    ibooking.set("Containers", containers);

    //List<MapObject> collected = this.sellingRates.stream().map(SellingRate::toBFSOneFee).collect(Collectors.toList());
    List<MapObject> collected = new ArrayList<>();
    for (SellingRate rate : this.sellingRates) {
      MapObject rec = rate.toBFSOneFee(customer);
      collected.add(rec);
    }
    ibooking.set("SellingRates", collected);
    return ibooking;
  }

  private String computeServiceType() {
    TransportationMode mode = inquiry.getMode();
    Purpose purpose = inquiry.getPurpose();

    String serviceType;
    if (TransportationMode.isSeaTransport(mode)) {
      serviceType = purpose == Purpose.EXPORT ? "SeaExpTransactions" : "SeaImpTransactions";
      if (TransportationMode.isSeaFCLTransport(mode)) {
        serviceType += "_FCL";
      } else if (TransportationMode.isSeaLCLTransport(mode)) {
        serviceType += "_LCL";
      }
    } else if (TransportationMode.isAirTransport(mode)) {
      serviceType = purpose == Purpose.EXPORT ? "AirExpTransactions" : "AirImpTransactions";
    } else if (TransportationMode.isTruckTransport(mode)) {
      serviceType = "InlandTrucking";
    } else {
      serviceType = "CustomsLogistics";
    }

    if (StringUtil.isEmpty(serviceType)) {
      throw new RuntimeError(ErrorType.IllegalArgument, "Invalid transportation mode: " + mode);
    }
    return serviceType;
  }

}