package cloud.datatp.fforwarder.sales.booking.entity;

import cloud.datatp.fforwarder.sales.common.entity.CustomerChargeModel;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.settings.FreightTerm;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyFollower.Type;
import net.datatp.module.company.entity.ShareableCompanyEntity;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.module.hr.entity.Employee;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

@Entity
@Table(
    name = Booking.TABLE_NAME,
    uniqueConstraints = {
        @UniqueConstraint(
            name = Booking.TABLE_NAME + "_booking_case_reference",
            columnNames = {"company_id", "booking_case_reference"})
    },
    indexes = {
        @Index(name = Booking.TABLE_NAME + "_company_id", columnList = "company_id"),
        @Index(name = Booking.TABLE_NAME + "_storage_state", columnList = "storage_state"),
    }
)
@DeleteGraphs({
    @DeleteGraph(target = BookingFollower.class, joinField = "booking_id", joinType = DeleteGraphJoinType.OneToMany),
    @DeleteGraph(target = BookingCustomClearance.class, joinField = "booking_id", joinType = DeleteGraphJoinType.OneToMany),
    @DeleteGraph(target = BookingTruckTransportCharge.class, joinField = "booking_id", joinType = DeleteGraphJoinType.OneToMany),
    @DeleteGraph(target = SpecificServiceInquiry.class, joinField = "inquiry_id"),
    @DeleteGraph(target = CustomerChargeModel.class, joinField = "customer_charge_model_id"),
    @DeleteGraph(target = BookingSeaTransportCharge.class, joinField = "booking_sea_charge_id"),
    @DeleteGraph(target = BookingAirTransportCharge.class, joinField = "booking_air_charge_id"),
    @DeleteGraph(target = BookingRailTransportCharge.class, joinField = "booking_rail_charge_id"),
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class Booking extends ShareableCompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_sales_booking";

  public static final String HPH_SEQUENCE = "logistics-sales:booking-hph";
  public static final String HAN_SEQUENCE = "logistics-sales:booking-han";
  public static final String HCM_SEQUENCE = "logistics-sales:booking-hcm";
  public static final String DAD_SEQUENCE = "logistics-sales:booking-dad";
  public static final String CORP_SEQUENCE = "logistics-sales:booking-dad";

  private static final Map<String, String> sequenceMap = Map.of(
      "beehph", HPH_SEQUENCE,
      "beehan", HAN_SEQUENCE,
      "beehcm", HCM_SEQUENCE,
      "beedad", DAD_SEQUENCE,
      "bee", CORP_SEQUENCE
  );

  @Column(name = "booking_number")
  private String bookingNumber;

  @Column(name = "booking_case_reference")
  private String bookingCaseReference;

  //For BFSOne Booking reference
  @Column(name = "external_case_reference")
  private String externalCaseReference;

  @Column(name = "hawb_no")
  private String hawbNo;

  @Column(name = "mawb_no")
  private String mawbNo;

  @Column(name = "booking_process_id", unique = true)
  private Long bookingProcessId;

  @Column(name = "shipment_type")
  private String shipmentType;

  @Column(name = "receiver_account_id")
  private Long receiverAccountId;

  @Column(name = "receiver_label")
  private String receiverLabel;

  @Enumerated(EnumType.STRING)
  @Column(name = "payment_term")
  protected FreightTerm paymentTerm = FreightTerm.PREPAID;

  @Column(name = "shipment_total_charge")
  private double shipmentTotalCharge;

  @Column(name = "shipment_total_tax")
  private double shipmentTotalTax;

  @Column(name = "shipment_final_additional_charge")
  private double shipmentFinalAdditionalCharge;

  @Column(name = "shipment_final_commission")
  private double shipmentFinalCommission;

  @Column(name = "shipment_final_charge")
  private double shipmentFinalCharge;

  @Column(name = "shipment_domestic_final_charge")
  private double shipmentDomesticFinalCharge;

  @Column(name = "domestic_final_charge")
  private double domesticFinalCharge;

  @Column(name = "domestic_currency")
  private String domesticCurrency;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "booking_date")
  private Date bookingDate;

  @Column(name = "specific_quotation_charge_id")
  private Long sQuotationChargeId;

  @OneToOne(optional = false, cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "customer_charge_model_id", referencedColumnName = "id")
  private CustomerChargeModel customerChargeModel;

  @OneToOne(optional = false, cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "inquiry_id", referencedColumnName = "id")
  private SpecificServiceInquiry inquiry;

  @Setter(AccessLevel.NONE)
  @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "booking_sea_charge_id", referencedColumnName = "id")
  private BookingSeaTransportCharge customerSeaTransportCharge;

  @Setter(AccessLevel.NONE)
  @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "booking_air_charge_id", referencedColumnName = "id")
  private BookingAirTransportCharge customerAirTransportCharge;

  @Setter(AccessLevel.NONE)
  @Getter(AccessLevel.NONE)
  @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "booking_rail_charge_id", referencedColumnName = "id")
  private BookingRailTransportCharge customerRailTransportCharge;

  @Setter(AccessLevel.NONE)
  @Getter(AccessLevel.NONE)
  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "booking_id", referencedColumnName = "id")
  private List<BookingTruckTransportCharge> customerTruckTransportCharges;

  @Setter(AccessLevel.NONE)
  @Getter(AccessLevel.NONE)
  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "booking_id", referencedColumnName = "id")
  private List<BookingCustomClearance> customerCustomClearances;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "booking_id", referencedColumnName = "id")
  private Set<BookingFollower> followers = new HashSet<>();

  public static String getSequence(String companyCode) {
    return sequenceMap.getOrDefault(companyCode, HPH_SEQUENCE);
  }

  public void setCustomerSeaTransportCharge(BookingSeaTransportCharge customSeaCharge) {
    if (customSeaCharge != null && customSeaCharge.getCode() != null) {
      customerAirTransportCharge = null;
      customerRailTransportCharge = null;
    }
    this.customerSeaTransportCharge = customSeaCharge;
  }

  public void setCustomerAirTransportCharge(BookingAirTransportCharge customerAirTransportCharge) {
    if (customerAirTransportCharge != null && customerAirTransportCharge.getCode() != null) {
      this.customerSeaTransportCharge = null;
      this.customerRailTransportCharge = null;
    }
    this.customerAirTransportCharge = customerAirTransportCharge;
  }

  public void setCustomerRailTransportCharge(BookingRailTransportCharge customerRailTransportCharge) {
    if (customerRailTransportCharge != null && customerRailTransportCharge.getCode() != null) {
      this.customerSeaTransportCharge = null;
      this.customerAirTransportCharge = null;
    }
    this.customerRailTransportCharge = customerRailTransportCharge;
  }

  public BookingRailTransportCharge getCustomerRailTransportCharge() {
    if (customerRailTransportCharge == null || customerRailTransportCharge.getCode() == null) return null;
    return customerRailTransportCharge;
  }

  public void setCustomerTruckTransportCharges(List<BookingTruckTransportCharge> charges) {
    if (Objects.isNull(customerTruckTransportCharges)) customerTruckTransportCharges = new ArrayList<>();
    this.customerTruckTransportCharges.clear();
    if (charges != null) this.customerTruckTransportCharges.addAll(charges);
  }

  public void setCustomerCustomClearances(List<BookingCustomClearance> clearances) {
    if (Objects.isNull(customerCustomClearances)) customerCustomClearances = new ArrayList<>();
    this.customerCustomClearances.clear();
    if (clearances != null) this.customerCustomClearances.addAll(clearances);
  }

  public List<BookingTruckTransportCharge> getCustomerTruckTransportCharges() {
    if (Objects.isNull(customerTruckTransportCharges)) customerTruckTransportCharges = new ArrayList<>();
    return customerTruckTransportCharges;
  }

  public List<BookingCustomClearance> getCustomerCustomClearances() {
    if (Objects.isNull(customerCustomClearances)) customerCustomClearances = new ArrayList<>();
    return customerCustomClearances;
  }

  public Booking withFollower(Employee employee) {
    BookingFollower follower = new BookingFollower(Type.Employee, employee.getId(), employee.getLabel());
    followers = Arrays.addToSet(followers, follower);
    return this;
  }

  public Booking removeCaseReference() {
    this.bookingCaseReference = null;
    return this;
  }

  public Booking withCaseReference(String ref) {
    this.bookingCaseReference = ref;
    return this;
  }

  public void set(ClientInfo client, Company company) {
    super.set(client, company);
    set(client, company.getId());
    inquiry.set(client, company);
    if (customerChargeModel != null) {
      customerChargeModel.set(client, company);
    }
    if (customerAirTransportCharge != null) {
      customerAirTransportCharge.set(client, company);
    }
    if (customerRailTransportCharge != null) {
      customerRailTransportCharge.set(client, company);
    }
    if (customerSeaTransportCharge != null) {
      customerSeaTransportCharge.set(client, company);
    }
    set(client, company, customerTruckTransportCharges);
    set(client, company, customerCustomClearances);
    set(client, company, followers);
  }

  public void withParticipant(BookingFollower participant) {
    followers = Arrays.addToSet(followers, participant);
  }

}