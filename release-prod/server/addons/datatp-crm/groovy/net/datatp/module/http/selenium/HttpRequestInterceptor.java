package net.datatp.module.http.selenium;

import java.io.UncheckedIOException;

import org.openqa.selenium.remote.http.Filter;
import org.openqa.selenium.remote.http.HttpHandler;
import org.openqa.selenium.remote.http.HttpRequest;
import org.openqa.selenium.remote.http.HttpResponse;

public class HttpRequestInterceptor implements Filter {

  @Override
  public HttpHandler apply(HttpHandler t) {
    return new WrapHttpHandler(t);
  }
  
  protected HttpResponse execute(HttpHandler handler, HttpRequest req) {
    System.out.println("WrapHandler: " + req.getUri());
    return handler.execute(req);
  }

  class WrapHttpHandler implements HttpHandler {
    private HttpHandler handler ;
    
    public WrapHttpHandler(HttpHandler handler) {
      this.handler = handler;
    }
    
    @Override
    public HttpResponse execute(HttpRequest req) throws UncheckedIOException {
      return HttpRequestInterceptor.this.execute(handler, req);
    }
    
  }
}
