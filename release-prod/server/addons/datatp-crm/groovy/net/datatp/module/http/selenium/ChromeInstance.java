package net.datatp.module.http.selenium;

import java.time.Duration;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.SearchContext;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.devtools.NetworkInterceptor;
import org.openqa.selenium.support.events.EventFiringDecorator;
import org.openqa.selenium.support.events.WebDriverListener;

import lombok.Getter;
import net.datatp.util.error.RuntimeError;

public class ChromeInstance {
  private ChromeOptions options = new ChromeOptions();
  @Getter 
  private ChromeDriver  chromeDriver;
  @Getter
  private WebDriver     webDriver;
  
  private HttpRequestInterceptor requestInterceptor;
  private NetworkInterceptor     networkInterceptor;
  
  public ChromeInstance init() {
    chromeDriver = new ChromeDriver(options);
    webDriver    = chromeDriver;
    return this;
  }
  
  public ChromeInstance decorate(WebDriverListener listener) {
    EventFiringDecorator<ChromeDriver> eventRecorder = new EventFiringDecorator<ChromeDriver>(listener);
    webDriver = eventRecorder.decorate(chromeDriver);
    return this;
  }
  
  
  public <T extends HttpRequestInterceptor>  T getRequestInterceptor() {
    return (T) requestInterceptor;
  }
  
  public <T extends HttpRequestInterceptor> ChromeInstance withInterceptor(T interceptor) {
    if(networkInterceptor != null) {
      networkInterceptor.close();
      networkInterceptor = null;
    }
    requestInterceptor = interceptor;
    networkInterceptor = new NetworkInterceptor(getChromeDriver(), interceptor);
    
    return this;
  }
  
  public ChromeInstance withDataDir(String dir) {
    options.addArguments("user-data-dir=" + dir);
    return this;
  }
  
  public ChromeInstance withHeadless() {
    options.addArguments("--headless=new");
    return this;
  }
  
  public ChromeInstance implicitlyWait(long period) {
    chromeDriver.manage().timeouts().implicitlyWait(Duration.ofMillis(period));
    return this;
  }
  
  public ChromeInstance quit() {
    if(networkInterceptor != null) {
      networkInterceptor.close();
    }
    chromeDriver.quit();
    return this;
  }
  
  public ChromeInstance get(String url) {
    webDriver.get(url);
    return this;
  }
  
  public ChromeInstance inputByName(String name, String value) {
    WebElement input = webDriver.findElement(By.name(name));
    input.sendKeys(value);
    return this;
  }
  
  public ChromeInstance clickButtonWithId(String id) {
    WebElement button = webDriver.findElement(By.id(id));
    button.click();
    return this;
  }
  
  public ChromeInstance clickButtonWithText(String text) {
    String exp = "//button[contains(.,'" + text + "')]";
    WebElement button = webDriver.findElement(By.xpath(exp));
    if(button != null) {
      button.click();
    }
    return this;
  }
  
  public ChromeInstance clickButtonWithAriaLabel(String text) {
    String exp = "//button[contains(@aria-label,'" + text + "')]";
    List<WebElement> buttons = webDriver.findElements(By.xpath(exp));
    if(buttons.size() == 1) {
      buttons.get(0).click();
    } else {
      throw RuntimeError.IllegalState("Expect 1 button with text {0}, but found {1}", text, buttons.size());
    }
    return this;
  }
  
  public WebElement elementWithText(String text) {
    String exp = "//*[contains(.,'" + text + "')]";
    List<WebElement> elements = webDriver.findElements((By.xpath(exp)));
    if(elements.size() == 0) return null;
    if(elements.size() == 1) return elements.get(0);
    throw RuntimeError.IllegalState("Expect 0 or 1 elements, found {0}", elements.size());
  }
  
  public WebElement elementWithXPath(String xpath) {
    return  webDriver.findElement((By.xpath(xpath)));
  }
  
  public List<WebElement> elementsWithXPath(String xpath) {
    return  webDriver.findElements((By.xpath(xpath)));
  }
  
  public List<WebElement> elementsWithText(String text) {
    String exp = "//*[contains(.,'" + text + "')]";
    List<WebElement> elements = webDriver.findElements((By.xpath(exp)));
    return elements;
  }
  
  public SearchContext findShadowRoot(String xpath) {
    WebElement shadowHost = webDriver.findElement(By.xpath(xpath));
    if(shadowHost == null) return null;
    return shadowHost.getShadowRoot();
  }
  
  public void test() {
    SearchContext shadowRoot = findShadowRoot("//mc-c-table");
    System.out.println("ShadowRoot: ");
    System.out.println(shadowRoot);
    if(shadowRoot != null) {
      System.out.println("---------------------------------------------------------------");
      List<WebElement> prominents = shadowRoot.findElements(By.cssSelector("div.prominent"));
      for(WebElement prominent : prominents) {
        String text = prominent.getText();
        System.out.println("TEXT: " + prominent.getText());
        if("BEE LOGISTICS CORPORATION - HAI PHONG BRANCH".equals(text.trim())) {
          prominent.click();
        }
      }
    }
  }
  
  public ChromeInstance expectText(String text) {
    String exp = "//*[text() = \"" + text + "\"]";
    List<WebElement> elements = webDriver.findElements((By.xpath(exp)));
    if(elements == null || elements.size() == 0) {
      throw RuntimeError.EntityNotFoundError("Text \"{0}\" is not found", text);
    }
    return this;
  }
  
  public ChromeInstance expectElement(String exp) {
    List<WebElement> elements = webDriver.findElements((By.xpath(exp)));
    if(elements == null || elements.size() == 0) {
      throw RuntimeError.EntityNotFoundError("Exp \"{0}\" is not found", exp);
    }
    return this;
  }
  
  public ChromeInstance expectLinkWithText(String text) {
    List<WebElement> elements = webDriver.findElements((By.linkText(text)));
    if(elements == null || elements.size() == 0) {
      throw RuntimeError.EntityNotFoundError("Link with text \"{0}\" is not found", text);
    }
    return this;
  }
}
