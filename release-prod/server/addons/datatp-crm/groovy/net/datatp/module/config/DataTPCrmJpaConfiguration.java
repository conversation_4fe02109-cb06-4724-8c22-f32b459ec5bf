package net.datatp.module.config;

import java.util.HashMap;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.data.db.ds.DatasourceConfiguration;
import net.datatp.util.dataformat.DataSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

@Slf4j
//@Configuration
public class DataTPCrmJpaConfiguration {

  //@Bean("crmEntityManagerFactory")
  public LocalContainerEntityManagerFactoryBean crmEntityManagerFactory( // TODO: Dan - replace PRIMARY_DS to DATATP_CRM
    @Qualifier(DatasourceConfiguration.PRIMARY_DS) DataSource dataSource, Environment environment,
    @Value("${spring.datasource.hibernate.hbm2ddl.auto:update}") String hbm2ddlAuto,
    @Value("${spring.datasource.hibernate.dialect:org.hibernate.dialect.HSQLDialect}") String hibernateDialect,
    @Value("${spring.datasource.hibernate.show_sql:false}") boolean showSql
  ) {
    LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();
    factory.setDataSource(dataSource);

    String[] moduleBasePkgs = new String[] {
      "cloud.datatp.fforwarder.price",
      "cloud.datatp.fforwarder.sales",
      "cloud.datatp.fforwarder.mgmt"
    };
    factory.setPackagesToScan(moduleBasePkgs);

    log.info("Entity Manager Factory scan packages: \n {}", DataSerializer.JSON.toString(moduleBasePkgs));
    factory.setJpaVendorAdapter(new HibernateJpaVendorAdapter());

    HashMap<String, Object> props = new HashMap<>();
    props.put("hibernate.hbm2ddl.auto", hbm2ddlAuto);
    props.put("hibernate.dialect", hibernateDialect);
    props.put("hibernate.show_sql", showSql);
    props.put("hibernate.format_sql", true);
    props.put("hibernate.enable_lazy_load_no_trans", true);
    log.info("Jpa Hibernate Props: \n {}", DataSerializer.JSON.toString(props));
    factory.setJpaPropertyMap(props);
    return factory;
  }

  //@Bean("crmTransactionManager")
  public PlatformTransactionManager crmTransactionManager(
    @Qualifier("crmEntityManagerFactory") LocalContainerEntityManagerFactoryBean factory
  ) {
    JpaTransactionManager transactionManager = new JpaTransactionManager();
    transactionManager.setEntityManagerFactory(factory.getObject());
    return transactionManager;
  }

}