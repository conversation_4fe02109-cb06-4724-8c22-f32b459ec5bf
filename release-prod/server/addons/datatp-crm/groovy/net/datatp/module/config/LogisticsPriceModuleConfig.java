package net.datatp.module.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Profile("logistics")
@ModuleConfig(
  basePackages= {"cloud.datatp.fforwarder.price"}
)
@Configuration
@ConfigurationProperties
@ComponentScan(basePackages = { "cloud.datatp.fforwarder.price" })
@EnableJpaRepositories(
  basePackages = {
    "cloud.datatp.fforwarder.price.repository",
  }
  //entityManagerFactoryRef = "crmEntityManagerFactory",
  //transactionManagerRef = "crmTransactionManager"
)
@EnableConfigurationProperties
@EnableTransactionManagement
@Import(value = {
  StorageModuleConfig.class, DataModuleConfig.class,
  //DataTPCrmJpaConfiguration.class
})
public class LogisticsPriceModuleConfig {
}