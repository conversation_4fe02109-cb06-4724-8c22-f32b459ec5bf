package net.datatp.logistics.intg.carrier.maersk;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.http.selenium.ChromeInstance;
import net.datatp.module.httpclient.CookieMap;
import net.datatp.module.httpclient.HttpClient;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;

@Slf4j @Getter
public class MaerskRest {
  final static String SERVER_URL        = "https://accounts.maersk.com";
  final static String AUTHENTICATE_PATH = "/root/acm/json/realms/mau/authenticate?authIndexType=service&authIndexValue=frPrimaryAuth";
  final static String SESSION_INFO_PATH = "/root/acm/json/mau/sessions/?_action=getSessionProperties";
  final static String ACCESS_TOKEN_PATH = "/ocean-maeu/acm/oauth2/realms/mau/access_token";

  
  private ChromeInstance chrome;
  
  @Getter
  private HttpClient client;
  private CookieMap  cookieMap ;
  
  public MaerskRest() {
    client = new HttpClient(SERVER_URL);
    SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
    requestFactory.setConnectTimeout(10000);  // 10 seconds
    requestFactory.setReadTimeout(10000);     // 10 seconds
    client.getRestTemplate().setRequestFactory(requestFactory);
  }

  public void authenticate(String loginId, String password) {
    HttpHeaders headers = createHttpHeaders() ;
    headers.set("x-acm-username", loginId);
    headers.set("x-acm-password", password);


    ResponseEntity<String> response = client.post(AUTHENTICATE_PATH, headers, new HashMap<String, Object>(), String.class);
    if(response.getStatusCode() != HttpStatus.OK) {
      log.error("Authenticate fail, status = {}", response.getStatusCode());
      log.error(response.getBody());
      throw RuntimeError.IllegalState("Authenticate fail, status = {}", response.getStatusCode());
    }
    cookieMap = new CookieMap(response);
    System.out.println(DataSerializer.JSON.toString(cookieMap));
  }
  
  
  public MapObject getSessionInfo() {
    HttpHeaders headers = createHttpHeaders();
    headers.set("Cookie", cookieMap.toCookiesString());

    Map<String, Object> requestBody = new HashMap<>();
    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
    ResponseEntity<MapObject> response = client.post(SESSION_INFO_PATH, headers, requestEntity, MapObject.class);

    if (HttpStatus.OK == response.getStatusCode()) {
      return response.getBody();
    } 
    throw new Error("Retrieve Session Info Fail!!!");
  }
  
  public MapObject getAccessToken() {
    HttpHeaders headers = createHttpHeaders();
    headers.set("Cookie", cookieMap.toCookiesString());
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

    MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
    requestBody.add("decision", "allow");
    requestBody.add("client_id", "portaluser");
    requestBody.add("grant_type", "authorization_code");
    requestBody.add("carrier", "MAEU");
    requestBody.add("code", "GZ7Jw1G1G8G0WVtZ4XL9nCISWgE");
    requestBody.add("nonce", "vvK7Hl7T8ya7y9S3Dx8t");
    requestBody.add("code_verifier", "5qyuneDI4hVxT7jBMgSj7r1Aq3srey0hSVc2NfEBNSX");
    requestBody.add("redirect_uri", "https://www.maersk.com/portaluser/oidc/callback");
    ResponseEntity<MapObject> response = client.post(ACCESS_TOKEN_PATH, headers, requestBody, MapObject.class);

    if (HttpStatus.OK == response.getStatusCode()) {
      return response.getBody();
    } 
    throw new Error("Retrieve Session Info Fail!!!");
  }

  

  public HttpHeaders createHttpHeaders() {
    HttpHeaders headers = client.createHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set("host", "accounts.maersk.com");
    headers.set("accept-api-version", "resource=2.0, protocol=1.0");
    headers.set("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/117.0.2045.31");

    return headers;
  }
}