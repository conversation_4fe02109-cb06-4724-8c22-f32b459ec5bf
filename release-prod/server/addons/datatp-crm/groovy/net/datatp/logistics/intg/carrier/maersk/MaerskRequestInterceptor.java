package net.datatp.logistics.intg.carrier.maersk;

import java.io.IOException;
import java.io.InputStream;

import org.openqa.selenium.remote.http.HttpHandler;
import org.openqa.selenium.remote.http.HttpRequest;
import org.openqa.selenium.remote.http.HttpResponse;

import lombok.extern.slf4j.Slf4j;
import net.datatp.module.http.selenium.HttpRequestInterceptor;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.io.IOUtil;

@Slf4j
public class MaerskRequestInterceptor extends HttpRequestInterceptor {
  @Override
  protected HttpResponse execute(HttpHandler handler, HttpRequest req) {
    String uri = req.getUri();
    if(uri.contains("ocean-maeu/acm/oauth2/realms/mau/access_token")) {
      System.out.println("WrapHandler: " + req.getUri());
      HttpResponse response = handler.execute(req);
      int status = response.getStatus() ;
      InputStream is = response.getContent().get();
      String content = null;
      try {
        content = IOUtil.getStreamContentAsString(is, response.getContentEncoding());
      } catch (IOException e) {
        log.error("Parse Response Error", e);
        throw RuntimeError.UnknownError("Cannot parse the response content");
      }
      if(status == 200) {
        System.out.println("ACCESS TOKEN: ");
        System.out.println(content);
        System.out.println("---------------------------------------------------------------------");
        //MapObject map = DataSerializer.JSON.fromString(content, MapObject.class);
        //System.out.println(DataSerializer.JSON.toString(map));
      } else {
      }
      return response;
    }
    String authorization = req.getHeader("Authorization");
    if(authorization != null) {
      System.out.println("Authorization: " + authorization);
    }
    return handler.execute(req);
  }
}
