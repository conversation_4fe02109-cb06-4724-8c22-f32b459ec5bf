package net.datatp.logistics.intg.carrier.maersk;

import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.SearchContext;
import org.openqa.selenium.WebElement;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;

import lombok.Getter;
import net.datatp.module.http.selenium.ChromeInstance;
import net.datatp.module.httpclient.CookieMap;
import net.datatp.module.httpclient.HttpClient;

@Getter
public class MaerskWebClient {
  final static String SERVER_URL           = "https://accounts.maersk.com";
  final static String HP_BRANCH_IDENTIFIER = "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH";
  
  
  private ChromeInstance chrome;
  private MaerskRequestInterceptor requestInterceptor;
  
  @Getter
  private HttpClient httpClient;
  private CookieMap  cookieMap ;
  
  public MaerskWebClient(ChromeInstance chrome) {
    this.chrome = chrome;
    requestInterceptor = new MaerskRequestInterceptor();
    chrome.withInterceptor(requestInterceptor);
    
    httpClient = new HttpClient(SERVER_URL);
    SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
    requestFactory.setConnectTimeout(10000);  // 10 seconds
    requestFactory.setReadTimeout(10000);     // 10 seconds
    httpClient.getRestTemplate().setRequestFactory(requestFactory);
  }

  public void authenticate(String loginId, String password) {
    chrome.get("https://www.maersk.com/portaluser/login");
    List<WebElement> hpBranchIdentifiers = chrome.elementsWithText(HP_BRANCH_IDENTIFIER);
    
    if(hpBranchIdentifiers.size() > 0) return;
    
    //checkAndAcceptCookies(1);
    chrome
      .inputByName("username", loginId)
      .inputByName("password", password)
      .clickButtonWithId("login-submit-button");
    //checkAndAcceptCookies(1);
    selectHPBranch();
  }
  
  public void selectHPBranch() {
    SearchContext shadowRoot = chrome.findShadowRoot("//mc-c-table");
    if(shadowRoot != null) {
      List<WebElement> prominents = shadowRoot.findElements(By.cssSelector("div.prominent"));
      for(WebElement prominent : prominents) {
        String text = prominent.getText();
        if("BEE LOGISTICS CORPORATION - HAI PHONG BRANCH".equals(text.trim())) {
          prominent.click();
        }
      }
    }
  }
  
  void checkAndAcceptCookies(int idx) {
    WebElement cookiesMsg = chrome.elementWithText("This website uses cookies");

    System.out.println("Cookie Info");
    System.out.println(cookiesMsg);
    if(cookiesMsg != null) {
      List<WebElement> buttons = chrome.elementsWithXPath("//button[contains(@aria-label,'Allow all')]");
      for(WebElement sel : buttons) {
        System.out.println("Button Label: " + sel.getText());
        System.out.println("on click:" + sel.getDomAttribute("onclick"));
      }
      //CookieInformation.submitAllCategories();
      //CookieInformation.submitAllCategories()
      if(idx == 0) {
        buttons.get(0).click();
        //chrome.getChromeDriver().executeScript("CookieInformation.submitAllCategories()");
      } else {
        buttons.get(0).click();
        //chrome.getChromeDriver().executeScript("CookieInformation.submitAllCategories();TogglePage(this, 'coiPage-1')");
      }
      
      System.out.println("Allow All Found = " + buttons.size());
      //buttons.get(buttonIdx).click();
      //chrome.clickButtonWithAriaLabel("Allow all");
    }
  }
 
  public HttpHeaders createHttpHeaders() {
    HttpHeaders headers = httpClient.createHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set("host", "accounts.maersk.com");
    headers.set("accept-api-version", "resource=2.0, protocol=1.0");
    headers.set("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/117.0.2045.31");

    return headers;
  }
}