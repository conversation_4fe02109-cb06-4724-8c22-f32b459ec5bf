package cloud.datatp.fforwarder.price.entity;

import lombok.Getter;

public enum CustomClearanceType {
  AIR("Air"), RAIL("Rail"), SEA_LCL("Sea LCL"), SEA_FCL("Sea FCL"),
  TRUCK_CONTAINER("Truck Container"), TRUCK_REGULAR("Truck Regular");

  @Getter
  private String label;

  CustomClearanceType(String label) {
    this.label = label;
  }

  static public CustomClearanceType[] ALL = CustomClearanceType.values();

  public static boolean isAirTransport(CustomClearanceType type) {
    return AIR.equals(type);
  }

  public static boolean isRailTransport(CustomClearanceType type) {
    return RAIL.equals(type);
  }

  public static boolean isSeaFCLTransport(CustomClearanceType type) {
    return SEA_FCL.equals(type);
  }

  public static boolean isSeaLCLTransport(CustomClearanceType type) {
    return SEA_LCL.equals(type);
  }

  public static boolean isTruckRegular(CustomClearanceType type) {
    return TRUCK_REGULAR.equals(type);
  }

  public static boolean isTruckContainer(CustomClearanceType type) {
    return TRUCK_CONTAINER.equals(type);
  }

  public static CustomClearanceType parse(String token) {
    if(token == null) return null;
    return valueOf(token.toUpperCase());
  }
}