package cloud.datatp.fforwarder.price.entity;

import cloud.datatp.fforwarder.price.common.BaseTruckTransportCharge;
import cloud.datatp.fforwarder.price.common.ContainerPriceGroup;
import cloud.datatp.fforwarder.price.common.ProvinceNormalizer;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

@Entity
@Table(
  name = TruckContainerTransportCharge.TABLE_NAME,
  indexes = {@Index(columnList = "valid_from, valid_to")}
)
@DeleteGraphs({
  @DeleteGraph(target = TransportAdditionalCharge.class, joinField = "truck_container_charge_id", joinType = DeleteGraphJoinType.OneToMany),
  @DeleteGraph(target = CommissionDistribution.class, joinField = "truck_container_charge_id", joinType = DeleteGraphJoinType.OneToMany),
  @DeleteGraph(target = TransportFrequency.class, joinField = "truck_container_charge_id", joinType = DeleteGraphJoinType.OneToMany),
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class TruckContainerTransportCharge extends BaseTruckTransportCharge {

  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_price_truck_container_charge";

  @NotNull
  private String code;

  @Embedded
  private ContainerPriceGroup priceGroup = new ContainerPriceGroup();

  @Column(name = "delivery_address_normalized")
  private String deliveryAddressNormalized;

  @Column(name = "priority")
  private Integer priority = 0; // Higher number means higher priority

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "truck_container_charge_id", referencedColumnName = "id")
  private List<TransportFrequency> transportFrequencies = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "truck_container_charge_id", referencedColumnName = "id")
  private List<TransportAdditionalCharge> additionalCharges = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "truck_container_charge_id", referencedColumnName = "id")
  private List<CommissionDistribution> commissionDistributions = new ArrayList<>();

  public TruckContainerTransportCharge withCurrency(String name) {
    this.currency = name;
    return this;
  }

  public TruckContainerTransportCharge withPrivateShareableScope() {
    super.withPrivateShareableScope();
    return this;
  }

  public List<TransportFrequency> getTransportFrequencies() {
    if (Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    return transportFrequencies;
  }

  public void setTransportFrequencies(List<TransportFrequency> frequencies) {
    if (Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    this.transportFrequencies.clear();
    if (frequencies != null) this.transportFrequencies.addAll(frequencies);
  }

  public List<TransportAdditionalCharge> getAdditionalCharges() {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    return additionalCharges;
  }

  public void setAdditionalCharges(List<TransportAdditionalCharge> addCharges) {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    this.additionalCharges.clear();
    if (addCharges != null) this.additionalCharges.addAll(addCharges);
  }

  public List<CommissionDistribution> getCommissionDistributions() {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    return commissionDistributions;
  }

  public void setCommissionDistributions(List<CommissionDistribution> commissions) {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    this.commissionDistributions.clear();
    if (commissions != null) this.commissionDistributions.addAll(commissions);
  }

  public TruckContainerTransportCharge computeFromMapObject(MapObject record) {
    if (record == null) return this;
    shareable = ShareableScope.COMPANY;
    mapFrom(record);
    priceGroup = Objects.ensureNotNull(priceGroup, ContainerPriceGroup::new).mapFrom(record);
    deliveryAddressNormalized = ProvinceNormalizer.removeVietnameseTone(this.deliveryAddress);
    return this;
  }

  @Override
  public void set(ClientInfo client, Company company) {
    super.set(client, company);
    set(client, company, additionalCharges);
    set(client, company, commissionDistributions);
    set(client, company, transportFrequencies);
  }

}