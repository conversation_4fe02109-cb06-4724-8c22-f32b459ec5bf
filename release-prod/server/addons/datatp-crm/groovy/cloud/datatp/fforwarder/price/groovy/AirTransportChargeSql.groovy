package cloud.datatp.fforwarder.price.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.module.data.db.query.RangeFilter
import net.datatp.util.ds.MapObject
import net.datatp.util.text.DateUtil
import net.datatp.util.text.StringUtil
import org.springframework.context.ApplicationContext

class AirTransportChargeSql extends Executor {
  public class SearchAirTransportCharge extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");

      RangeFilter filter = (RangeFilter) sqlParams.get("validTo");
      RangeFilter createdTime = (RangeFilter) sqlParams.get("createdTime");

      String filterByDate = ""
      if(filter != null) {
        if (StringUtil.isNotEmpty(filter.getFromValue())) {
          sqlParams.put("validToFrom", DateUtil.parseCompactDateTime(filter.getFromValue()));
          filterByDate = " AND (charge.valid_from <= :validToFrom AND charge.valid_to >= :validToFrom)"
        }

        if (StringUtil.isNotEmpty(filter.getToValue())) {
          sqlParams.put("validToTo", DateUtil.parseCompactDateTime(filter.getToValue()));
          filterByDate += " AND (charge.valid_to <= :validToTo)"
        }
      } else if(createdTime != null) {
        if (StringUtil.isNotEmpty(createdTime.getFromValue())) {
          sqlParams.put("createdTimeFrom", DateUtil.parseCompactDateTime(createdTime.getFromValue()));
          filterByDate = " AND (charge.created_time >= :createdTimeFrom)"
        }

        if (StringUtil.isNotEmpty(createdTime.getToValue())) {
          sqlParams.put("createdTimeTo", DateUtil.parseCompactDateTime(createdTime.getToValue()));
          filterByDate += " AND (charge.created_time <= :createdTimeTo)"
        }
      } else {
        filterByDate = " AND DATE(charge.valid_to) >= CURRENT_DATE";
      }

      String orderBy = """
                ORDER BY charge.from_location_code, charge.to_location_code,
                         charge.created_time DESC, charge.valid_to DESC 
      """

      if(!isNotContainKey(sqlParams, "pricingCreatorId")) {
        orderBy = """ ORDER BY charge.modified_time DESC """
      }

      String query = """
                WITH frequency_data AS (
                    SELECT air_charge_id,
                           STRING_AGG(depart_time, ', ')        AS etd,
                           STRING_AGG(cut_off_time, ', ')       AS cutoff,
                           STRING_AGG(label, ', ')              AS frequency,
                           STRING_AGG(transit_time, ', ')       AS transit_time,
                           STRING_AGG(transit_label, ', ')      AS transit_port
                    FROM lgc_price_transport_frequency
                    GROUP BY air_charge_id
                ), add_charge_data AS (
                    SELECT air_charge_id,
                          json_agg(
                            json_build_object(
                              'id', id, 
                              'label', label, 
                              'unit', unit, 
                              'finalCharge', final_charge,
                              'note', note
                              )) AS additional_charges
                    FROM lgc_price_transport_additional_charge
                    GROUP BY air_charge_id
                )
                SELECT  
                  charge.*,                                     
                  add.additional_charges                        AS additional_charges,
                  company.label                                 AS company_label
                FROM lgc_price_air_charge AS charge
                    LEFT JOIN frequency_data AS freq ON charge.id = freq.air_charge_id
                    LEFT JOIN company_company AS company ON charge.company_id = company.id 
                    LEFT JOIN add_charge_data AS add ON charge.id = add.air_charge_id
                    JOIN settings_location AS from_loc 
                        ON from_loc.location_type = 'Airport' 
                        AND (from_loc.code = charge.from_location_code OR from_loc.iata_code = charge.from_location_code)
                    JOIN settings_location AS to_loc 
                        ON to_loc.location_type = 'Airport' 
                        AND (to_loc.code = charge.to_location_code OR to_loc.iata_code = charge.to_location_code)
                WHERE 
                  ${FILTER_BY_STORAGE_STATE('charge', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.purpose', 'purpose', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.assignee_account_id', 'pricingCreatorId', sqlParams)}
                  ${AND_FILTER_BY(['from_loc.code', "from_loc.iata_code"], 'fromLocationCode', sqlParams)}
                  ${AND_FILTER_BY(['to_loc.code', "to_loc.iata_code"], 'toLocationCode', sqlParams)}
                  ${filterByDate}
                  AND (
                    (charge.shareable = 'COMPANY' AND charge.company_id = :companyId) OR 
                    (
                        charge.shareable = 'DESCENDANTS' AND charge.company_id IN (
                            SELECT 
                                distinct cc.id 
                            FROM company_company cc 
                            WHERE cc.parent_id IN (:companyIds) OR cc.id IN (:companyIds)
                    )) OR
                    (charge.shareable = 'ORGANIZATION')
                  )
                ${orderBy} 
                ${MAX_RETURN(sqlParams)}
            """;
      return query;
    }
  }

  public AirTransportChargeSql() {
    register(new SearchAirTransportCharge())
  }
}