package cloud.datatp.fforwarder.price.common;

import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.xlsx.SheetMetadata;
import net.datatp.module.data.xlsx.XSLXToMapObjectParser;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;

@Setter @Getter
@NoArgsConstructor
public class ExcelPriceParserResult {
  private Map<String, SheetMapping> sheetMappingMap = new HashMap<>();
  private Deque<String> warningLogs = new ArrayDeque<>();
  private SeaFclTransportCharge.GroupType groupType = null;

  @JsonProperty("records")
  public List<MapObject> getRecords() {
    List<MapObject> holder = new ArrayList<>();
    for(SheetMapping sheetMapping: this.sheetMappingMap.values()) {
      holder.addAll(sheetMapping.getRecords());
    }
    //holder.sort(Comparator.comparing(sel -> sel.getBoolean("_validate_", true)));
    return holder;
  }

  public void withSheetMappingMap(SheetMapping sheetMapping) {
    this.sheetMappingMap.put(sheetMapping.getSheetName(), sheetMapping);
  }

  @Getter @Setter @NoArgsConstructor
  public static class SheetMapping {
    private String sheetName;
    private MapObject metadata = new MapObject();

    @JsonIgnore
    private Map<String, String> headerMapping = new HashMap<>();

    @JsonIgnore
    private List<MapObject> records = new ArrayList<>();

    @JsonIgnore
    private Map<String, String> templateUpload = new HashMap<>();

    public SheetMapping(String sheetName, XSLXToMapObjectParser parser, Map<String, String> templateUpload) {
      this.templateUpload = templateUpload;
      this.sheetName = sheetName;
      this.headerMapping = new HashMap<>();
      this.metadata = computeTemplateRecord(parser.getSheetMetadata());

      for(String header: parser.getHeaderMapping().getHeaders()) {
        if(header == null) header = "";
        String trimmedHeader = header.trim();
        String fieldMapping = templateUpload.getOrDefault(trimmedHeader, null);
        if(fieldMapping == null) fieldMapping = isNormalizedColumnMatch(templateUpload, trimmedHeader);
        this.headerMapping.put(trimmedHeader, fieldMapping);
      }

      this.records = new ArrayList<>();
      final List<MapObject> rows = parser.getRows();
      for(MapObject row: rows) {
        if(row == null) continue;
        MapObject recordMapper = DataSerializer.JSON.clone(metadata);
        for (String column : row.keySet()) {
          String field = headerMapping.getOrDefault(column, column);
          if(field == null) field = column;
          recordMapper.put(field, row.get(column));
        }
        this.records.add(recordMapper);
      }
    }

    private static String isNormalizedColumnMatch(Map<String, String> templateUpload, String header) {
      String normalizedColumn1 = header.replaceAll("\\s+", "").toLowerCase();
      for(String templateCol: templateUpload.keySet()) {
        String normalizedColumn2 = templateCol.replaceAll("\\s+", "").toLowerCase();
        if(normalizedColumn1.equals(normalizedColumn2)) {
          return templateUpload.get(templateCol);
        }
      }
      return null;
    }

    public MapObject computeTemplateRecord(SheetMetadata metadata) {
      MapObject template = new MapObject();
      template.put("_validate_", true);
      template.put("_error_", null);
      if(metadata != null) {
        for(String property : metadata.keySet()) {
          String fieldMapping = templateUpload.getOrDefault(property, null);
          if(fieldMapping == null) fieldMapping = isNormalizedColumnMatch(templateUpload, property);
          this.headerMapping.put(property, fieldMapping);
          if(fieldMapping != null) template.put(fieldMapping, metadata.get(property));
        }
      }
      return template;
    }

    @JsonProperty("unknownHeader")
    public List<String> getUnknownMapping() {
      List<String> holder = new ArrayList<>();
      for (Entry<String, String> entry: headerMapping.entrySet()) {
        if(entry.getValue() == null) {
          holder.add(entry.getKey());
        }
      }
      return holder;
    }
  }
}