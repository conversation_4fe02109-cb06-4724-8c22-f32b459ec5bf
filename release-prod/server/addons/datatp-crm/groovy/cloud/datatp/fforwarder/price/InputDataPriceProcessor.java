package cloud.datatp.fforwarder.price;

import cloud.datatp.bfsone.partner.BFSOnePartnerLogic;
import cloud.datatp.bfsone.partner.entity.BFSOnePartner;
import cloud.datatp.fforwarder.price.common.ExcelParserHelper;
import cloud.datatp.fforwarder.price.common.ExcelPriceParserResult;
import cloud.datatp.fforwarder.price.common.ExcelPriceParserResult.SheetMapping;
import cloud.datatp.fforwarder.price.common.ProvinceNormalizer;
import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import cloud.datatp.fforwarder.price.http.Params.XlsxPriceParserReq;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.TransportationMode;
import jakarta.annotation.PostConstruct;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Deque;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.xlsx.SheetMetadata;
import net.datatp.module.data.xlsx.XSLXToMapObjectParser;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.module.http.upload.UploadService;
import net.datatp.module.resource.location.LocationLogic;
import net.datatp.module.resource.location.StateLogic;
import net.datatp.module.resource.location.entity.Location;
import net.datatp.module.resource.location.entity.LocationType;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j @Getter
@Component
public class InputDataPriceProcessor {
  private Map<String, Location> locationMap = new HashMap<>();
  private Deque<String> warningLogs = new ArrayDeque<>();

  @Autowired
  private LocationLogic locationLogic;

  @Autowired
  private StateLogic stateLogic;

  @Autowired
  private UploadService uploadService;

  @Autowired
  private ProvinceNormalizer normalizer;

  @Autowired
  private BFSOnePartnerLogic fwdPartnerLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  @PostConstruct
  public void init() {
    locationMap = new HashMap<>();
    warningLogs = new ArrayDeque<>();
  }

  public ExcelPriceParserResult xlsxPriceParser(ClientInfo client, Company company, XlsxPriceParserReq req) {
    if(!normalizer.isCached()) {
      List<Location> provinces = locationLogic.findLocationsByType(client, LocationType.State);
      normalizer.cacheProvinces(provinces);
    }
    locationMap = new HashMap<>();
    warningLogs = new ArrayDeque<>();
    try {
      UploadResource upRes = req.getUploadResource();
      byte[] data = uploadService.load(upRes.getStoreId());
      return dataProcessor(client, company, req, data);
    } catch (Exception ex) {
      log.error("Error when parse excel price, {1}", ex);
    }
    return null;
  }

  public ExcelPriceParserResult dataProcessor(ClientInfo client, Company company, XlsxPriceParserReq req, byte[] data) throws Exception {
    Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    Objects.assertNotNull(employee, "Employee not found!!!, login id: " + client.getRemoteUser());

    XSLXToMapObjectParser parser = new XSLXToMapObjectParser(data);
    TransportationMode mode = req.getMode();
    final Purpose purpose = req.getPurpose();
    Map<String, String> templateUpload = ExcelParserHelper.getTemplateUpload(mode, purpose);

    ExcelPriceParserResult result = new ExcelPriceParserResult();

    SeaFclTransportCharge.GroupType groupType = null;

    for (String sheetName : parser.getSheetNames()) {
      if (sheetName.startsWith("__")) continue;
      parser.parse(sheetName);
      final SheetMetadata sheetMetadata = parser.getSheetMetadata();

      BFSOnePartner carrier = null;
      String carrierLabel = sheetMetadata.getString("Subcontractor", "").trim();
      if(StringUtil.isNotEmpty(carrierLabel)) carrier = fwdPartnerLogic.getByCode(client, carrierLabel);

      SheetMapping sheetMapping = new SheetMapping(sheetName, parser, templateUpload);

      List<MapObject> mappedRecords = new ArrayList<>();

      for (MapObject rec : sheetMapping.getRecords()) {
        MapObject error = new MapObject();

        try {
          Date validFrom = rec.getDate("validFrom", null);
          if(validFrom == null) validFrom = new Date();
          rec.put("validFrom", DateUtil.asCompactDateTime(validFrom));
        } catch (RuntimeException ex) {
          rec.put("validFrom", DateUtil.asCompactDateTime(new Date()));
        }

        try {
          final Date validTo = rec.getDate("validTo", null);
          if(validTo == null) {
            String mgs = "Valid To is required!!!";
            error.put("validTo", mgs);
          } else {
            rec.put("validTo", DateUtil.asCompactDateTime(validTo));
          }
        } catch (RuntimeException ex) {
          String mgs = "Valid To is not in date format!!!";
          error.put("validTo", mgs);
        }

        if(TransportationMode.isAirTransport(mode)) {

          Location fromLocation = computeAirPortLocation(client, rec.getString("fromLocationCode", "").trim());
          if(fromLocation == null) {
            String mgs = "Location: " + rec.getString("fromLocationCode", "") + " not found!!!";
            error.put("fromLocationCode", mgs);
          } else {
            checkPurposeWithFromLocation(fromLocation, purpose);
            rec.put("fromLocationCode", fromLocation.getCode().trim());
            rec.put("fromLocationLabel", fromLocation.getShortLabel());
          }
          Location toLocation = computeAirPortLocation(client, rec.getString("toLocationCode", "").trim());
          if(toLocation == null) {
            String mgs = "Location: " + rec.getString("toLocationCode", "") + " not found!!!";
            error.put("toLocationCode", mgs);
          } else {
            rec.put("toLocationCode", toLocation.getCode().trim());
            rec.put("toLocationLabel", toLocation.getShortLabel());
          }
          final boolean validate = error.isEmpty();
          rec.put("_error_", error);
          rec.put("_validate_", validate);
          mappedRecords.add(rec);

        } else if(TransportationMode.isSeaTransport(mode)) {

          if(TransportationMode.isSeaLCLTransport(mode)) {
            try {
              double freightChargeLCL = rec.getDouble("freightChargeLCL", 0D);
              if(freightChargeLCL != 0) {
                rec.put("minimumChargeLCL", freightChargeLCL);
                rec.put("less2CbmPrice", freightChargeLCL);
                rec.put("less3CbmPrice", freightChargeLCL);
                rec.put("less5CbmPrice", freightChargeLCL);
                rec.put("less7CbmPrice", freightChargeLCL);
                rec.put("less10CbmPrice", freightChargeLCL);
                rec.put("geq10CbmPrice", freightChargeLCL);
              }
            } catch (RuntimeException ex) {

            }
          } else if(Purpose.EXPORT.equals(purpose)) {

            if(groupType == null) {
              boolean isSpecial = rec.containsKey("reefer20Price") || rec.containsKey("reefer40Price");
              boolean isUsRoute = rec.containsKey("highCube45Price");
              if(isSpecial && isUsRoute) {
                warningLogs.add("Warning: Group type is not properly classified. Please review the uploaded data.");
              } else if(isSpecial) {
                groupType = SeaFclTransportCharge.GroupType.SPECIAL;
              } else if(isUsRoute) {
                groupType = SeaFclTransportCharge.GroupType.US_ROUTE;
              } else {
                groupType = SeaFclTransportCharge.GroupType.NONE_US;
              }

              if(groupType == null) {
                String mgs = "Group type is not properly classified. Please review the uploaded data.!!!";
                error.put("groupType", mgs);
              } else {
                rec.put("groupType", groupType.name());
              }
            } else {
              rec.put("groupType", groupType.name());
            }
          }

          Location fromLocation = computeLocation(client, rec.getString("fromLocationCode", "").trim());
          if(fromLocation == null) {
            String mgs = "Location: " + rec.getString("fromLocationCode", "") + " not found!!!";
            error.put("fromLocationCode", mgs);
          } else {
            checkPurposeWithFromLocation(fromLocation, purpose);
            rec.put("fromLocationCode", fromLocation.getCode().trim());
            rec.put("fromLocationLabel", fromLocation.getShortLabel());
          }

          Location toLocation = computeLocation(client, rec.getString("toLocationCode", "").trim());

          if(toLocation == null) {
            String mgs = "Location: " + rec.getString("toLocationCode", "") + " not found!!!";
            error.put("toLocationCode", mgs);
          } else {
            rec.put("toLocationCode", toLocation.getCode().trim());
            rec.put("toLocationLabel", toLocation.getShortLabel());
          }

          rec.put("assigneeEmployeeId", employee.getId());
          rec.put("assigneeLabel", employee.getLabel());

          final boolean validate = error.keySet().isEmpty();
          rec.put("_error_", error);
          rec.put("_validate_", validate);
          mappedRecords.add(rec);
        } else if(TransportationMode.isTruckTransport(mode)) {
          validateTruckingRate(client, company, rec, carrier, error, mappedRecords);
        }
      }

      sheetMapping.setRecords(mappedRecords);
      result.setWarningLogs(this.warningLogs);
      result.setGroupType(groupType);
      result.withSheetMappingMap(sheetMapping);
    }
    return result;
  }

  private void checkPurposeWithFromLocation(Location fromLocation, Purpose purpose) {
    String countryLabel = fromLocation.getCountryLabel();
    if (!StringUtil.isEmpty(countryLabel)) {
      String lowerCountry = countryLabel.toLowerCase().trim();
      String baseMessage = null;
      boolean nonMatchExp = purpose.equals(Purpose.EXPORT) && !lowerCountry.equals("vietnam");
      boolean nonMatchImp = purpose.equals(Purpose.IMPORT) && lowerCountry.equals("vietnam");
      if (nonMatchExp) {
        baseMessage = "Warning: Price list mismatch! Export selected, but data is for import.";
      } else if (nonMatchImp) {
        baseMessage = "Warning: Price list mismatch! Import selected, but data is for export.";
      }


      if (baseMessage != null) {
        boolean found = false;
        for (String log : warningLogs) {
          if (log.startsWith(baseMessage)) {
            found = true;
            int count = 1;
            int lastSpaceIndex = log.lastIndexOf(' ');
            if (lastSpaceIndex != -1) {
              try {
                count = Integer.parseInt(log.substring(lastSpaceIndex + 1)) + 1;
              } catch (NumberFormatException ignored) {}
            }
            warningLogs.remove(log);
            warningLogs.offerFirst(baseMessage + " Occurrences: " + count);
            break;
          }
        }
        if (!found) {
          warningLogs.offerFirst(baseMessage + " Occurrences: 1");
        }
      }
    }
  }

  private void validateTruckingRate(
    ClientInfo client, Company company, MapObject rec, BFSOnePartner carrier, MapObject error, List<MapObject> mappedRecords) {

    if(carrier != null) {
      long carrierPartnerId = rec.getLong("carrierPartnerId", -1L);
      if(carrierPartnerId == -1) {
        rec.put("carrierPartnerId", carrier.getId());
        rec.put("carrierLabel", carrier.getName());
      }
    }

    String pickupLocCode = rec.getString("pickupLocationCode", "").trim();
    Location pickupLocation = normalizer.findLocationProvince(pickupLocCode);

    if(pickupLocation == null) pickupLocation = computeLocation(client, pickupLocCode);
    if(pickupLocation == null) {
      String mgs = "Pickup Location/ Province: " + rec.getString("pickupLocationCode", "") + " not valid!!!";
      error.put("pickupLocationCode", mgs);
    } else {
      rec.put("pickupLocationLabel", normalizer.normalizeProvinceToken(pickupLocation.getLabel(), false));
      rec.put("pickupLocationCode", pickupLocation.getCode());
      if(StringUtil.isEmpty(rec.getString("pickupAddress", ""))) rec.put("pickupAddress", pickupLocation.getAddress());
    }

    final String deliveryLocCode = rec.getString("deliveryLocationCode", "").trim();
    Location deliveryLocation = normalizer.findLocationProvince(deliveryLocCode);
    if(deliveryLocation == null) deliveryLocation = computeLocation(client, deliveryLocCode);
    if(deliveryLocation == null) {
      String mgs = "Delivery Location/ Province: " + rec.getString("deliveryLocationCode", "") + " not valid!!!";
      error.put("deliveryLocationCode", mgs);
    } else {
      rec.put("deliveryLocationLabel", normalizer.normalizeProvinceToken(deliveryLocation.getLabel(), false));
      rec.put("deliveryLocationCode", deliveryLocation.getCode());
      if(StringUtil.isEmpty(rec.getString("pickupAddress", ""))) rec.put("pickupAddress", deliveryLocation.getAddress());
    }

    boolean isGeneralRate = true;

    String targetReferenceLabel = rec.getString("targetReferenceLabel", "");
    if(StringUtil.isNotEmpty(targetReferenceLabel)) {
      String[] codes = Arrays.stream(targetReferenceLabel.split(","))
        .map(String::trim)
        .toArray(String[]::new);

      for (String partnerCode: codes) {
        isGeneralRate  = false;
        MapObject newRec = DataSerializer.JSON.clone(rec);
        MapObject newError = DataSerializer.JSON.clone(error);

        BFSOnePartner forwarderPartner = fwdPartnerLogic.getByCode(client, partnerCode);
        if(forwarderPartner == null) {
          newRec.put("targetReferenceLabel", partnerCode);
        } else {
          newRec.put("targetReferenceId", forwarderPartner.getId());
          newRec.put("targetReferenceLabel", forwarderPartner.getName());
        }
        boolean newValidate = newError.keySet().isEmpty();
        newRec.put("_error_", newError);
        newRec.put("_validate_", newValidate);
        mappedRecords.add(newRec);
      }
    }

    if(isGeneralRate) {
      boolean validate = error.keySet().isEmpty();
      rec.put("_error_", error);
      rec.put("_validate_", validate);
      mappedRecords.add(rec);
    }

  }

  public Location computeAirPortLocation(ClientInfo client, String locCode) {
    Location location = null;
    if(!locationMap.containsKey(locCode) && !warningLogs.contains(locCode)) {
      location = locationLogic.getAirPortLocation(client, locCode);
      if(location != null) {
        locationMap.put(locCode, location);
      } else {
        final List<Location> locations = locationLogic.findLocationByLabel(client, locCode);
        if (locations.size() == 1) {
          location = locations.get(0);
          locationMap.put(locCode, location);
        } else {
          //warningLogs.add(locCode);
        }
      }
    } else {
      location = locationMap.get(locCode);
    }
    return location;
  }

  public Location computeLocation(ClientInfo client, String locCode) {
    Location location = null;
    if(!locationMap.containsKey(locCode) && !warningLogs.contains(locCode)) {
      location = locationLogic.getLocation(client, locCode);
      if(location != null) {
        locationMap.put(locCode, location);
      } else {
        final List<Location> locations = locationLogic.findLocationByLabel(client, locCode);
        if (locations.size() == 1) {
          location = locations.get(0);
          locationMap.put(locCode, location);
        } else {
          //warningLogs.add(locCode);
        }
      }
    } else {
      location = locationMap.get(locCode);
    }
    return location;
  }

}