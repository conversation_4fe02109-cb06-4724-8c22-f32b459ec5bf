package cloud.datatp.fforwarder.price;

import java.util.Date;
import java.util.List;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Component;

import cloud.datatp.fforwarder.price.calculator.AdditionalChargeCalculator;
import cloud.datatp.fforwarder.price.entity.RailTransportCharge;
import cloud.datatp.fforwarder.price.repository.RailTransportChargeRepository;
import groovy.lang.Binding;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.ShareableUtil;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryManager;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.groovy.GroovyRepositoryManager;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import net.datatp.util.text.TokenUtil;

// TODO: An - rename to RailPriceLogic
@Component
public class RailTransportChargeLogic extends TransportPriceLogic {
  @Autowired
  private RailTransportChargeRepository repo;

  @Autowired
  private SeqService seqService;

  @Autowired
  private SqlQueryManager sqlQueryManager;

  @Autowired
  private GroovyRepositoryManager groovyRepoManager;

  @Autowired
  private DataSource dataSource;

  public RailTransportCharge save(ClientInfo client, Company company, RailTransportCharge charge) {
    if (charge.isNew()) {
      final String code = generateTransportPriceCode(charge);
      charge.setCode(code);
    } else {
      Objects.assertTrue(charge.isSameCompany(company.getId()), "Not allowed to save data with the different company");
    }
    final String fromLocCode = charge.getFromLocationCode();
    final String label = TokenUtil.labelWithToken(fromLocCode, charge.getToLocationCode(), charge.getCarrierLabel());
    charge.setLabel(label.toUpperCase());
    charge.setRoute(charge.getFromLocationCode() + "-" + charge.getToLocationCode());
    if (StringUtil.isEmpty(charge.getCarrierLabel())) charge.setCarrierRoute(charge.getRoute() + "-" + "N/A");
    else charge.setCarrierRoute(charge.getRoute() + "-" + charge.getCarrierLabel());
    AdditionalChargeCalculator.calculate(charge.getAdditionalCharges());
    charge.set(client, company);
    return repo.save(charge);
  }

  public List<RailTransportCharge> saveCharges(ClientInfo client, Company company, List<RailTransportCharge> charges) {
    return charges.stream().map(charge -> save(client, company, charge)).toList();
  }

  public List<SqlMapRecord> search(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    SqlQueryManager.QueryContext queryContext = sqlQueryManager.create(QUERY_SCRIPT_DIR, "RailTransportChargeQuery.groovy");
    ShareableUtil.enrichShareableParams(client, company, sqlParams);
    MapSqlParameterSource params = sqlParams.toSqlParameter();
    final SqlSelectView view = queryContext.createSqlSelectView(new Binding(), params);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public boolean updateStorageState(ClientInfo clientInfo, ChangeStorageStateRequest req) {
    repo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public List<RailTransportCharge> findByCompany(ClientInfo client, Company company) {
    return repo.findByCompanyId(company.getId());
  }

  public List<RailTransportCharge> findByIds(ClientInfo client, Company company, List<Long> ids) {
    return repo.findByIds(ids);
  }

  public boolean deleteByIds(ClientInfo client, Company company, List<Long> ids) {
    if (Collections.isEmpty(ids)) return false;
    List<RailTransportCharge> charges = findByIds(client, company, ids);
    for (RailTransportCharge charge : charges) {
      Objects.assertTrue(charge.isSameCompany(company.getId()), "Not allowed to delete data with the different company");
    }
    DBConnectionUtil connectionUtil = new DBConnectionUtil(getJdbcDataSource());
    DeleteGraphBuilder graph = new DeleteGraphBuilder(connectionUtil, company.getId(), RailTransportCharge.class, ids);
    int count = graph.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count > 0;
  }
}