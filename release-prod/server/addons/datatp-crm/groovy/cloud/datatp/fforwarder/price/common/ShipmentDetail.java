package cloud.datatp.fforwarder.price.common;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.text.StringUtil;

@Embeddable
@Getter @Setter
@NoArgsConstructor
public class ShipmentDetail {

  public enum Stackable {
    STACKABLE, NON_STACKABLE;
  }

  @Column(name = "dimension_length")
  private double dimensionL; // DIM (L)

  @Column(name = "dimension_width")
  private double dimensionW; // DIM (W)

  @Column(name = "dimension_height")
  private double dimensionH; // DIM (H)

  @Column(name = "stackable")
  @Enumerated(EnumType.STRING)
  private Stackable stackable; // Stackable or Non Stackable?

  @Column(name = "package_quantity")
  private Integer packageQty;

  @Column(name = "volume_info")
  private String volumeInfo; // Information based on container/volume/gross weight

  @Column(name = "volume_cbm")
  private double volumeCbm; //VOL (CBM)

  @Column(name = "gross_weight_kg")
  private double grossWeightKg; // G.W (KGS)

  @Column(name = "report_volume")
  private Double reportVolume;

  @Column(name = "report_volume_unit")
  private String reportVolumeUnit;

  @Column(name = "commodity", length = 1024 * 32)
  private String commodity;

  @Column(name = "desc_of_goods", length = 2*1024)
  private String descOfGoods;

  @Column(name = "dg_liquid_cargo")
  private boolean dgLiquidCargo; // DG/Liquid Cargo?

  @Column(name = "buy_insurance_request")
  private boolean buyInsuranceRequest; // Request to buy insurance?

  @Column(name = "express_courier")
  private Boolean expressCourier;

  @Column(name = "cross_border_trucking")
  private Boolean crossBorderTrucking;

  /*
   * Special request notes for air/freight etc.
   * Air Freight: Special request : keep cool, degree?
   * FCL: Special container: Provide Temperature (If reefer container), DIM, GW, Picture of goods
   */
  @Column(name = "special_request_note", length = 2*1024)
  private String specialRequestNote;

  @Column(name = "free_time_terminal_request", length = 512)
  private String freeTimeTerminalRequest; // Request for free time/terminal at destination

  @Column(name = "vn_border_gate_request", length = 512)
  private String vnBorderGateRequest; // Cửa khẩu yêu cầu ở VN

  public static double calculateTUE(String volumeInfo) {
    if(StringUtil.isEmpty(volumeInfo)) return 0;
    String pattern = "^\\s*\\d+[*x](20|40|45)[A-Za-z]{2}\\s*(\\/\\s*\\d+[*x](20|40|45)[A-Za-z]{2}\\s*)*$";
    if (!volumeInfo.matches(pattern)) return 0;
    String[] entries = volumeInfo.split("[,/]");
    int totalTUE = 0;

    for (String entry : entries) {
      entry = entry.trim();
      Pattern eachPattern = Pattern.compile("(\\d+)[*x](20|40|45)[A-Za-z]{2}");
      Matcher matcher = eachPattern.matcher(entry);

      if (matcher.find()) {
        int quantity = Integer.parseInt(matcher.group(1));
        String size = matcher.group(2);

        if ("20".equals(size)) {
          totalTUE += quantity;
        } else if ("40".equals(size) || "45".equals(size)) {
          totalTUE += quantity * 2;
        }
      }
    }
    return totalTUE;
  }

  public boolean isExpressCourier() {
    return expressCourier != null && expressCourier;
  }

  public boolean isCrossBorderTrucking() {
    return crossBorderTrucking != null && crossBorderTrucking;
  }

}