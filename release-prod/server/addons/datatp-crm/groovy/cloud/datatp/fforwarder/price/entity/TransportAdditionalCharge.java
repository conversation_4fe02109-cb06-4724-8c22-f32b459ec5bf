package cloud.datatp.fforwarder.price.entity;

import cloud.datatp.fforwarder.price.common.ChargeTarget;
import cloud.datatp.fforwarder.price.entity.AdditionalCharge.AdditionalChargeMethod;
import cloud.datatp.fforwarder.price.entity.AdditionalCharge.AdditionalChargeType;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.TransportationMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyPersistable;
import net.datatp.module.settings.currency.entity.Currency;
import net.datatp.util.ds.Objects;

@Entity
@Table(name = TransportAdditionalCharge.TABLE_NAME)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class TransportAdditionalCharge extends CompanyPersistable {
  public static final String TABLE_NAME = "lgc_price_transport_additional_charge";

  private String name;

  private String label;

  @Enumerated(EnumType.STRING)
  private AdditionalChargeMethod method = AdditionalChargeMethod.QUANTITY;

  @Enumerated(EnumType.STRING)
  private AdditionalChargeType type = AdditionalChargeType.OTHER_CHARGE;

  @Enumerated(EnumType.STRING)
  private ChargeTarget target = ChargeTarget.ORIGIN;

  @Enumerated(EnumType.STRING)
  private Purpose purpose;

  @Enumerated(EnumType.STRING)
  @Column(name = "cc_type")
  private CustomClearanceType customClearanceType;

  @Enumerated(EnumType.STRING)
  @Column(name = "transportation_mode")
  private TransportationMode transportationMode;

  private double quantity;

  @Column(name = "unit_price")
  private double unitPrice;

  private String unit;

  @Column(name = "tax_rate")
  private double taxRate;

  private String currency;

  private double total;

  @Column(name = "total_tax")
  private double totalTax;

  @Column(name = "final_charge")
  private double finalCharge;

  @Column(length = 1024 * 32)
  private String note;

  @Column(name = "ref_quantity")
  private double refQuantity;

  @Column(name = "ref_unit_price")
  private double refUnitPrice;

  @Column(name = "ref_unit")
  private String refUnit;

  @Column(name = "ref_tax_rate")
  private double refTaxRate;

  @Column(name = "ref_total")
  private double refTotal;

  @Column(name = "ref_total_tax")
  private double refTotalTax;

  @Column(name = "ref_final_charge")
  private double refFinalCharge;

  @Column(name = "ref_currency")
  private String refCurrency;

  @Column(name="sea_fcl_charge_id", updatable=false, insertable=false)
  private Long seaFclChargeId;

  @Column(name="sea_lcl_charge_id", updatable=false, insertable=false)
  private Long seaLclChargeId;

  public TransportAdditionalCharge(AdditionalCharge additionalCharge) {
    label = additionalCharge.getLabel();
    method = additionalCharge.getMethod();
    type = additionalCharge.getType();
    target = additionalCharge.getTarget();
    purpose = additionalCharge.getPurpose();

    quantity = additionalCharge.getQuantity();
    taxRate = additionalCharge.getTaxRate();
    unit = additionalCharge.getUnit();
    currency = additionalCharge.currency;

    customClearanceType = additionalCharge.getCustomClearanceType();
    transportationMode = additionalCharge.getTransportationMode();

    refQuantity = additionalCharge.quantity;
    refUnit = additionalCharge.unit;
    refTaxRate = additionalCharge.taxRate;
    refCurrency = additionalCharge.currency;
  }

  public TransportAdditionalCharge(String label) {
    this.label = label;
    this.name = label;
  }

  public TransportAdditionalCharge withLabel(String label) {
    this.label = label;
    return this;
  }

  public TransportAdditionalCharge withCurrency(Currency currency) {
    this.currency = currency.getName();
    return this;
  }

  public TransportAdditionalCharge withCurrency(String name) {
    this.currency = name;
    return this;
  }

  public Long getTransportPriceId() {
    Long chargeId = null;
    if(Objects.nonNull(seaFclChargeId)) chargeId = seaFclChargeId;
    if(Objects.nonNull(seaLclChargeId)) chargeId = seaLclChargeId;
    return chargeId;
  }
}