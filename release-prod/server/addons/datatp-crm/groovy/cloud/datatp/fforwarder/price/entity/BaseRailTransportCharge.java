package cloud.datatp.fforwarder.price.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.MappedSuperclass;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.ShareableCompanyEntity;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.util.bean.BeanUtil;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

import java.io.Serial;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@MappedSuperclass
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class BaseRailTransportCharge extends ShareableCompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  @NotNull
  protected String code;

  @NotNull
  protected String label = "N/A";

  @Column(name = "from_location_code")
  protected String fromLocationCode;

  @Column(name = "from_location_label")
  protected String fromLocationLabel;

  @Column(name = "to_location_code")
  protected String toLocationCode;

  @Column(name = "to_location_label")
  protected String toLocationLabel;

  @Column(name = "carrier_partner_id")
  protected Long carrierPartnerId;

  @Column(name = "carrier_label")
  protected String carrierLabel;

  @Enumerated(EnumType.STRING)
  @Column(name = "edit_mode")
  protected EditMode editMode = EditMode.VALIDATED;

  protected String currency;

  @Setter(value = AccessLevel.NONE)
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "valid_from")
  protected Date validFrom;

  @Setter(value = AccessLevel.NONE)
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "valid_to")
  protected Date validTo;

  @Column(length = 1024 * 32)
  protected String note;

  public void setCurrency(String currency) {
    if (StringUtil.isEmpty(currency)) this.currency = "VND";
    else this.currency = currency;
  }

  protected void copy(BaseRailTransportCharge other) {
    List<Field> fields = BeanUtil.getFields(BaseRailTransportCharge.class);
    BeanUtil.copyAllFields(this, other, fields);
  }

  public void setValidTo(Date validTo) {
    if(validTo == null) validTo = new Date();
    LocalDate localDate = validTo.toInstant()
      .atZone(ZoneId.systemDefault())
      .toLocalDate();
    LocalDateTime endOfDay = localDate.atTime(LocalTime.MAX);
    this.validTo = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
  }

  public void setValidFrom(Date validFrom) {
    if (validFrom == null) validFrom = new Date();
    LocalDate localDate = validFrom.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    LocalDateTime endOfDay = localDate.atTime(LocalTime.MIN);
    this.validFrom = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
  }


    public String identify() {
    return code;
  }
}