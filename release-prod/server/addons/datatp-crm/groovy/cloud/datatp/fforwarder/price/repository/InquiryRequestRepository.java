package cloud.datatp.fforwarder.price.repository;

import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import java.io.Serializable;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface InquiryRequestRepository extends JpaRepository<InquiryRequest, Serializable> {

  @Query("SELECT p FROM InquiryRequest p WHERE p.companyId = :companyId AND p.code = :code")
  InquiryRequest getByCode(@Param("companyId") Long companyId, @Param("code") String requestCode);

  @Query("SELECT p FROM InquiryRequest p WHERE p.companyId = :companyId")
  List<InquiryRequest> findByCompany(@Param("companyId") Long companyId);

  @Query("SELECT p FROM InquiryRequest p WHERE p.companyId = :companyId AND p.status = 'DONE' ORDER BY p.pricingDate ASC")
  List<InquiryRequest> findNoResponseInquiryRequests(@Param("companyId") Long companyId);

  @Modifying
  @Query("UPDATE InquiryRequest p SET p.status = 'NO_RESPONSE' WHERE p.id IN (:requestIds)")
  void updateToNoResponse(@Param("requestIds") List<Long> requestIds);


}