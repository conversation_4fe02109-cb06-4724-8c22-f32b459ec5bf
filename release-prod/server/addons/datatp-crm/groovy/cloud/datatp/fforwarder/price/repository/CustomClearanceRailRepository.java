package cloud.datatp.fforwarder.price.repository;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import cloud.datatp.fforwarder.price.entity.CustomClearanceRail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.data.db.entity.StorageState;

public interface CustomClearanceRailRepository extends JpaRepository<CustomClearanceRail, Serializable>{

  @Query("SELECT c FROM CustomClearanceRail c WHERE c.companyId = :companyId")
  List<CustomClearanceRail> findByCompanyId(@Param("companyId") Long companyId);

  @Modifying
  @Query("UPDATE CustomClearanceRail c SET c.storageState = :state WHERE c.id IN :ids")
  int updateStorageState(@Param("state") StorageState state, @Param("ids") List<Long> ids);
}