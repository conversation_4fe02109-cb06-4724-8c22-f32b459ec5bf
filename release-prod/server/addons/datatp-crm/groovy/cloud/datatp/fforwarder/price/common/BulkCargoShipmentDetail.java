package cloud.datatp.fforwarder.price.common;

import cloud.datatp.fforwarder.price.common.ShipmentDetail.Stackable;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;

import java.util.Date;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.text.DateUtil;

@Embeddable
@Getter
@Setter
@NoArgsConstructor
public class BulkCargoShipmentDetail {

  public enum StackableType {ON_DECK, UNDER_DECK}

  public enum BulkCargoType {
    BULK, PACKAGE, EQUIPMENTS, VEHICLE, STEEL_PRODUCT, LIVE_ANIMAL, SHIPPING_AGENCY;

    public String getCode() {
      return switch (this) {
        case BULK -> "BU";
        case PACKAGE -> "PA";
        case EQUIPMENTS -> "EQ";
        case VEHICLE -> "VE";
        case STEEL_PRODUCT -> "SP";
        case LIVE_ANIMAL -> "LA";
        case SHIPPING_AGENCY -> "SA";
      };
    }
  }

  @Enumerated(EnumType.STRING)
  @Column(name = "cargo_type", nullable = false)
  private BulkCargoType cargoType = BulkCargoType.BULK;

  @Column(name = "term_of_service")
  private String termOfService;

  @Column(name = "target_rate", length = 2 * 1024)
  private String targetRate;


  //remove this field
//  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
//  @Column(name = "cargo_ready_date")
//  private Date cargoReadyDate;

  // laycan
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "laydays_date")
  private Date laydaysDate; // laydays (thoi gian xep hang)

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "cancelling_date")
  private Date cancellingDate; // cancellingDate (ngay huy hop dong hang)

  @Column(name = "volume")
  private double volume;

  @Column(name = "quantity")
  private double quantity;

  private String unit;

  @Column(name = "stowage_factor")
  private double stowageFactor; //CBM/TNE

  @Column(name = "commodity", length = 1024 * 32)
  private String commodity;

  @Column(name = "desc_of_goods", length = 2 * 1024)
  private String descOfGoods;

  @Column(name = "stackable")
  @Enumerated(EnumType.STRING)
  private Stackable stackable;

  @Column(name = "stackable_type")
  @Enumerated(EnumType.STRING)
  private StackableType stackableType;

  @Column(name = "allow_combine")
  private boolean allowCombine;

  @Column(name = "cargo_proceeding")
  private String cargoProceeding;

  @Column(name = "load_rate", length = 1024)
  private String loadRate;

  @Column(name = "discharge_rate", length = 1024)
  private String dischargeRate;

}