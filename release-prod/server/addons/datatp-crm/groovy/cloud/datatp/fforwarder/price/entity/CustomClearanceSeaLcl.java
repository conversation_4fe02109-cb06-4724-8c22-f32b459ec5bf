package cloud.datatp.fforwarder.price.entity;

import cloud.datatp.fforwarder.settings.commodity.entity.CommodityType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.partner.entity.Partner;
import net.datatp.module.resource.location.entity.Location;
import net.datatp.module.settings.currency.entity.Currency;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Entity
@Table(
  name = CustomClearanceSeaLcl.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = CustomClearanceSeaLcl.TABLE_NAME + "_code",
      columnNames = { "company_id", "code" }) },
  indexes = {
    @Index(columnList = "code")
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class CustomClearanceSeaLcl extends BaseCustomClearance {
  public static final String TABLE_NAME = "lgc_price_cc_sea_lcl";

  @NotNull
  private String code;

  @Column(name = "carrier_partner_id")
  private Long carrierPartnerId;

  @Column(name = "carrier_label")
  private String carrierLabel;

  @ManyToMany(cascade = {CascadeType.MERGE})
  @JoinTable(
    name = "lgc_price_cc_sea_lcl_commodity_type_rel",
    joinColumns = {
      @JoinColumn(name = "cc_sea_lcl_id", referencedColumnName = "id"),
      @JoinColumn(name = "company_id", referencedColumnName = "company_id"),
    },
    inverseJoinColumns = @JoinColumn(name = "commodity_type_id"),
    uniqueConstraints = {
      @UniqueConstraint(
        name = "lgc_price_cc_sea_lcl_commodity_type_rel_id_commodity_type",
        columnNames = { "cc_sea_lcl_id", "commodity_type_id" })
    }
  )
  private Set<CommodityType> commodityTypes;

  @OneToMany(cascade = CascadeType.ALL)
  @JoinColumn(name = "cc_sea_lcl_id", referencedColumnName = "id")
  private List<TransportAdditionalCharge> additionalCharges;

  @OneToMany(cascade = CascadeType.ALL)
  @JoinColumn(name = "cc_sea_lcl_id", referencedColumnName = "id")
  private List<CommissionDistribution> commissionDistributions;

  @OneToMany(cascade = CascadeType.ALL)
  @JoinColumn(name = "cc_sea_lcl_id", referencedColumnName = "id")
  private List<TransportCustomClearanceSurCharge> transportCustomClearanceSurCharges;

  private double price;

  public CustomClearanceSeaLcl(Partner carrier, Location terminal) {
    Objects.assertNotNull(carrier);
    Objects.assertNotNull(terminal);
    this.terminalLocationLabel = terminal.getLabel();
    this.terminalLocationCode = terminal.getCode();
    this.carrierPartnerId   = carrier.getId();
    this.carrierLabel   = carrier.getLabel();
    StringBuilder b = new StringBuilder("SeaLCL");
    b.append("-").append(terminal.getCode());
    b.append("-").append(carrier.getLoginId());
    this.code = b.append("-").append(DateUtil.asCompactDateTimeId(new Date())).toString();
  }

  public CustomClearanceSeaLcl withCurrency(Currency currency) {
    this.currency = currency.getName();
    return this;
  }

  public CustomClearanceSeaLcl withCurrency(String name) {
    this.currency = name;
    return this;
  }

  public CustomClearanceSeaLcl withPrivateShareableScope() {
    super.withPrivateShareableScope();
    return this;
  }

  public CustomClearanceSeaLcl withEditMode(EditMode editMode) {
    setEditMode(editMode);
    return this;
  }

  @Override
  public void set(ClientInfo client, Company company) {
    super.set(client, company);
    set(client, company, additionalCharges);
    set(client, company, commissionDistributions);
    set(client, company, transportCustomClearanceSurCharges);
  }
}