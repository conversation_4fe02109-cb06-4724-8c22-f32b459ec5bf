package cloud.datatp.fforwarder.price;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.fforwarder.price.common.ExcelPriceParserResult;
import cloud.datatp.fforwarder.price.entity.BulkCargoInquiryRequest;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.price.http.Params.SaveModifiedPrice;
import cloud.datatp.fforwarder.price.http.Params.ShareableUpdate;
import cloud.datatp.fforwarder.price.http.Params.XlsxPriceParserReq;
import cloud.datatp.fforwarder.settings.TransportationMode;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;

@Service("TransportPriceMiscService")
public class TransportPriceMiscService extends BaseComponent {

  @Autowired
  private InquiryRequestLogic requestLogic;
  
  @Autowired
  private BulkCargoInquiryRequestLogic bulkCargoInquiryRequestLogic;

  @Autowired
  private SeaPriceLogic seaPriceLogic;

  @Autowired
  private TruckPriceLogic truckPriceLogic;

  @Autowired
  private AirPriceLogic airPriceLogic;

  @Autowired
  private InputDataPriceProcessor excelProcessor;

  // ------------- price check request --------------
  @Transactional(readOnly = true)
  public InquiryRequest getInquiryRequest(ClientInfo client, Company company, Long requestId) {
    return requestLogic.getInquiryRequest(client, company, requestId);
  }

  @Transactional(readOnly = true)
  public InquiryRequest initInquiryRequest(ClientInfo client, Company company, InquiryRequest request) {
    return requestLogic.initInquiryRequest(client, company, request);
  }

  @Transactional
  public List<MapObject> saveInquiryRequestRecords(ClientInfo client, Company company, List<MapObject> records) {
    return requestLogic.saveInquiryRequestRecords(client, company, records);
  }

  @Transactional
  public InquiryRequest sendInquiryRequest(ClientInfo client, Company company, InquiryRequest request) {
    return requestLogic.sendInquiryRequest(client, company, request);
  }

  @Transactional
  public InquiryRequest saveInquiryRequest(ClientInfo client, Company company, InquiryRequest request) {
    return requestLogic.saveInquiryRequest(client, company, request);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchInquiryRequests(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    return requestLogic.searchInquiryRequests(client, company, sqlParams);
  }
  
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchInquiryRequestSpace(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    List<SqlMapRecord> records = new ArrayList<>();
    records.addAll(requestLogic.searchInquiryRequestSpace(client, company, sqlParams));
    records.addAll(bulkCargoInquiryRequestLogic.searchBulkCargoInquiryRequestSpace(client, company, sqlParams));
    records.sort((a, b) -> {
      try {
        Date dateA = a.getDate("requestDate", null);
        Date dateB = b.getDate("requestDate", null);
        if(dateA == null) return 0;
        if(dateB == null) return 1;
        return dateB.compareTo(dateA);
      } catch (Exception e) {
        return 0;
      }
    });
    return records;
  }

  @Transactional(readOnly = true)
  public List<InquiryRequest> findRequestByCompany(ClientInfo client, Company company) {
    return requestLogic.findByCompany(client, company);
  }

  @Transactional(readOnly = true)
  public List<InquiryRequest> findNoResponseInquiryRequests(ClientInfo client, Company company) {
    return requestLogic.findNoResponseInquiryRequests(client, company);
  }

  @Transactional
  public void updateToNoResponse(ClientInfo client, Company company, List<Long> requestIds) {
    requestLogic.updateToNoResponse(client, company, requestIds);
  }

  // --------------------------------- --------------

  @Transactional
  public int updateShareableScope(ClientInfo client, Company company, ShareableUpdate shareableUpdate) {
    return requestLogic.updateShareableScope(client, company, shareableUpdate);
  }

  @Transactional
  public List<MapObject> saveModifiedPrice(ClientInfo client, Company company, SaveModifiedPrice saveModified) {
    final TransportationMode mode = saveModified.getMode();
    if (TransportationMode.isSeaLCLTransport(mode)) {
      return seaPriceLogic.saveSeaLclModifyRecords(client, company, saveModified.getRecords());
    } else if (TransportationMode.isSeaFCLTransport(mode)) {
      return seaPriceLogic.saveSeaFclTransportCharges(client, company, saveModified.getRecords());
    } else if (TransportationMode.isAirTransport(mode)) {
      return airPriceLogic.saveAirTransportCharges(client, company, saveModified.getRecords());
    } else if (TransportationMode.isTruckContainer(mode)) {
      return truckPriceLogic.saveTruckContainerCharges(client, company, saveModified.getRecords());
    } else if (TransportationMode.isTruckRegular(mode)) {
      return truckPriceLogic.saveTruckRegulars(client, company, saveModified.getRecords());
    } else {
      throw RuntimeError.IllegalArgument("Unsupported mode!!!");
    }
  }

  @Transactional
  public ExcelPriceParserResult xlsxPriceParser(ClientInfo client, Company company, XlsxPriceParserReq req) {
    return excelProcessor.xlsxPriceParser(client, company, req);
  }

  @Transactional
  public boolean deleteInquiryRequests(ClientInfo client, Company company, List<Long> ids) {
    return requestLogic.deleteInquiryRequests(client, company, ids);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> inquiryRequestReport(ClientInfo client, Company company, SqlQueryParams params) {
    return requestLogic.inquiryRequestReport(client, company, params);
  }
  
//Bulk Cargo inquiry request
  @Transactional(readOnly = true)
  public BulkCargoInquiryRequest getBulkCargoInquiryRequest(ClientInfo client, Company company, Long requestId) {
    return bulkCargoInquiryRequestLogic.getBulkCargoInquiryRequest(client, company, requestId);
  }

  @Transactional(readOnly = true)
  public BulkCargoInquiryRequest initBulkCargoInquiryRequest(ClientInfo client, Company company, BulkCargoInquiryRequest request) {
    return bulkCargoInquiryRequestLogic.initBulkCargoInquiryRequest(client, company, request);
  }
  
  @Transactional
  public BulkCargoInquiryRequest sendBulkCargoInquiryRequest(ClientInfo client, Company company, BulkCargoInquiryRequest request) {
    return bulkCargoInquiryRequestLogic.sendBulkCargoInquiryRequest(client, company, request);
  }
  
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchBulkCargoInquiryRequests(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    return bulkCargoInquiryRequestLogic.searchBulkCargoInquiryRequests(client, company, sqlParams);
  }
  
  @Transactional
  public  List<MapObject> saveBulkCargoInquiryRequestRecords(ClientInfo client, Company company, List<MapObject> records) {
    return bulkCargoInquiryRequestLogic.saveBulkCargoInquiryRequestRecords(client, company, records);
  }
  
  @Transactional
  public boolean deleteBulkCargoInquiryRequests(ClientInfo client, Company company, List<Long> ids) {
    return bulkCargoInquiryRequestLogic.deleteBulkCargoInquiryRequests(client, company, ids);
  }
}