package cloud.datatp.fforwarder.price.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.module.data.db.query.RangeFilter
import net.datatp.util.ds.MapObject
import net.datatp.util.text.DateUtil
import net.datatp.util.text.StringUtil
import org.springframework.context.ApplicationContext

class SeaFCLPriceSql extends Executor {
  public class SearchSeaFCLPrice extends ExecutableSqlBuilder {
    
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");

      RangeFilter filter = (RangeFilter) sqlParams.get("validTo");

      RangeFilter createdTime = (RangeFilter) sqlParams.get("createdTime");

      String filterByDate = ""
      if(filter != null) {

        if (StringUtil.isNotEmpty(filter.getFromValue())) {
          sqlParams.put("validToFrom", DateUtil.parseCompactDateTime(filter.getFromValue()));
          filterByDate = " AND (charge.valid_to >= :validToFrom)"
        }
        
        if (StringUtil.isNotEmpty(filter.getToValue())) {
          sqlParams.put("validToTo", DateUtil.parseCompactDateTime(filter.getToValue()));
          filterByDate += " AND (charge.valid_to <= :validToTo)"
        }

      } else if(createdTime != null) {
        if (StringUtil.isNotEmpty(createdTime.getFromValue())) {
          sqlParams.put("createdTimeFrom", DateUtil.parseCompactDateTime(createdTime.getFromValue()));
          filterByDate = " AND (charge.created_time >= :createdTimeFrom)"
        }

        if (StringUtil.isNotEmpty(createdTime.getToValue())) {
          sqlParams.put("createdTimeTo", DateUtil.parseCompactDateTime(createdTime.getToValue()));
          filterByDate += " AND (charge.created_time <= :createdTimeTo)"
        }
      } else {
        filterByDate = " AND DATE(charge.valid_to) >= CURRENT_DATE";
      }

      String orderBy = """
                ORDER BY charge.created_time DESC, charge.valid_to DESC 
      """

      if(!isNotContainKey(sqlParams, "pricingCreatorId")) {
        orderBy = """ ORDER BY charge.modified_time DESC """
      }
      
      String query = """
                WITH add_charge_data AS (
                    SELECT sea_fcl_charge_id,
                          json_agg(
                            json_build_object(
                              'id', id, 
                              'label', label, 
                              'unit', unit, 
                              'finalCharge', final_charge,
                              'note', note
                              )) AS additional_charges
                    FROM lgc_price_transport_additional_charge
                    GROUP BY sea_fcl_charge_id
                )
                SELECT charge.*,
                       charge.cut_off_time          AS cutoff,
                       charge.depart_time           AS etd,
                       add.additional_charges       AS additional_charges
                FROM lgc_price_sea_fcl_charge AS charge
                  LEFT JOIN add_charge_data AS add ON charge.id = add.sea_fcl_charge_id
                WHERE ${FILTER_BY_STORAGE_STATE('charge', sqlParams)}  
                  ${AND_FILTER_BY_PARAM('charge.purpose', 'purpose', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.group_type', 'groupType', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.from_location_code', 'fromLocationCode', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.to_location_code', 'toLocationCode', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.carrier_label', 'carrierLabel', sqlParams)}
                  ${AND_FILTER_BY_RANGE('charge.modified_time', 'modifiedTime', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.assignee_account_id', 'pricingCreatorId', sqlParams)}
                  AND (
                    (charge.shareable = 'COMPANY' AND charge.company_id = :companyId) OR 
                    (
                      charge.shareable = 'DESCENDANTS' AND charge.company_id IN (
                        SELECT 
                            distinct cc.id 
                        FROM company_company cc 
                        WHERE cc.parent_id IN (:companyIds) OR cc.id IN (:companyIds)
                    )) OR
                    (charge.shareable = 'ORGANIZATION')
                  )
                  ${filterByDate}
                ${orderBy}
                ${MAX_RETURN(sqlParams)}
              """;
      return query;
    }
  }
  public SeaFCLPriceSql() {
    register(new SearchSeaFCLPrice());
  }
}