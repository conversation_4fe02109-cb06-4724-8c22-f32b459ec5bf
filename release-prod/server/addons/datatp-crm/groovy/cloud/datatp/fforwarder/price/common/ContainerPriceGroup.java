package cloud.datatp.fforwarder.price.common;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.ds.MapObject;

@Embeddable
@Getter
@Setter
@NoArgsConstructor
public class ContainerPriceGroup {

  @Column(name = "cont_dry_20_less_10_ton_price")
  protected double contDry20Lt10TonPrice;

  @Column(name = "cont_dry_20_geq_10_ton_price")
  protected double contDry20Geq10TonPrice;

  @Column(name = "cont_dry_20_geq_17_ton_price")
  protected double contDry20Geq17TonPrice;

  @Column(name = "cont_dry_40_less_17_ton_price")
  protected double contDry40Lt17TonPrice;

  @Column(name = "cont_dry_40_geq_17_ton_price")
  protected double contDry40Geq17TonPrice;

  // High Cube container (HC)
  @Column(name = "cont_high_cube_20_less_17_ton_price")
  protected double contHighCube20Lt17TonPrice;

  @Column(name = "cont_high_cube_20_geq_17_ton_price")
  protected double contHighCube20Geq17TonPrice;

  @Column(name = "cont_high_cube_40_less_17_ton_price")
  protected double contHighCube40Lt17TonPrice;

  @Column(name = "cont_high_cube_40_geq_17_ton_price")
  protected double contHighCube40Geq17TonPrice;

  // Reefer container (RF)
  @Column(name = "cont_reefer_20_less_17_ton_price")
  protected double contReefer20Lt17TonPrice;

  @Column(name = "cont_reefer_20_geq_17_ton_price")
  protected double contReefer20Geq17TonPrice;

  @Column(name = "cont_reefer_40_less_17_ton_price")
  protected double contReefer40Lt17TonPrice;

  @Column(name = "cont_reefer_40_geq_17_ton_price")
  protected double contReefer40Geq17TonPrice;

  // Open Top Container (OT)
  @Column(name = "cont_open_top_20_less_17_ton_price")
  protected double contOpenTop20Lt17TonPrice;

  @Column(name = "cont_open_top_20_geq_17_ton_price")
  protected double contOpenTop20Geq17TonPrice;

  @Column(name = "cont_open_top_40_less_17_ton_price")
  protected double contOpenTop40Lt17TonPrice;

  @Column(name = "cont_open_top_40_geq_17_ton_price")
  protected double contOpenTop40Geq17TonPrice;

  // Flat rack Container (FR)
  @Column(name = "cont_flat_rack_20_less_17_ton_price")
  protected double contFlatRack20Lt17TonPrice;

  @Column(name = "cont_flat_rack_20_geq_17_ton_price")
  protected double contFlatRack20Geq17TonPrice;

  @Column(name = "cont_flat_rack_40_less_17_ton_price")
  protected double contFlatRack40Lt17TonPrice;

  @Column(name = "cont_flat_rack_40_geq_17_ton_price")
  protected double contFlatRack40Geq17TonPrice;

  // Tank Container (Tank)
  @Column(name = "cont_tank_20_less_17_ton_price")
  protected double contTank20Lt17TonPrice;

  @Column(name = "cont_tank_20_geq_17_ton_price")
  protected double contTank20Geq17TonPrice;

  @Column(name = "cont_tank_40_less_17_ton_price")
  protected double contTank40Lt17TonPrice;

  @Column(name = "cont_tank_40_geq_17_ton_price")
  protected double contTank40Geq17TonPrice;

  // --------------- For Cross Border Trucking Price --------------------------
  // High Cube container (HC)
  @Column(name = "cont_high_cube_40_price")
  protected double contHighCube40Price;

  @Column(name = "cont_high_cube_45_price")
  protected double contHighCube45Price;

  // Reefer container (RF)
  @Column(name = "cont_reefer_40_price")
  protected double contReefer40Price;

  @Column(name = "cont_reefer_45_price")
  protected double contReefer45Price;

  // Custom Clearance Fee
  @Column(name = "custom_fee_at_lang_son")
  protected double customFeeAtLangSon;

  @Column(name = "custom_fee_at_pin_xiang")
  protected double customFeeAtPinXiang;

  public ContainerPriceGroup mapFrom(MapObject record) {
    setContDry20Lt10TonPrice(record.getDouble("contDry20Lt10TonPrice", 0d));
    setContDry20Geq10TonPrice(record.getDouble("contDry20Geq10TonPrice", 0d));
    setContDry20Geq17TonPrice(record.getDouble("contDry20Geq17TonPrice", 0d));
    setContDry40Lt17TonPrice(record.getDouble("contDry40Lt17TonPrice", 0d));
    setContDry40Geq17TonPrice(record.getDouble("contDry40Geq17TonPrice", 0d));

    setContHighCube20Lt17TonPrice(record.getDouble("contHighCube20Lt17TonPrice", 0d));
    setContHighCube20Geq17TonPrice(record.getDouble("contHighCube20Geq17TonPrice", 0d));
    setContHighCube40Lt17TonPrice(record.getDouble("contHighCube40Lt17TonPrice", 0d));
    setContHighCube40Geq17TonPrice(record.getDouble("contHighCube40Geq17TonPrice", 0d));

    setContReefer20Lt17TonPrice(record.getDouble("contReefer20Lt17TonPrice", 0d));
    setContReefer20Geq17TonPrice(record.getDouble("contReefer20Geq17TonPrice", 0d));
    setContReefer40Lt17TonPrice(record.getDouble("contReefer40Lt17TonPrice", 0d));
    setContReefer40Geq17TonPrice(record.getDouble("contReefer40Geq17TonPrice", 0d));

    setContOpenTop20Lt17TonPrice(record.getDouble("contOpenTop20Lt17TonPrice", 0d));
    setContOpenTop20Geq17TonPrice(record.getDouble("contOpenTop20Geq17TonPrice", 0d));
    setContOpenTop40Lt17TonPrice(record.getDouble("contOpenTop40Lt17TonPrice", 0d));
    setContOpenTop40Geq17TonPrice(record.getDouble("contOpenTop40Geq17TonPrice", 0d));

    setContFlatRack20Lt17TonPrice(record.getDouble("contFlatRack20Lt17TonPrice", 0d));
    setContFlatRack20Geq17TonPrice(record.getDouble("contFlatRack20Geq17TonPrice", 0d));
    setContFlatRack40Lt17TonPrice(record.getDouble("contFlatRack40Lt17TonPrice", 0d));
    setContFlatRack40Geq17TonPrice(record.getDouble("contFlatRack40Geq17TonPrice", 0d));

    setContTank20Lt17TonPrice(record.getDouble("contTank20Lt17TonPrice", 0d));
    setContTank20Geq17TonPrice(record.getDouble("contTank20Geq17TonPrice", 0d));
    setContTank40Lt17TonPrice(record.getDouble("contTank40Lt17TonPrice", 0d));
    setContTank40Geq17TonPrice(record.getDouble("contTank40Geq17TonPrice", 0d));

    setContHighCube40Price(record.getDouble("contHighCube40Price", 0d));
    setContHighCube45Price(record.getDouble("contHighCube45Price", 0d));
    setContReefer40Price(record.getDouble("contReefer40Price", 0d));
    setContReefer45Price(record.getDouble("contReefer45Price", 0d));
    setCustomFeeAtLangSon(record.getDouble("customFeeAtLangSon", 0d));
    setCustomFeeAtPinXiang(record.getDouble("customFeeAtPinXiang", 0d));

    return this;
  }

}