package cloud.datatp.fforwarder.price.calculator;

import java.util.List;

import cloud.datatp.fforwarder.price.entity.TransportCustomClearanceSurCharge;

import net.datatp.util.ds.Collections;

public class CustomClearanceSurChargeCalculator {
  
  public static void calculate(List<TransportCustomClearanceSurCharge> surCharges) {
    if(Collections.isEmpty(surCharges)) return;
    for(TransportCustomClearanceSurCharge surCharge : surCharges) {
      double total = surCharge.getQuantity() * surCharge.getUnitPrice();
      double totalTax = total * surCharge.getTaxRate();
      
      surCharge.setTotalTax(totalTax);
      surCharge.setTotal(total);
      surCharge.setFinalCharge(total + totalTax);
    }
  }
}