package cloud.datatp.fforwarder.price;

import org.springframework.beans.factory.annotation.Autowired;

import cloud.datatp.fforwarder.price.common.BaseTransportCharge;
import cloud.datatp.fforwarder.price.common.BaseTruckTransportCharge;
import cloud.datatp.fforwarder.settings.commodity.CommodityTypeLogic;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import net.datatp.module.company.CompanyReadLogic;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.http.upload.UploadService;
import net.datatp.module.partner.PartnerLogic;
import net.datatp.module.resource.location.LocationLogic;

@Getter
public class TransportPriceLogic extends DAOService {
  protected String QUERY_SCRIPT_DIR;

  @Autowired
  protected CompanyReadLogic companyLogic;

  @Autowired
  protected SeqService seqService;

  @Autowired
  protected EmployeeLogic employeeLogic;

  @Autowired
  protected LocationLogic locationLogic;

  @Autowired
  protected CommodityTypeLogic commodityLogic;

  @Autowired
  protected PartnerLogic partnerLogic;

  @Autowired
  protected UploadService uploadService;

  @PostConstruct
  public void onInit() {
    if ("test".equals(env)) return;
    QUERY_SCRIPT_DIR = appEnv.addonPath("logistics", "groovy/lgc/forwarder/price/");
  }

  public <T extends BaseTransportCharge> String generateTransportPriceCode(T price) {
    StringBuilder builder = new StringBuilder(price.genCodePrefix());
    String sequence = String.format("%04d", seqService.nextSequence(price.getSequence()));
    builder.append(sequence);
    return builder.toString();
  }

  public <T extends BaseTruckTransportCharge> String generateTransportPriceCode(T price) {
    StringBuilder builder = new StringBuilder("T");
    String sequence = String.format("%04d", seqService.nextSequence("lgc:lgc_price_truck_container_charge"));
    builder.append(sequence);
    return builder.toString();
  }

}