package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.price.entity.AdditionalCharge;
import cloud.datatp.fforwarder.price.entity.CustomClearanceType;
import cloud.datatp.fforwarder.price.repository.AdditionalChargeRepository;
import cloud.datatp.fforwarder.settings.TransportationMode;
import groovy.lang.Binding;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.ShareableUtil;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryManager;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.List;

@Component
public class AdditionalChargeLogic extends DAOService {

  @Autowired
  private AdditionalChargeRepository repo;

  @Autowired
  private DataSource dataSource;

  public AdditionalCharge getAdditionalChargeByName(
    ClientInfo client, Company company, String name, TransportationMode mode) {
    return repo.getByName(company.getId(), name, mode);
  }

  public AdditionalCharge getAdditionalChargeByName(ClientInfo client, Company company, String name, CustomClearanceType type) {
    return repo.getByName(company.getId(), name, type);
  }

  public AdditionalCharge getAdditionalChargeById(ClientInfo client, Company company, Long id) {
    final String loginId = client.getRemoteUser();
    final List<Long> companyIds = company.findCompanyIdPaths();
    return repo.getById(company.getId(), companyIds, loginId, id);
  }
  
  public AdditionalCharge saveAdditionalCharge(ClientInfo client, Company company, AdditionalCharge additionalCharge) {
    additionalCharge.set(client, company);
    return repo.save(additionalCharge);
  }

  public List<MapObject> saveAdditionalCharges(ClientInfo client, Company company, List<MapObject> additionalCharges) {
    for (MapObject charge : additionalCharges) {
      final Long id = charge.getLong("id", null);
      AdditionalCharge additionalCharge = new AdditionalCharge();
      if(id != null) {
        additionalCharge = getAdditionalChargeById(client, company, id);
        Objects.assertNotNull(additionalCharge, "Additional Charge not found: id = " + id);
      }
      additionalCharge = additionalCharge.computeFromMapObject(charge);
      AdditionalCharge updated = saveAdditionalCharge(client, company, additionalCharge);
      charge.put("id", updated.getId());
    }
    return additionalCharges;
  }

  public List<AdditionalCharge> findAdditionalChargesByCompany(ClientInfo client, Company company) {
    return repo.findByCompany(company.getId());
  }

  public List<SqlMapRecord> searchAdditionalCharge(ClientInfo client, Company company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/AdditionalChargeSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchAdditionalCharge", params);
  }


  public boolean changeAdditionalTransportStorageState(ClientInfo client, ChangeStorageStateRequest request) {
    List<Long> chargeIds = request.getEntityIds();
    if (chargeIds.isEmpty()) throw RuntimeError.IllegalArgument("No Records were selected!!");
    repo.setStorageState(chargeIds, request.getNewStorageState());
    return true;
  }

  public boolean deleteAdditionalTransportCharges(ClientInfo client, Company company, List<Long> ids) {
    if (Collections.isEmpty(ids)) return false;
    DBConnectionUtil connectionUtil = new DBConnectionUtil(getJdbcDataSource());
    DeleteGraphBuilder graph = new DeleteGraphBuilder(connectionUtil, company.getId(), AdditionalCharge.class, ids);
    int count = graph.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count > 0;
  }
}