package cloud.datatp.fforwarder.price.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject

class TruckPriceSql extends Executor {

    public class SearchTruckPrice extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
                SELECT  
                  charge.id                                     AS id,
                  charge.code                                   AS code,
                  charge.carrier_route                          AS carrier_route,
                  
                  charge.pickup_location_code                   AS pickup_location_code,
                  charge.pickup_location_label                  AS pickup_location_label,
                  charge.pickup_address                         AS pickup_address,
                  charge.delivery_location_code                 AS delivery_location_code,
                  charge.delivery_location_label                AS delivery_location_label,
                  charge.delivery_address                       AS delivery_address,
                  charge.delivery_address_normalized            AS delivery_address_normalized,
                  charge.priority                               AS priority,
                  
                  charge.target_reference_id                    AS target_reference_id,
                  charge.target_reference_label                 AS target_reference_label,
                  charge.target_customer                        AS target_customer,
                  
                  charge.carrier_partner_id                     AS carrier_partner_id,
                  charge.carrier_label                          AS carrier_label,
                  charge.assignee_account_id                    AS assignee_account_id,
                  charge.assignee_label                         AS assignee_label,
                  charge.km_2_way                               AS km2way,
                  charge.oil_price_in_effect                    AS oil_price_in_effect,
            
                  -- Tariff
                  charge.truck1_ton25_price                     AS truck1_ton25_price,
                  charge.truck1_ton5_price                      AS truck1_ton5_price,
                  charge.truck2_ton5_price                      AS truck2_ton5_price,
                  charge.truck3_ton5_price                      AS truck3_ton5_price,
                  charge.truck5_ton_price                       AS truck5_ton_price,
                  charge.truck7_ton_price                       AS truck7_ton_price,
                  charge.truck8_ton_price                       AS truck8_ton_price,
                  charge.truck9_ton_price                       AS truck9_ton_price,
                  charge.truck10_ton_price                      AS truck10_ton_price,
                  charge.truck12_ton_price                      AS truck12_ton_price,
                  charge.truck15_ton_price                      AS truck15_ton_price,
                  charge.truck30_ton_price                      AS truck30_ton_price,
                  -- End Tariff
            
                  charge.currency                               AS currency,
                  charge.valid_from                             AS valid_from,
                  charge.valid_to                               AS valid_to,
                  charge.note                                   AS note,
            
                  charge.storage_state                          AS storage_state,
                  charge.edit_mode                              AS edit_mode,
                  charge.shareable                              AS shareable,
                  charge.created_by                             AS created_by,
                  charge.created_time                           AS created_time,
                  charge.modified_by                            AS modified_by,
                  charge.modified_time                          AS modified_time,                            
                  charge.version                                AS version,                            
                  charge.company_id                             AS company_id,                            
                  company.label                                 AS company_label
                FROM lgc_price_truck_regular_charge AS charge
                  JOIN company_company AS company ON charge.company_id = company.id
                WHERE
                  ${FILTER_BY_STORAGE_STATE('charge', sqlParams)}
                  AND charge.is_cbt_transportation IS FALSE 
                  ${AND_FILTER_BY_PARAM('charge.pickup_location_code', 'pickupLocationCode', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.delivery_location_code', 'deliveryLocationCode', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.assignee_account_id', 'pricingCreatorId', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.carrier_partner_id', 'carrierPartnerId', sqlParams)}
                  ${AND_FILTER_BY_RANGE('charge.modified_time', 'modifiedTime', sqlParams)}
                  AND (
                    (charge.shareable = 'COMPANY' AND charge.company_id = :companyId) OR 
                    (
                        charge.shareable = 'DESCENDANTS' AND charge.company_id IN (
                            SELECT 
                                distinct cc.id 
                            FROM company_company cc 
                            WHERE cc.parent_id IN (:companyIds) OR cc.id IN (:companyIds)
                    )) OR
                    (charge.shareable = 'ORGANIZATION')
                  )
                ORDER BY
                  charge.delivery_address_normalized ASC,
                  charge.pickup_location_code, charge.delivery_location_code, 
                  charge.valid_to, charge.modified_time, charge.created_time DESC
                ${MAX_RETURN(sqlParams)}
              """;
            return query;
        }
    }

    public TruckPriceSql() {
        register(new SearchTruckPrice());
    }
}