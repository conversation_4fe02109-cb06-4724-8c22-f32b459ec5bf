package cloud.datatp.fforwarder.price.request;

import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.settings.message.MailMessageProvider;
import cloud.datatp.fforwarder.settings.message.MessageServicePlugin;
import cloud.datatp.fforwarder.settings.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.settings.message.entity.MessageStatus;
import cloud.datatp.fforwarder.settings.message.entity.MessageType;
import java.util.Date;
import java.util.HashSet;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class InquiryUpdatedMessagePlugin extends MessageServicePlugin {
  public static final String PLUGIN_TYPE = "inquiry-update-status-reminder";

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private MailMessageProvider mailMessageProvider;

  @Autowired
  private InquiryRequestLogic inquiryRequestLogic;

  protected InquiryUpdatedMessagePlugin() {
    super(PLUGIN_TYPE);
  }

  public void onPostSend(ClientInfo client, CRMMessageSystem message) {
  }

  public void onSendError(ClientInfo client, CRMMessageSystem message, Exception error) throws Exception {
    if(message.getStatus() != MessageStatus.CANCELLED && message.getMessageType() == MessageType.ZALO) {
      Objects.assertNotNull(message.getReferenceType(), "Message has no reference type");
      Objects.assertNotNull(message.getReferenceId(), "Message has no reference id");
      if (!InquiryRequest.TABLE_NAME.equals(message.getReferenceType())) {
        throw new RuntimeException("Message reference type is not InquiryRequest: " + message.getReferenceType());
      }
      Company company = companyLogic.getCompany(client, message.getCompanyId());
      InquiryRequest request = inquiryRequestLogic.getInquiryRequest(client, company, message.getReferenceId());
      Objects.assertNotNull(request, "Inquiry request {} is not found", message.getReferenceId());
      Objects.assertNotNull(company, "Company {} is not found", message.getCompanyId());
      CRMMessageSystem mailMessage = new CRMMessageSystem();
      mailMessage.setCompanyId(message.getCompanyId());
      mailMessage.setReferenceId(message.getReferenceId());
      mailMessage.setReferenceType(message.getReferenceType());
      mailMessage.setMessageType(MessageType.MAIL);
      mailMessage.setContent("<div>Test</div>");
      mailMessage.setPluginName(PLUGIN_TYPE);
      mailMessage.setRecipients(new HashSet<>(request.getToList()));
      mailMessage.setScheduledAt(new Date());
      MapObject metadata = new MapObject();
      metadata.put("fromEmail", "<EMAIL>");
      mailMessage.setMetadata(metadata);
      mailMessageProvider.send(client, mailMessage);
    }
  }

}