package cloud.datatp.fforwarder.price.repository;

import cloud.datatp.fforwarder.price.entity.RailTransportCharge;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.entity.StorageState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Repository
public interface RailTransportChargeRepository extends JpaRepository<RailTransportCharge, Serializable> {

  @Query("SELECT a FROM RailTransportCharge a WHERE a.companyId = :companyId")
  List<RailTransportCharge> findByCompanyId(@Param("companyId") Long companyId);

  @Modifying
  @Query("UPDATE RailTransportCharge t SET t.storageState = :storageState WHERE t.id IN :ids ")
  int updateStorageState(@Param("storageState") StorageState state, @Param("ids") List<Long> ids);

  @Query("SELECT charge FROM RailTransportCharge charge WHERE charge.id IN (:ids)")
  List<RailTransportCharge> findByIds(@Param("ids") List<Long> ids);
}