package cloud.datatp.fforwarder.price.common;

import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaLclTransportCharge;
import net.datatp.module.account.entity.Account;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

public class SeaNotificationTemplate {

  public static String buildMailMessage(Object charge, Account saleAccount, String feedback) {
    String mode;
    String detailsHtml;
    String pricingLabel;

    if (charge.getClass().equals(SeaFclTransportCharge.class)) {
      mode = "SEA FCL";
      detailsHtml = buildSeaFclDetailsHtml((SeaFclTransportCharge) charge);
      pricingLabel = ((SeaFclTransportCharge) charge).getAssigneeLabel();
    } else {
      mode = "SEA LCL";
      detailsHtml = buildSeaLclDetailsHtml((SeaLclTransportCharge) charge);
      pricingLabel = ((SeaLclTransportCharge) charge).getAssigneeLabel();
    }

    return String.format("""
        <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 15px; background-color: #f9fafb;">
            <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h1 style="color: #1f2937; font-size: 20px; margin: 0 0 16px 0; padding-bottom: 12px; border-bottom: 1px solid #e5e7eb;">
                    PRICE CHECK REQUEST - %s
                </h1>
                <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                    <p style="margin: 0 0 8px 0; color: #374151;">
                        <strong style="color: #1f2937;">👨‍💼 Sales:</strong> %s
                    </p>
                    <p style="margin: 0; color: #374151;">
                        <strong style="color: #1f2937;">📧 Email:</strong> %s
                    </p>
                </div>
                %s
                <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                    <p style="margin: 0 0 8px 0; color: #374151;">
                        <strong style="color: #1f2937;">💬 Feedback:</strong> %s
                    </p>
                    <p style="margin: 0 0 8px 0; color: #374151;">
                        <strong style="color: #1f2937;">👨‍💼 Pricing By:</strong> %s
                    </p>
                    <p style="margin: 0; color: #374151;">
                        <strong style="color: #1f2937;">👤 Updated by:</strong> %s
                    </p>
                </div>
                <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <p style="color: #6b7280; font-size: 14px; margin: 0;">
                        This is an automated notification from the CRM Task Management System.
                    </p>
                </div>
            </div>
        </div>
        """,
      mode,
      saleAccount.getFullName(),
      saleAccount.getEmail(),
      detailsHtml,
      feedback,
      pricingLabel,
      saleAccount.getLoginId()
    );
  }

  private static String buildSeaFclDetailsHtml(SeaFclTransportCharge charge) {
    StringBuilder html = new StringBuilder();
    html.append("""
      <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
          <p style="margin: 0 0 8px 0; color: #374151;">
              <strong style="color: #1f2937;">📦 Price Details:</strong>
          </p>
          <ul style="margin: 0; padding-left: 20px; color: #374151;">
      """);

    String from = charge.getFromLocationLabel();
    String to = charge.getToLocationLabel();
    String finalTerm = charge.getFinalTerminalLocationLabel();

    String route = from + " - " + to + (StringUtil.isNotEmpty(finalTerm) ? " - " + finalTerm : "");
    html.append(String.format("<li>Route: %s</li>", route));


    if (StringUtil.isNotEmpty(charge.getCarrierLabel())) {
      html.append(String.format("<li>Carrier: %s</li>", charge.getCarrierLabel()));
    }

    if (StringUtil.isNotEmpty(charge.getHandlingAgentPartnerLabel())) {
      html.append(String.format("<li>Agent: %s</li>", charge.getHandlingAgentPartnerLabel()));
    }
    if (charge.getValidTo() != null) {
      html.append(String.format("<li>Valid Date: %s</li>", DateUtil.asCompactDate(charge.getValidTo())));
    }
    String remark = charge.getRemarks();
    String note = charge.getNote();
    if (StringUtil.isNotEmpty(remark) || StringUtil.isNotEmpty(note)) {
      html.append("<li>Remarks:");
      html.append("<br>");
      if (StringUtil.isNotEmpty(remark)) {
        html.append(remark).append("<br>");
      }
      if (StringUtil.isNotEmpty(note)) {
        html.append(note);
      }
      html.append("</li>");
    }
    html.append("</ul></div>");

    return html.toString();
  }

  private static String buildSeaLclDetailsHtml(SeaLclTransportCharge charge) {
    StringBuilder html = new StringBuilder();
    html.append("""
      <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
          <p style="margin: 0 0 8px 0; color: #374151;">
              <strong style="color: #1f2937;">📦 Price Details:</strong>
          </p>
          <ul style="margin: 0; padding-left: 20px; color: #374151;">
      """);

    String from = charge.getFromLocationLabel();
    String to = charge.getToLocationLabel();
    String route = from + " - " + to;
    html.append(String.format("<li>Route: %s</li>", route));


    if (StringUtil.isNotEmpty(charge.getCarrierLabel())) {
      html.append(String.format("<li>Carrier: %s</li>", charge.getCarrierLabel()));
    }

    if (StringUtil.isNotEmpty(charge.getHandlingAgentPartnerLabel())) {
      html.append(String.format("<li>Agent: %s</li>", charge.getHandlingAgentPartnerLabel()));
    }
    if (charge.getValidTo() != null) {
      html.append(String.format("<li>Valid Date: %s</li>", DateUtil.asCompactDate(charge.getValidTo())));
    }

    if (StringUtil.isNotEmpty(charge.getNote())) {
      html.append(String.format("<li>Remarks: %s</li>", charge.getNote()));
    }
    html.append("</ul></div>");

    return html.toString();
  }
}
