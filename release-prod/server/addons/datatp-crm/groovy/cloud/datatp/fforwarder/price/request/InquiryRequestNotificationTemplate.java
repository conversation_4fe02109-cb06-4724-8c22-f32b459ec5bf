package cloud.datatp.fforwarder.price.request;

import cloud.datatp.fforwarder.price.common.ShipmentDetail;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

public class InquiryRequestNotificationTemplate {

  public static String buildMailMessage(InquiryRequest request, String remoteUser) {
    ShipmentDetail shipmentDetail = request.getShipmentDetail();

    return String.format("""
            <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 15px; background-color: #f9fafb;">
                <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h1 style="color: #1f2937; font-size: 20px; margin: 0 0 16px 0; padding-bottom: 12px; border-bottom: 1px solid #e5e7eb;">
                        PRICE CHECK REQUEST - REF: %s
                    </h1>
                    <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">👨‍💼 Sales:</strong> %s
                        </p>
                        <p style="margin: 0; color: #374151;">
                            <strong style="color: #1f2937;">📧 Email:</strong> %s
                        </p>
                    </div>
                    %s
                    <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">📋 Subject:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">💬 Feedback:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">📝 Pricing Note:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">👨‍💼 Pricing By:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">📊 Status:</strong> %s
                        </p>
                        <p style="margin: 0; color: #374151;">
                            <strong style="color: #1f2937;">👤 Updated by:</strong> %s
                        </p>
                    </div>
                    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                        <p style="color: #6b7280; font-size: 14px; margin: 0;">
                            This is an automated notification from the CRM Task Management System.
                        </p>
                    </div>
                </div>
            </div>
            """,
        request.getCode(),
        request.getSalemanLabel(),
        request.getSalemanEmail(),
        buildShipmentDetailsHtml(shipmentDetail),
        request.getMailSubject(),
        request.getFeedback() != null ? request.getFeedback() : "N/A",
        request.getPricingNote() != null ? request.getPricingNote() : "N/A",
        request.getPricingLabel() != null ? request.getPricingLabel() : "N/A",
        request.getStatus() != null ? request.getStatus().getLabel() : "N/A",
        remoteUser
    );
  }

  private static String buildShipmentDetailsHtml(ShipmentDetail shipment) {
    if (shipment == null ||
        (shipment.getGrossWeightKg() == 0 &&
            shipment.getVolumeCbm() == 0 &&
            StringUtil.isEmpty(shipment.getVolumeInfo()))) {
      return "";
    }

    StringBuilder html = new StringBuilder();
    html.append("""
        <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
            <p style="margin: 0 0 8px 0; color: #374151;">
                <strong style="color: #1f2937;">📦 Shipment Details:</strong>
            </p>
            <ul style="margin: 0; padding-left: 20px; color: #374151;">
        """);

    if (shipment.getGrossWeightKg() > 0) {
      html.append(String.format("<li>Weight: %.2f KG</li>", shipment.getGrossWeightKg()));
    }
    if (shipment.getVolumeCbm() > 0) {
      html.append(String.format("<li>Volume: %.2f CBM</li>", shipment.getVolumeCbm()));
    }
    if (StringUtil.isNotEmpty(shipment.getVolumeInfo())) {
      html.append(String.format("<li>Container: %s</li>", shipment.getVolumeInfo()));
    }

    html.append("</ul></div>");
    return html.toString();
  }

  public static String buildUncompletedInquiryZaloMessage(InquiryRequest request) {
    String currentDate = new SimpleDateFormat("dd/MM/yyyy").format(new Date());
    StringBuilder message = new StringBuilder();

    message.append(String.format("""
            📬 THÔNG BÁO: Inquiry Request chưa xử lý (%s)
            ━━━━━━━━━━━━━━━━━━━━
            
            👨‍💼 Sales: %s
            📧 Email: %s
            """,
      currentDate,
      request.getSalemanLabel() != null ? request.getSalemanLabel() : "N/A",
      request.getSalemanEmail() != null ? request.getSalemanEmail() : "N/A"
    ));

    // Add shipment details if available
    ShipmentDetail shipment = request.getShipmentDetail();
    if (shipment != null) {
      if (shipment.getGrossWeightKg() > 0 || shipment.getVolumeCbm() > 0 || StringUtil.isNotEmpty(shipment.getVolumeInfo())) {
        message.append("\n📦 Shipment Details:");
        if (shipment.getGrossWeightKg() > 0) {
          message.append(String.format("\n- Weight: %.2f KG", shipment.getGrossWeightKg()));
        }
        if (shipment.getVolumeCbm() > 0) {
          message.append(String.format("\n- Volume: %.2f CBM", shipment.getVolumeCbm()));
        }
        if (StringUtil.isNotEmpty(shipment.getVolumeInfo())) {
          message.append(String.format("\n- Container: %s", shipment.getVolumeInfo()));
        }
      }
    }

    message.append(String.format("""
            
            📋 Subject: %s
            📝 Pricing Note: %s
            👨‍💼 Pricing By: %s
            📊 Status: %s
            
            ⚡️ Vui lòng ưu tiên xử lý inquiry request này để đảm bảo thông tin đến các bộ phận liên quan.
            
            🔔 Lưu ý:
             - Sau 48h chưa xử lý, hệ thống sẽ tự động mail CC quản lý trực tiếp
             - Request chưa xử lý sẽ tiếp tục được nhắc nhở vào ngày mai
            ━━━━━━━━━━━━━━━━━━━━
            """,
      request.getMailSubject(),
      request.getPricingNote() != null ? request.getPricingNote() : "N/A",
      request.getPricingLabel() != null ? request.getPricingLabel() : "N/A",
      request.getStatus() != null ? request.getStatus() : "N/A"));

    return message.toString();
  }

  public static String buildInquiryDetailMailMessage(InquiryRequest request) {
    // Build shipment details section if available
    StringBuilder shipmentSection = new StringBuilder();
    ShipmentDetail shipment = request.getShipmentDetail();
    if (shipment != null) {
      if (shipment.getGrossWeightKg() > 0 || shipment.getVolumeCbm() > 0 || StringUtil.isNotEmpty(shipment.getVolumeInfo())) {
        shipmentSection.append("""
            <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                <p style="margin: 0 0 8px 0; color: #1f2937;"><strong>📦 Shipment Details:</strong></p>
            """);

        if (shipment.getGrossWeightKg() > 0) {
          shipmentSection.append(String.format("""
              <p style="margin: 0 0 4px 0; color: #374151;">- Weight: %.2f KG</p>
              """, shipment.getGrossWeightKg()));
        }
        if (shipment.getVolumeCbm() > 0) {
          shipmentSection.append(String.format("""
              <p style="margin: 0 0 4px 0; color: #374151;">- Volume: %.2f CBM</p>
              """, shipment.getVolumeCbm()));
        }
        if (StringUtil.isNotEmpty(shipment.getVolumeInfo())) {
          shipmentSection.append(String.format("""
              <p style="margin: 0; color: #374151;">- Container: %s</p>
              """, shipment.getVolumeInfo()));
        }
        shipmentSection.append("</div>");
      }
    }

    return String.format("""
            <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 15px; background-color: #f9fafb;">
                <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h1 style="color: #2563eb; font-size: 20px; margin: 0 0 16px 0; padding-bottom: 12px; border-bottom: 1px solid #e5e7eb;">
                        📬 THÔNG BÁO: Inquiry Request chưa có feedback từ nhân viên kinh doanh (%s)
                    </h1>
                    
                    <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">👨‍💼 Sales:</strong> %s
                        </p>
                        <p style="margin: 0; color: #374151;">
                            <strong style="color: #1f2937;">📧 Email:</strong> %s
                        </p>
                    </div>

                    %s

                    <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">📋 Subject:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">📝 Pricing Note:</strong> %s
                        </p>
                        <p style="margin: 0 0 8px 0; color: #374151;">
                            <strong style="color: #1f2937;">👨‍💼 Pricing By:</strong> %s
                        </p>
                        <p style="margin: 0; color: #374151;">
                            <strong style="color: #1f2937;">📊 Status:</strong> %s
                        </p>
                    </div>

                    <p style="color: #dc2626; font-weight: 500; background-color: #fef2f2; padding: 12px; border-radius: 8px; margin: 16px 0;">
                        ⚡️ Vui lòng ưu tiên xử lý inquiry request này để đảm bảo thông tin đến các bộ phận liên quan.
                    </p>

                    <div style="background-color: #f3f4f6; padding: 16px; border-radius: 8px; margin: 16px 0;">
                        <p style="color: #1f2937; font-weight: 600; margin: 0 0 8px 0;">🔔 Lưu ý:</p>
                        <ul style="color: #374151; margin: 0; padding-left: 20px;">
                            <li>Sau 48h chưa xử lý, hệ thống sẽ tự động mail CC quản lý trực tiếp</li>
                            <li>Request chưa xử lý sẽ tiếp tục được nhắc nhở vào ngày mai</li>
                        </ul>
                    </div>

                    <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
                        <p style="color: #6b7280; font-size: 14px; margin: 0;">
                            This is an automated notification from the CRM Task Management System.
                        </p>
                    </div>
                </div>
            </div>
            """,
      DateUtil.asCompactDate(new Date()),
      request.getSalemanLabel() != null ? request.getSalemanLabel() : "N/A",
      request.getSalemanEmail() != null ? request.getSalemanEmail() : "N/A",
      shipmentSection.toString(),
      request.getMailSubject(),
      request.getPricingNote() != null ? request.getPricingNote() : "N/A",
      request.getPricingLabel() != null ? request.getPricingLabel() : "N/A",
      request.getStatus() != null ? request.getStatus().getLabel() : "N/A"
    );
  }

}