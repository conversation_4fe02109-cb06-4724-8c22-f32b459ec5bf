package cloud.datatp.fforwarder.price.common;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import java.util.Set;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.bean.BeanInspector;
import net.datatp.util.ds.MapObject;

@Embeddable
@Getter @Setter
@NoArgsConstructor
public class AirPriceGroup {

  @Column(name = "min_price")
  private double minPrice;

  @Column(name = "level1_price")
  private double level1Price;

  @Column(name = "level2_price")
  private double level2Price;

  @Column(name = "level3_price")
  private double level3Price;

  @Column(name = "level4_price")
  private double level4Price;

  @Column(name = "level5_price")
  private double level5Price;

  @Column(name = "level6_price")
  private double level6Price;

  @Column(name = "min_price_note")
  private String minPriceNote;

  @Column(name = "level1_price_note")
  private String level1PriceNote;

  @Column(name = "level2_price_note")
  private String level2PriceNote;

  @Column(name = "level3_price_note")
  private String level3PriceNote;

  @Column(name = "level4_price_note")
  private String level4PriceNote;

  @Column(name = "level5_price_note")
  private String level5PriceNote;

  @Column(name = "level6_price_note")
  private String level6PriceNote;

  public AirPriceGroup computeFrom(MapObject record) {
    minPriceNote = record.getString("minPriceNote", "");
    level1PriceNote = record.getString("level1PriceNote", "");
    level2PriceNote = record.getString("level2PriceNote", "");
    level3PriceNote = record.getString("level3PriceNote", "");
    level4PriceNote = record.getString("level4PriceNote", "");
    level5PriceNote = record.getString("level5PriceNote", "");
    level6PriceNote = record.getString("level6PriceNote", "");

    try {
      minPrice = record.getDouble("minPrice", 0D);
    } catch (RuntimeException ex) {
      minPrice = 0;
      minPriceNote = record.getString("minPrice", "");
    }

    try {
      level1Price = record.getDouble("level1Price", 0D);
    } catch (RuntimeException ex) {
      level1Price = 0;
      level1PriceNote = record.getString("level1Price", "");
    }

    try {
      level2Price = record.getDouble("level2Price", 0D);
    } catch (RuntimeException ex) {
      level2Price = 0;
      level2PriceNote = record.getString("level2Price", "");
    }

    try {
      level3Price = record.getDouble("level3Price", 0D);
    } catch (RuntimeException ex) {
      level3Price = 0;
      level3PriceNote = record.getString("level3Price", "");
    }

    try {
      level4Price = record.getDouble("level4Price", 0D);
    } catch (RuntimeException ex) {
      level4Price = 0;
      level4PriceNote = record.getString("level4Price", "");
    }

    try {
      level5Price = Math.round(record.getDouble("level5Price", 0D) * 1000.0) / 1000.0; ;
    } catch (RuntimeException ex) {
      level5Price = 0;
      level5PriceNote = record.getString("level5Price", "");
    }

    try {
      level6Price = Math.round(record.getDouble("level6Price", 0D) * 1000.0) / 1000.0; ;
    } catch (RuntimeException ex) {
      level6Price = 0;
      level6PriceNote = record.getString("level6Price", "");
    }
    return this;
  }

  public double computePriceValue(double chargeableWeight) {
    if (chargeableWeight <= 10) {
      return minPrice;
    } else if (chargeableWeight < 45) {
      return level1Price;
    } else if (chargeableWeight < 100) {
      return level2Price;
    } else if (chargeableWeight < 300) {
      return level3Price;
    } else if (chargeableWeight < 500) {
      return level4Price;
    } else if (chargeableWeight < 1000) {
      return level5Price;
    } else {
      return level6Price;
    }
  }

  private void computeValueMapObject() {
    BeanInspector<AirPriceGroup> inspector = new BeanInspector(this.getClass());
    Set<String> fieldNames = inspector.getPropertyNames();
  }

}