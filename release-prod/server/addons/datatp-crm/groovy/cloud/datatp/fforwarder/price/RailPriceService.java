package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.price.entity.RailTransportCharge;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.service.BaseComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Getter
@Service("RailPriceService")
public class RailPriceService extends BaseComponent {
  @Autowired
  private RailTransportChargeLogic logic;

  @Autowired
  private SeqService seqService;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(RailTransportCharge.SEQUENCE, 10);
  }

  @Transactional
  public RailTransportCharge save(ClientInfo client, Company company, RailTransportCharge charge) {
    return logic.save(client, company, charge);
  }

  public List<RailTransportCharge> saveCharges(ClientInfo client, Company company, List<RailTransportCharge> charges) {
    return logic.saveCharges(client, company, charges);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> search(ClientInfo client, Company company, SqlQueryParams params) {
    return logic.search(client, company, params);
  }

  @Transactional
  public boolean updateStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.updateStorageState(client, req);
  }

  @Transactional
  public boolean deleteByIds(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteByIds(client, company, ids);
  }
}