package cloud.datatp.fforwarder.price.repository;

import java.io.Serializable;
import java.util.List;

import cloud.datatp.fforwarder.price.entity.CustomClearanceSurCharge;
import cloud.datatp.fforwarder.price.entity.CustomClearanceType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import net.datatp.module.data.db.entity.StorageState;

@Repository
public interface CustomClearanceSurChargeRepository extends JpaRepository<CustomClearanceSurCharge, Serializable> {

  @Query("SELECT sc FROM CustomClearanceSurCharge sc WHERE sc.companyId = :companyId")
  public List<CustomClearanceSurCharge> findByCompany(@Param("companyId") Long companyId);

  @Modifying
  @Query("UPDATE CustomClearanceSurCharge sc SET sc.storageState = :state WHERE sc.id IN (:ids)")
  public int setStorageState(@Param("ids") List<Long> ids, @Param("state") StorageState state);

  @Query("SELECT sc FROM CustomClearanceSurCharge sc WHERE sc.id IN :ids")
  public List<CustomClearanceSurCharge> findCustomClearanceSurChargeById(@Param("ids") List<Long> ids);
}