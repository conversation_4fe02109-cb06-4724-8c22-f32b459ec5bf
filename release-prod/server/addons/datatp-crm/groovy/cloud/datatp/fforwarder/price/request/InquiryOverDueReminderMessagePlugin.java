package cloud.datatp.fforwarder.price.request;

import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.settings.message.CRMMessageLogic;
import cloud.datatp.fforwarder.settings.message.MailMessageProvider;
import cloud.datatp.fforwarder.settings.message.MessageServicePlugin;
import cloud.datatp.fforwarder.settings.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.settings.message.entity.MessageStatus;
import cloud.datatp.fforwarder.settings.message.entity.MessageType;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class InquiryOverDueReminderMessagePlugin extends MessageServicePlugin {
  public static final String PLUGIN_TYPE = "inquiry-overdue-reminder"; // set message at 8 A.M

  @Autowired
  private InquiryRequestLogic inquiryRequestLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private MailMessageProvider mailMessageProvider;

  protected InquiryOverDueReminderMessagePlugin() {
    super(PLUGIN_TYPE);
  }

  @Override
  public CRMMessageSystem onPreSend(ClientInfo client, CRMMessageSystem message) {
    try {
      Objects.assertNotNull(message.getReferenceType(), "Message has no reference type");
      Objects.assertNotNull(message.getReferenceId(), "Message has no reference id");
      Company company = companyLogic.getCompany(client, message.getCompanyId());
      Objects.assertNotNull(company, "Company {} is not found", message.getCompanyId());
      InquiryRequest request = inquiryRequestLogic.getInquiryRequest(client, company, message.getReferenceId());
      Objects.assertNotNull(request, "Inquiry request {} is not found", message.getReferenceId());
      InquiryRequest.InquiryStatus status = request.getStatus();

      log.info("📧 Preparing over due email for inquiry request [code={}, status={}] at {}\n", request.getCode(), status, DateUtil.asCompactDateTime(new Date()));

      if(InquiryRequest.InquiryStatus.DONE == status || InquiryRequest.InquiryStatus.NO_RESPONSE == status) {
        request.setStatus(InquiryRequest.InquiryStatus.NO_RESPONSE);
        if((InquiryRequest.InquiryStatus.DONE == status)) {
          inquiryRequestLogic.updateToNoResponse(client, company, Arrays.asList(request.getId()));
        }
      } else {
        log.info("❌ Cancelling message [id={}] due to invalid status: {}", message.getId(), status);
        message.setStatus(MessageStatus.CANCELLED);
        message = crmMessageLogic.saveMessage(client, company, message);
      }
      return message;
    } catch (Exception e) {
      log.error("❌ Error during pre-send processing for message [id={}]: {}",
          message.getId(), e.getMessage(), e);
      Company company = companyLogic.getCompany(client, message.getCompanyId());
      message.setStatus(MessageStatus.CANCELLED);
      return crmMessageLogic.saveMessage(client, company, message);
    }
  }

  @Override
  public void onPostSend(ClientInfo client, CRMMessageSystem message) {

    if(message.getStatus() == MessageStatus.SENT && message.getMessageType() == MessageType.MAIL) {
      Company company = companyLogic.getCompany(client, message.getCompanyId());
      if(company != null && company.getCode().equals("beehph")) {
        InquiryRequest request = inquiryRequestLogic.getInquiryRequest(client, company, message.getReferenceId());
        if(request.getStatus().equals(InquiryRequest.InquiryStatus.NO_RESPONSE)) {
          int reminderCount = message.getReminderCount() + 1;
          CRMMessageSystem overDueMailMessage = request.toOverDueMailMessage(client, reminderCount);
          Calendar calendar = Calendar.getInstance();
          calendar.setTime(new Date());
          calendar.add(Calendar.HOUR, 6);
          overDueMailMessage.setScheduledAt(calendar.getTime());
          String subject = overDueMailMessage.getMetadata().getString("subject", "CRM - ⚠️ Alert: Overdue Inquiry Request [" + request.getCode() + "] - Reminder " + reminderCount);
          MapObject metadata = message.getMetadata();
          metadata.put("subject", subject);
          overDueMailMessage.setMetadata(metadata);
          overDueMailMessage.setReminderCount(reminderCount);
          inquiryRequestLogic.getCrmMessageLogic().scheduleMessage(client, company, overDueMailMessage);
          log.info("📧 Scheduled over due reminder for inquiry request [id={}]", request.getId());
        }
      }
    }
  }

  @Override
  public void onSendError(ClientInfo client, CRMMessageSystem message, Exception error) {
    /*
    if(message.getStatus() != MessageStatus.CANCELLED && message.getMessageType() == MessageType.ZALO) {
      try {
        Objects.assertNotNull(message.getReferenceType(), "Message has no reference type");
        Objects.assertNotNull(message.getReferenceId(), "Message has no reference id");
        if (!InquiryRequest.TABLE_NAME.equals(message.getReferenceType())) {
          throw new RuntimeException("Message reference type is not InquiryRequest: " + message.getReferenceType());
        }

        Company company = companyLogic.getCompany(client, message.getCompanyId());
        Objects.assertNotNull(company, "Company {} is not found", message.getCompanyId());
        InquiryRequest request = inquiryRequestLogic.getInquiryRequest(client, company, message.getReferenceId());
        Objects.assertNotNull(request, "Inquiry request {} is not found", message.getReferenceId());

        log.info("📧 Preparing email fallback for inquiry request [id={}]", request.getId());
        CRMMessageSystem overDueMailMessage = request.toOverDueMailMessage(client, 1);
        overDueMailMessage.setScheduledAt(new Date());
        mailMessageProvider.send(client, overDueMailMessage);
      } catch (Exception e) {
        log.error("❌ Failed to send fallback email for Zalo message [id={}]: {}", message.getId(), e.getMessage(), e);
        throw new RuntimeException("Failed to send fallback email: " + e.getMessage(), e);
      }
    }
     */

  }

}