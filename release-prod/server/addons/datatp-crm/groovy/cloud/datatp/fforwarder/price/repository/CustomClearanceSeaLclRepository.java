package cloud.datatp.fforwarder.price.repository;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import cloud.datatp.fforwarder.price.entity.CustomClearanceSeaLcl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import net.datatp.module.data.db.entity.StorageState;

@Repository
public interface CustomClearanceSeaLclRepository extends JpaRepository<CustomClearanceSeaLcl, Serializable> {
  
  @Query("SELECT c FROM CustomClearanceSeaLcl c WHERE c.companyId = :companyId")
  List<CustomClearanceSeaLcl> findByCompanyId(@Param("companyId") Long companyId);

  @Modifying
  @Query("UPDATE CustomClearanceSeaLcl c SET c.storageState = :state WHERE c.id IN (:ids)")
  int setStorageState(@Param("ids") List<Long> ids, @Param("state") StorageState state);
  
  @Query("SELECT c FROM CustomClearanceSeaLcl c WHERE c.id IN :ids")
  List<CustomClearanceSeaLcl> findById(@Param("ids") List<Long> ids);
}