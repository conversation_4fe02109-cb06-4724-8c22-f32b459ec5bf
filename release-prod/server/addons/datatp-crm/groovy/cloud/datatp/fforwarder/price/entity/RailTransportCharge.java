package cloud.datatp.fforwarder.price.entity;

import java.io.Serial;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import cloud.datatp.fforwarder.price.common.BaseTransportCharge;
import cloud.datatp.fforwarder.settings.commodity.entity.CommodityType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.module.settings.currency.entity.Currency;

@Entity
@Table(
  name = RailTransportCharge.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = RailTransportCharge.TABLE_NAME + "_code",
      columnNames = { "company_id", "code" }),
    @UniqueConstraint(
      name = SeaFclTransportCharge.TABLE_NAME + "_source_code",
      columnNames = {"company_id", "source_code", "source_from"})
  },
  indexes = { @Index(columnList = "code") }
)
@DeleteGraphs({
  @DeleteGraph(target = TransportAdditionalCharge.class, joinField = "rail_charge_id", joinType = DeleteGraphJoinType.OneToMany),
  @DeleteGraph(target = CommissionDistribution.class, joinField = "rail_charge_id", joinType = DeleteGraphJoinType.OneToMany),
  @DeleteGraph(target = TransportFrequency.class, joinField = "rail_charge_id", joinType = DeleteGraphJoinType.OneToMany),
  @DeleteGraph(
    table = "lgc_price_rail_charge_commodity_type_rel",
    joinField = "rail_charge_id", joinType = DeleteGraphJoinType.ManyToMany
  ),
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class RailTransportCharge extends BaseTransportCharge {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_price_rail_charge";
  public static final String SEQUENCE = "lgc:lgc_price_rail_charge";

  @NotNull
  private String code;

  @Column(name = "carrier_partner_id")
  protected Long carrierPartnerId;

  @Column(name = "carrier_label")
  protected String carrierLabel;

  @Column(name = "handling_agent_partner_id")
  protected Long handlingAgentPartnerId;

  @Column(name = "handling_agent_partner_label")
  protected String handlingAgentPartnerLabel;

  @Column(name = "regular_20_price")
  private double regular20Price;

  @Column(name = "regular_40_price")
  private double regular40Price;

  @Column(name = "regular_45_price")
  private double regular45Price;

  @Column(name = "reefer_20_price")
  private double reefer20Price;

  @Column(name = "reefer_40_price")
  private double reefer40Price;

  @Column(name = "reefer_45_price")
  private double reefer45Price;

  @Column(name = "flatbed_20_price")
  private double flatbed20Price;

  @Column(name = "flatbed_40_price")
  private double flatbed40Price;

  @Column(name = "flatbed_45_price")
  private double flatbed45Price;

  @Column(name = "platform_20_price")
  private double platform20Price;

  @Column(name = "platform_40_price")
  private double platform40Price;

  @Column(name = "platform_45_price")
  private double platform45Price;

  @Column(name = "tilt_20_price")
  private double tilt20Price;

  @Column(name = "tilt_40_price")
  private double tilt40Price;

  @Column(name = "tilt_45_price")
  private double tilt45Price;

  @Column(name = "high_cube_40_price")
  private double highCube40Price;

  @Column(name = "high_cube_45_price")
  private double highCube45Price;

  @Column(name = "reefer_high_cube_40_price")
  private double reeferHighCube40Price;

  @Column(name = "iso_tank_20_price")
  private double isoTank20Price;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @DeleteGraph(target=TransportFrequency.class, joinField = "rail_charge_id")
  @JoinColumn(name = "rail_charge_id", referencedColumnName = "id")
  private List<TransportFrequency>     transportFrequencies = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @DeleteGraph(target=TransportAdditionalCharge.class, joinField = "rail_charge_id")
  @JoinColumn(name = "rail_charge_id", referencedColumnName = "id")
  private List<TransportAdditionalCharge> additionalCharges = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @DeleteGraph(target=CommissionDistribution.class, joinField = "rail_charge_id")
  @JoinColumn(name = "rail_charge_id", referencedColumnName = "id")
  private List<CommissionDistribution> commissionDistributions = new ArrayList<>();

  @ManyToMany(cascade = {CascadeType.MERGE})
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinTable(
    name = "lgc_price_rail_charge_commodity_type_rel",
    joinColumns = {
      @JoinColumn(name = "rail_charge_id", referencedColumnName = "id"),
      @JoinColumn(name = "company_id", referencedColumnName = "company_id"),
    },
    inverseJoinColumns = @JoinColumn(name = "commodity_type_id"),
    uniqueConstraints = {
      @UniqueConstraint(
        name = "lgc_price_rail_charge_commodity_type_rel_id_commodity_type",
        columnNames = { "rail_charge_id", "commodity_type_id" })
    }
  )
  private Set<CommodityType> commodityTypes;

  public RailTransportCharge withCurrency(Currency currency) {
    this.currency = currency.getName();
    return this;
  }

  public RailTransportCharge withCurrency(String name) {
    this.currency = name;
    return this;
  }

  public RailTransportCharge withRegular20Price(double regular20) {
    this.regular20Price = regular20;
    return this;
  }

  public RailTransportCharge withRegular40Price(double regular40) {
    this.regular40Price = regular40;
    return this;
  }

  public RailTransportCharge withRegular45Price(double regular45) {
    this.regular45Price = regular45;
    return this;
  }

  public RailTransportCharge withReefer20Price(double reefer20) {
    this.reefer20Price = reefer20;
    return this;
  }

  public RailTransportCharge withReefer40Price(double reefer40) {
    this.reefer40Price = reefer40;
    return this;
  }

  public RailTransportCharge withReefer45Price(double reefer45) {
    this.reefer45Price = reefer45;
    return this;
  }

  public RailTransportCharge withFlatbed20Price(double flatbed20) {
    this.flatbed20Price = flatbed20;
    return this;
  }

  public RailTransportCharge withFlatbed40Price(double flatbed40) {
    this.flatbed40Price = flatbed40;
    return this;
  }

  public RailTransportCharge withFlatbed45Price(double flatbed45) {
    this.flatbed45Price = flatbed45;
    return this;
  }

  public RailTransportCharge withPlatform20Price(double platform20) {
    this.platform20Price = platform20;
    return this;
  }

  public RailTransportCharge withPlatform45Price(double platform45) {
    this.platform45Price = platform45;
    return this;
  }

  public RailTransportCharge withTilt20Price(double tilt20) {
    this.tilt20Price = tilt20;
    return this;
  }

  public RailTransportCharge withTilt40Price(double tilt40) {
    this.tilt40Price = tilt40;
    return this;
  }

  public RailTransportCharge withTilt45Price(double tilt45) {
    this.tilt45Price = tilt45;
    return this;
  }

  public RailTransportCharge withAdditionalCharges(TransportAdditionalCharge additionalCharge) {
    if(additionalCharges == null) additionalCharges = new ArrayList<>();
    if(additionalCharge.getCurrency() == null) additionalCharge.setCurrency(this.currency);
    additionalCharges.add(additionalCharge);
    return this;
  }

  public RailTransportCharge withEditMode(EditMode editMode) {
    setEditMode(editMode);
    return this;
  }

  @Override
  public void set(ClientInfo client, Company company) {
    super.set(client, company);
    set(client, company, additionalCharges);
    set(client, company, commissionDistributions);
    set(client, company, transportFrequencies);
  }

  public List<TransportFrequency> getTransportFrequencies() {
    if(Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    return transportFrequencies;
  }

  public void setTransportFrequencies(List<TransportFrequency> frequencies) {
    if(Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    this.transportFrequencies.clear();
    if(frequencies != null) this.transportFrequencies.addAll(frequencies);
  }

  public List<TransportAdditionalCharge> getAdditionalCharges() {
    if(Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    return additionalCharges;
  }

  public void setAdditionalCharges(List<TransportAdditionalCharge> addCharges) {
    if(Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    this.additionalCharges.clear();
    if(addCharges != null) this.additionalCharges.addAll(addCharges);
  }

  public List<CommissionDistribution> getCommissionDistributions() {
    if(Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    return commissionDistributions;
  }

  public void setCommissionDistributions(List<CommissionDistribution> commissions) {
    if(Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    this.commissionDistributions.clear();
    if(commissions != null) this.commissionDistributions.addAll(commissions);
  }

  public Set<CommodityType> getCommodityTypes() {
    if(Objects.isNull(commodityTypes)) commodityTypes = new HashSet<>();
    return commodityTypes;
  }

  public void setCommodityTypes(Set<CommodityType> types) {
    if(Objects.isNull(commodityTypes)) commodityTypes = new HashSet<>();
    this.commodityTypes.clear();
    if(types != null) this.commodityTypes.addAll(types);
  }

  @Override
  public String getServiceType() {
    return "RAIL";
  }

  @Override
  public String getSequence() {
    return SEQUENCE;
  }
}