package cloud.datatp.fforwarder.price;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.fforwarder.price.common.BaseTruckTransportCharge;
import cloud.datatp.fforwarder.price.entity.TruckContainerTransportCharge;
import cloud.datatp.fforwarder.price.entity.TruckRegularTransportCharge;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.monitor.activity.StatisticService;
import net.datatp.module.monitor.activity.entity.StatisticKey;
import net.datatp.module.service.BaseComponent;
import net.datatp.util.ds.MapObject;

@Getter
@Service("TruckPriceService")
public class TruckPriceService extends BaseComponent {

  @Autowired
  private TruckPriceLogic logic;

  @Autowired
  private SeqService seqService;

  @Autowired
  private StatisticService statService;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(BaseTruckTransportCharge.PRICE_SEQUENCE, 10);
  }

  // =================================Truck ======================================

  @Transactional(readOnly = true)
  public TruckRegularTransportCharge getTruckRegularTransportChargeById(ClientInfo client, Company company, Long id) {
    return logic.getTruckRegularById(client, company, id);
  }

  @Transactional(readOnly = true)
  public List<TruckRegularTransportCharge> findTruckRegularTransportChargeByIds(ClientInfo client, Company company, List<Long> ids) {
    return logic.findTruckByIds(client, company, ids);
  }

  @Transactional
  public TruckRegularTransportCharge saveTruckRegularCharge(
    ClientInfo client, Company company, TruckRegularTransportCharge charge) {
    return logic.saveTruckRegularCharge(client, company, charge);
  }

  @Transactional
  public List<MapObject> saveTruckRegularCharges(ClientInfo client, Company company, List<MapObject> modified) {
    return logic.saveTruckRegulars(client, company, modified);
  }

  @Transactional
  public List<TruckRegularTransportCharge> saveTruckRegularTransportCharges(
    ClientInfo client, Company company, List<MapObject> records) {
    return logic.saveTruckCharges(client, company, records);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchTruckRegularTransportCharges(
    ClientInfo client, Company company, SqlQueryParams params) {
    String fromLocationCode = params.getString("pickupLocationCode");
    String toLocationCode   = params.getString("deliveryLocationCode");
    StatisticKey key =
      new StatisticKey(new Date(), client, "fforwarder:pricing", "search-truck-price")
        .withSource(fromLocationCode)
        .withDestination(toLocationCode);
    statService.incr(client, key, 1);
    return logic.searchTruckRegularTransportCharges(client, company, params);
  }

  @Transactional
  public boolean changeTruckStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.changeTruckStorageState(client, req);
  }

  @Transactional
  public boolean deleteTruckRegularByIds(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteTruckRegularByIds(client, company, ids);
  }

  // ============================= Container ===============================

  @Transactional(readOnly = true)
  public TruckContainerTransportCharge getTruckContainerTransportChargeById(ClientInfo client, Company company, Long id) {
    return logic.getTruckContainerById(client, company, id);
  }

  @Transactional
  public TruckContainerTransportCharge saveTruckContainerCharge(
    ClientInfo client, Company company, TruckContainerTransportCharge charge) {
    return logic.saveTruckContainerCharge(client, company, charge);
  }

  @Transactional
  public List<MapObject> saveTruckContainerCharges(
    ClientInfo client, Company company, List<MapObject> modified) {
    return logic.saveTruckContainerCharges(client, company, modified);
  }

  @Transactional
  public List<TruckContainerTransportCharge> saveTruckContainerTransportCharges(
    ClientInfo client, Company company, List<MapObject> records) {
    return logic.updateContainerCharges(client, company, records);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchTruckContainerTransportCharges(
    ClientInfo client, Company company, SqlQueryParams params) {
    String fromLocationCode = params.getString("pickupLocationCode");
    String toLocationCode   = params.getString("deliveryLocationCode");
    StatisticKey key =
      new StatisticKey(new Date(), client, "fforwarder:pricing", "search-container-price")
        .withSource(fromLocationCode)
        .withDestination(toLocationCode);
    statService.incr(client, key, 1);
    return logic.searchTruckContainerTransportCharges(client, company, params);
  }

  @Transactional
  public boolean changeContainerStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.changeContainerStorageState(client, req);
  }

  @Transactional
  public boolean deleteTruckContainerByIds(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteTruckContainerByIds(client, company, ids);
  }

}