package cloud.datatp.fforwarder.price.repository;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import cloud.datatp.fforwarder.price.entity.CustomClearanceSeaFcl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import net.datatp.module.data.db.entity.StorageState;

@Repository
public interface CustomClearanceSeaFclRepository extends JpaRepository<CustomClearanceSeaFcl, Serializable> {

  @Query("SELECT c FROM CustomClearanceSeaFcl c WHERE c.companyId = :companyId")
  List<CustomClearanceSeaFcl> findByCompanyId(@Param("companyId") Long companyId);

  @Modifying
  @Query("UPDATE CustomClearanceSeaFcl c SET c.storageState = :state WHERE c.id IN (:ids)")
  int setStorageState(@Param("ids") List<Long> ids, @Param("state") StorageState state);
  
  @Query("SELECT c FROM CustomClearanceSeaFcl c WHERE c.id IN :ids")
  List<CustomClearanceSeaFcl> findById(@Param("ids") List<Long> ids);
}