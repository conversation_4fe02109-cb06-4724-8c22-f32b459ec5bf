package cloud.datatp.fforwarder.price.entity;

import cloud.datatp.fforwarder.price.common.AirPriceGroup;
import cloud.datatp.fforwarder.price.common.BaseTransportCharge;
import cloud.datatp.fforwarder.price.common.ChargeTarget;
import cloud.datatp.fforwarder.settings.Purpose;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.module.partner.entity.Partner;
import net.datatp.module.resource.location.entity.Location;
import net.datatp.module.settings.currency.entity.Currency;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Entity
@Table(
  name = AirTransportCharge.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = AirTransportCharge.TABLE_NAME + "_code",
      columnNames = {"company_id", "code"})
  },
  indexes = {
    @Index(name = "lgc_price_air_charge_valid_date_idx", columnList = "valid_from, valid_to"),
    @Index(columnList = "code")
  }
)
@DeleteGraphs({
  @DeleteGraph(target = TransportAdditionalCharge.class, joinField = "air_charge_id", joinType = DeleteGraphJoinType.OneToMany),
  @DeleteGraph(target = CommissionDistribution.class, joinField = "air_charge_id", joinType = DeleteGraphJoinType.OneToMany),
  @DeleteGraph(target = TransportFrequency.class, joinField = "air_charge_id", joinType = DeleteGraphJoinType.OneToMany),
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class AirTransportCharge extends BaseTransportCharge {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_price_air_charge";
  public static final String SEQUENCE = "lgc:lgc_price_air_charge";

  @NotNull
  private String code;

  @Embedded
  private AirPriceGroup priceGroup = new AirPriceGroup();

  @Column(name = "carrier_partner_id")
  protected Long carrierPartnerId;

  @Column(name = "carrier_label")
  protected String carrierLabel;

  @Column(name = "handling_agent_partner_id")
  protected Long handlingAgentPartnerId;

  @Column(name = "handling_agent_partner_label")
  protected String handlingAgentPartnerLabel;

  @Column(name = "packing_note")
  private String packingNote;

  @Column(name = "commodity")
  private String commodity;

  @Column(name = "roe_vnd_usd")
  private String roeVNDToUSD;

  @Column(name = "flight_note")
  private String flightNote;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @DeleteGraph(target = TransportFrequency.class, joinField = "air_charge_id")
  @JoinColumn(name = "air_charge_id", referencedColumnName = "id")
  private List<TransportFrequency> transportFrequencies = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @DeleteGraph(target = TransportAdditionalCharge.class, joinField = "air_charge_id")
  @JoinColumn(name = "air_charge_id", referencedColumnName = "id")
  private List<TransportAdditionalCharge> additionalCharges = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @DeleteGraph(target = CommissionDistribution.class, joinField = "air_charge_id")
  @JoinColumn(name = "air_charge_id", referencedColumnName = "id")
  private List<CommissionDistribution> commissionDistributions = new ArrayList<>();

  public AirTransportCharge(Partner carrier, Location from, Location to) {
    this.fromLocationCode = from.getCode();
    this.fromLocationLabel = from.getLabel();
    this.toLocationCode = to.getCode();
    this.toLocationLabel = to.getLabel();
    this.carrierPartnerId = carrier.getId();
    this.carrierLabel = carrier.getLabel();
  }

  public AirTransportCharge(Purpose purpose) {
    this.purpose = purpose;
    editMode = EditMode.DRAFT;
    shareable = ShareableScope.COMPANY;
    currency = "USD";
    validFrom = new Date();
  }

  public AirTransportCharge computeFromMapObject(MapObject record) {
    if (record == null) return this;
    shareable = ShareableScope.ORGANIZATION;

    fromLocationCode = record.getString("fromLocationCode", null);
    fromLocationLabel = record.getString("fromLocationLabel", null);
    toLocationCode = record.getString("toLocationCode", null);
    toLocationLabel = record.getString("toLocationLabel", null);
    route = record.getString("route", null);
    carrierRoute = record.getString("carrierRoute", null);
    carrierPartnerId = record.getLong("carrierPartnerId", null);
    carrierLabel = record.getString("carrierLabel", null);
    handlingAgentPartnerId = record.getLong("handlingAgentPartnerId", null);
    handlingAgentPartnerLabel = record.getString("handlingAgentPartnerLabel", null);

    purpose = Purpose.valueOf(record.getString("purpose", "EXPORT"));
    priceGroup = Objects.ensureNotNull(priceGroup, AirPriceGroup::new).computeFrom(record);

    roeVNDToUSD = record.getString("roeVNDToUSD", "");
    flightNote = record.getString("flightNote", "");
    packingNote = record.getString("packingNote", "");
    commodity = record.getString("commodity", "");

    currency = record.getString("currency", "USD");
    String fromDateStr = record.getString("validFrom", "");
    if (fromDateStr.isEmpty()) setValidFrom(new Date());
    else setValidFrom(DateUtil.parseCompactDate(fromDateStr));

    String toDateStr = record.getString("validTo", "");
    if (toDateStr.isEmpty()) setValidFrom(new Date());
    else setValidTo(DateUtil.parseCompactDate(toDateStr));

    note = record.getString("note", null);
    setTransportFrequencies(Collections.singletonList(TransportFrequency.creator(record)));

    Map<String, String> addChargeMap = new HashMap<>();
    addChargeMap.put("addCharge:FSC:KGM", "S_FSC");
    addChargeMap.put("addCharge:SCC:KGM", "S_SCC"); // not detect charge in BFSONE.
    addChargeMap.put("addCharge:AMS:HAWB", "S_AMS");
    addChargeMap.put("addCharge:AMS:MAWB", "S_AMS");
    addChargeMap.put("addCharge:AWB:SET", "S_AWB");
    addChargeMap.put("addCharge:XRAY:KGM", "S_XRAY");
    addChargeMap.put("addCharge:TCS:KGM", "S_TCS");
    addChargeMap.put("addCharge:FHL:SET", "S_FHL");
    addChargeMap.put("addCharge:FWB:SET", "S_FWB");
    addChargeMap.put("addCharge:CCA:SET", "S_CCA");
    addChargeMap.put("addCharge:DG:SET", "S_DG");

    List<TransportAdditionalCharge> addCharges = new ArrayList<>();
    for (String key : addChargeMap.keySet()) {
      if (!record.containsKey(key)) continue;
      double amount = 0;
      String note = "";
      try {
        amount = record.getDouble(key, 0D);
      } catch (RuntimeException ex) {
        note = record.getString(key, "");
      }
      if (amount == 0 && StringUtil.isEmpty(note)) continue;
      final String[] split = key.split(":");
      String name = addChargeMap.get(key);
      String label = split[1];
      String unit = split[2];
      TransportAdditionalCharge addCharge = new TransportAdditionalCharge(name);
      addCharge.setLabel(label);
      addCharge.setUnit(unit);
      addCharge.setNote(note);
      addCharge.setUnitPrice(amount);
      addCharge.setQuantity(1);
      addCharge.setCurrency("USD");
      addCharge.setTotal(amount);
      addCharge.setFinalCharge(amount);
      addCharge.setPurpose(purpose);
      if (Purpose.EXPORT.equals(purpose)) addCharge.setTarget(ChargeTarget.ORIGIN);
      if (Purpose.IMPORT.equals(purpose)) addCharge.setTarget(ChargeTarget.DESTINATION);
      addCharges.add(addCharge);
    }
    setAdditionalCharges(addCharges);
    return this;
  }

  public AirTransportCharge withCurrency(Currency currency) {
    this.currency = currency.getName();
    return this;
  }

  public AirTransportCharge withCurrency(String name) {
    this.currency = name;
    return this;
  }

  public AirTransportCharge withAdditionalCharges(TransportAdditionalCharge... additionalCharge) {
    for (TransportAdditionalCharge sel : additionalCharge) {
      if (sel.getCurrency() == null) sel.setCurrency(this.currency);
    }
    additionalCharges = Arrays.addToList(additionalCharges, additionalCharge);
    return this;
  }

  public AirTransportCharge withPrivateShareableScope(ShareableScope scope) {
    setShareable(scope);
    return this;
  }

  public List<TransportFrequency> getTransportFrequencies() {
    if (Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    return transportFrequencies;
  }

  public void setTransportFrequencies(List<TransportFrequency> frequencies) {
    if (Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    this.transportFrequencies.clear();
    if (frequencies != null) this.transportFrequencies.addAll(frequencies);
  }

  public List<TransportAdditionalCharge> getAdditionalCharges() {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    return additionalCharges;
  }

  public void setAdditionalCharges(List<TransportAdditionalCharge> addCharges) {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    this.additionalCharges.clear();
    if (addCharges != null) this.additionalCharges.addAll(addCharges);
  }

  public List<CommissionDistribution> getCommissionDistributions() {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    return commissionDistributions;
  }

  public void setCommissionDistributions(List<CommissionDistribution> commissions) {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    this.commissionDistributions.clear();
    if (commissions != null) this.commissionDistributions.addAll(commissions);
  }

  @Override
  public String getServiceType() {
    return "AIR";
  }

  @Override
  public String getSequence() {
    return SEQUENCE;
  }

  @Override
  public void set(ClientInfo client, Company company) {
    super.set(client, company);
    set(client, company, additionalCharges);
    set(client, company, commissionDistributions);
    set(client, company, transportFrequencies);
  }
}