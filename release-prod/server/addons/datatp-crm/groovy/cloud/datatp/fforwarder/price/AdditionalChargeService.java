package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.price.entity.AdditionalCharge;
import cloud.datatp.fforwarder.price.entity.CustomClearanceType;

import cloud.datatp.fforwarder.settings.TransportationMode;
import lombok.Getter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.util.ds.MapObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("AdditionalChargeService")
public class AdditionalChargeService extends BaseComponent{

  @Autowired @Getter
  private AdditionalChargeLogic logic;

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchAdditionalCharges(ClientInfo client, Company company, SqlQueryParams params) {
    return logic.searchAdditionalCharge(client, company, params);
  }

  @Transactional(readOnly = true)
  public AdditionalCharge getAdditionalCharge(ClientInfo client, Company company, String name, TransportationMode mode) {
    return logic.getAdditionalChargeByName(client, company, name, mode);
  }

  @Transactional(readOnly = true)
  public AdditionalCharge getAdditionalCharge(ClientInfo client, Company company, String name, CustomClearanceType type) {
    return logic.getAdditionalChargeByName(client, company, name, type);
  }

  @Transactional
  public AdditionalCharge saveAdditionalCharge(ClientInfo client, Company company, AdditionalCharge additionalCharge) {
    return logic.saveAdditionalCharge(client, company, additionalCharge);
  }

  @Transactional
  public List<MapObject> saveAdditionalCharges(ClientInfo client, Company company, List<MapObject> additionalCharges) {
    return logic.saveAdditionalCharges(client, company, additionalCharges);
  }

  @Transactional(readOnly = true)
  public List<AdditionalCharge> findAdditionalChargesByCompany(ClientInfo client, Company company) {
    return logic.findAdditionalChargesByCompany(client, company);
  }

  @Transactional
  public boolean changeAdditionalTransportStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.changeAdditionalTransportStorageState(client, req);
  }
  
  @Transactional
  public boolean deleteAdditionalTransportCharges(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteAdditionalTransportCharges(client, company, ids);
  }
}