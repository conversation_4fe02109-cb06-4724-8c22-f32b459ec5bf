package cloud.datatp.fforwarder.price.repository;

import java.io.Serializable;
import java.util.List;

import cloud.datatp.fforwarder.price.entity.AdditionalCharge;
import cloud.datatp.fforwarder.price.entity.CustomClearanceType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import cloud.datatp.fforwarder.settings.TransportationMode;
import net.datatp.module.data.db.entity.StorageState;

public interface AdditionalChargeRepository extends JpaRepository<AdditionalCharge, Serializable> {

  @Query("SELECT ac FROM AdditionalCharge ac WHERE (ac.companyId = :companyId OR ac.shareable='Public') AND ac.name = :name AND ac.transportationMode = :mode")
  AdditionalCharge getByName(@Param("companyId") Long companyId, @Param("name") String name, @Param("mode") TransportationMode mode);
  
  @Query("SELECT ac FROM AdditionalCharge ac WHERE (ac.companyId = :companyId OR ac.shareable='Public') AND ac.name = :name AND ac.customClearanceType = :type")
  AdditionalCharge getByName(@Param("companyId") Long companyId, @Param("name") String name, @Param("type") CustomClearanceType type);

  @Query("SELECT ac FROM AdditionalCharge ac WHERE ac.companyId = :companyId")
  List<AdditionalCharge> findByCompany(@Param("companyId") Long companyId);

  @Modifying
  @Query("UPDATE AdditionalCharge ac SET ac.storageState = :state WHERE ac.id IN (:ids)")
  int setStorageState(@Param("ids") List<Long> ids, @Param("state") StorageState state);

  @Query("SELECT ac FROM AdditionalCharge ac WHERE ac.id IN :ids")
  List<AdditionalCharge> findAdditionalTransportChargeById(@Param("ids") List<Long> ids);
  
  @Query(
      "SELECT ac FROM AdditionalCharge ac" +
        " WHERE ac.id = :id" +
        " AND (" +
        " (ac.shareable = 'PRIVATE' AND ac.companyId = :companyId AND ac.createdBy = :loginId)" +
        " OR (ac.shareable = 'COMPANY' AND ac.companyId = :companyId)" +
        " OR (ac.shareable = 'DESCENDANTS' AND ac.companyId IN (:companyIds))" +
        " OR (ac.shareable = 'ORGANIZATION')" +
        ")"
    )
  AdditionalCharge getById(
      @Param("companyId") Long companyId, @Param("companyIds") List<Long> companyIds,
      @Param("loginId") String loginId, @Param("id") Long id
    );
}