package cloud.datatp.fforwarder.price.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder

class BFSOneFCLPricingSql extends Executor {

    public class BFSOneFCLExpCountingSql extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            String query = """
                  SELECT 
                    s.Grp                                     AS group_type,
                    LEFT(s.NoID, CHARINDEX('_', s.NoID) - 1)  AS user_name,
                    ui.UserID                                 AS user_input,
                    COUNT(s.<PERSON>ey)                            AS total_count
                  FROM SeaFreightPricing s
                  JOIN UserInfos ui 
                    ON ui.Username = LEFT(s.NoID, CHARINDEX('_', s.NoID) - 1)
                  WHERE 1 = 1
                    AND CAST(s.ModifyDate AS DATE) = CAST(GETDATE() AS DATE)
                  GROUP BY s.Grp, LEFT(s.NoID, CHARINDEX('_', s.NoID) - 1), ui.UserID

              """;
            return query;
        }
    }

    public class BFSOneFCLExpNoneUSPricingSql extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            Binding binding = ctx.getParam("binding");
            String inputUser = binding.getProperty("inputUser");
            String query = """
                SELECT 
                  'EXPORT'                      AS purpose,
                  CONCAT(s.NoID, '-', s.IDKey)  AS source_code,
                  s.POL                         AS from_location_code, 
                  pol.AirPortName               AS from_location_label, 
                  s.POD                         AS to_location_code, 
                  pod.AirPortName               AS to_location_label, 
                  s.FinalDest                   AS final_terminal_location_label,
                  s.Carrier                     AS carrier_label, 
                  s.Commodity                   AS commodity, 
                  
                  s.DC20                        AS dry_20_price, 
                  s.DC40                        AS dry_40_price, 
                  s.HC40                        AS high_cube_40_price, 
                  s.HC45                        AS high_cube_45_price, 
                  
                  s.DC20_KB                     AS commission_20_D_C, 
                  s.DC40_KB                     AS commission_40_D_C, 
                  s.HC40_KB                     AS commission_40_H_C, 
                  s.HC45_KB                     AS commission_45_H_C, 
                  
                  s.Currency                    AS currency, 
                  s.Remarks                     AS remarks, 
                  s.TT                          AS transit_time, 
                  s.TransitPort                 AS transit_port, 
                  s.Freq                        AS frequency, 
                  s.FreeTime                    AS free_time,
                  s.EffectDate                  AS valid_from, 
                  s.Validity                    AS valid_to, 
                  s.Note                        AS note,
                  'NONE_US'                     AS group_type
                FROM SeaFreightPricing s 
                  JOIN Airports pol ON pol.AirPortID = s.POL 
                  JOIN Airports pod ON pod.AirPortID = s.POD 
                WHERE 1 = 1 
                  AND CAST(s.ModifyDate AS DATE) = CAST(GETDATE() AS DATE)
                  AND (s.NoID LIKE '${inputUser}%') 
                  AND s.Grp = 'NONE US'
              """;
            return query;
        }
    }

    public class BFSOneFCLExpReeferPricingSql extends ExecutableSqlBuilder {
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            Binding binding = ctx.getParam("binding");
            String inputUser = binding.getProperty("inputUser");
            String query = """
                SELECT 
                  'EXPORT'                      AS purpose,
                  CONCAT(s.NoID, '-', s.IDKey)  AS source_code,
                  s.POL                         AS from_location_code, 
                  pol.AirPortName               AS from_location_label, 
                  s.POD                         AS to_location_code, 
                  pod.AirPortName               AS to_location_label, 
                  s.FinalDest                   AS final_terminal_location_label,
                  s.Carrier                     AS carrier_label, 
                  s.Commodity                   AS commodity, 
                  
                  s.DC20                        AS dry_20_price, 
                  s.DC40                        AS dry_40_price, 
                  s.HC40                        AS high_cube_40_price, 
                  s.HC45                        AS high_cube_45_price, 
                  s.RF20                        AS reefer_20_price, 
                  s.RF40                        AS reefer_40_price, 
                  
                  s.DC20_KB                     AS commission_20_D_C, 
                  s.DC40_KB                     AS commission_40_D_C, 
                  s.HC40_KB                     AS commission_40_H_C, 
                  s.HC45_KB                     AS commission_45_H_C, 
                  s.RF20_KB                     AS commission_20_R_F, 
                  s.RF40_KB                     AS commission_40_R_F, 
                  
                  s.Currency                    AS currency, 
                  s.Remarks                     AS remarks, 
                  s.TT                          AS transit_time, 
                  s.TransitPort                 AS transit_port, 
                  s.Freq                        AS frequency, 
                  s.FreeTime                    AS free_time,
                  s.EffectDate                  AS valid_from, 
                  s.Validity                    AS valid_to, 
                  s.Note                        AS note,
                  'SPECIAL'                     AS group_type
                FROM SeaFreightPricing s 
                  JOIN Airports pol ON pol.AirPortID = s.POL 
                  JOIN Airports pod ON pod.AirPortID = s.POD 
                WHERE 1 = 1 
                  AND CAST(s.ModifyDate AS DATE) = CAST(GETDATE() AS DATE)
                  AND (s.NoID LIKE '${inputUser}%') 
                  AND s.Grp = 'REEFER RATE'
            
              """;
            return query;
        }
    }

    public class BFSOneFCLExpUSRoutePricingSql extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            Binding binding = ctx.getParam("binding");
            String inputUser = binding.getProperty("inputUser");
            String query = """
                SELECT 
                  'EXPORT'                      AS purpose,
                  CONCAT(s.NoID, '-', s.IDKey)  AS source_code,
                  s.POL                         AS from_location_code, 
                  pol.AirPortName               AS from_location_label, 
                  s.POD                         AS to_location_code, 
                  pod.AirPortName               AS to_location_label, 
                  s.FinalDest                   AS final_terminal_location_label,
                  s.Carrier                     AS carrier_label, 
                  s.Commodity                   AS commodity, 
                  
                  s.DC20                        AS dry_20_price, 
                  s.DC40                        AS dry_40_price, 
                  s.HC40                        AS high_cube_40_price, 
                  s.HC45                        AS high_cube_45_price, 
                  
                  s.DC20_KB                     AS commission_20_D_C, 
                  s.DC40_KB                     AS commission_40_D_C, 
                  s.HC40_KB                     AS commission_40_H_C, 
                  s.HC45_KB                     AS commission_45_H_C, 
                  
                  s.Currency                    AS currency, 
                  s.Remarks                     AS remarks, 
                  s.TT                          AS transit_time, 
                  s.TransitPort                 AS transit_port, 
                  s.Mode                        AS container_handling_type,    
                  s.Freq                        AS frequency, 
                  s.FreeTime                    AS free_time,
                  s.EffectDate                  AS valid_from, 
                  s.Validity                    AS valid_to, 
                  s.Note                        AS note,
                  'US_ROUTE'                    AS group_type
                FROM SeaFreightPricing s 
                  JOIN Airports pol ON pol.AirPortID = s.POL 
                  JOIN Airports pod ON pod.AirPortID = s.POD 
                WHERE 1 = 1
                  AND CAST(s.ModifyDate AS DATE) = CAST(GETDATE() AS DATE)
                  AND (s.NoID LIKE '${inputUser}%') 
                  AND s.Grp = 'US ROUTE'
            
              """;
            return query;
        }
    }

    public BFSOneFCLPricingSql() {
        register(new BFSOneFCLExpCountingSql());
        register(new BFSOneFCLExpNoneUSPricingSql());
        register(new BFSOneFCLExpReeferPricingSql());
        register(new BFSOneFCLExpUSRoutePricingSql());
    }
}