package cloud.datatp.fforwarder.price.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.Executor;
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject;

public class AdditionalChargeSql extends Executor {

  public class SearchAdditionalCharge extends ExecutableSqlBuilder {

    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT  
          charge.id                                     AS id,
          charge.name                                   AS name,
          charge.label                                  AS label,
          charge.edit_mode                              AS edit_mode,
          charge.quantity                               AS quantity,
          charge.unit                                   AS unit,
          charge.tax_rate                               AS tax_rate,
          charge.currency                               AS currency,
          charge.transportation_mode                    AS transportation_mode,
          charge.type                                   AS type,
          charge.method                                 AS method,
          charge.target                                 AS target,
          charge.purpose                                AS purpose,
    
          charge.storage_state                          AS storage_state,
          charge.edit_mode                              AS edit_mode,
          charge.shareable                              AS shareable,
          charge.created_by                             AS created_by,
          charge.created_time                           AS created_time,
          charge.modified_by                            AS modified_by,
          charge.modified_time                          AS modified_time,                            
          charge.version                                AS version,                            
          charge.company_id                             AS company_id   
        FROM lgc_price_additional_charge AS charge
        WHERE 
          ${FILTER_BY_STORAGE_STATE(sqlParams)}
          ${AND_FILTER_BY_PARAM('charge.transportationMode','transportationMode', sqlParams)}
          ${AND_SEARCH_BY_PARAMS(['name', 'label'], 'search', sqlParams)}
        ORDER BY charge.modified_time DESC, charge.label
        ${MAX_RETURN(sqlParams)}        
      """;
      return query;
    }
  }
  
  public AdditionalChargeSql() {
    register(new SearchAdditionalCharge())
  }
}
