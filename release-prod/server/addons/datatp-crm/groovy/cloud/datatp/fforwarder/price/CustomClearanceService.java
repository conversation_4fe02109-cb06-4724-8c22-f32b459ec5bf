package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.price.entity.*;
import cloud.datatp.fforwarder.price.entity.*;
import lombok.Getter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Getter
@Service("CustomClearanceService")
public class CustomClearanceService extends BaseComponent {

  @Autowired
  private CustomClearanceLogic logic;

  // Air 

  @Transactional
  public CustomClearanceAir saveCustomClearanceAir(ClientInfo client, Company company, CustomClearanceAir customClearance) {
    return logic.saveCustomClearanceAir(client, company, customClearance);
  }

  @Transactional
  public List<CustomClearanceAir> saveCustomClearanceAirs(ClientInfo client, Company company, List<CustomClearanceAir> customClearances) {
    return logic.saveCustomClearanceAirs(client, company, customClearances);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchCustomClearanceAirs(ClientInfo client, Company company, SqlQueryParams params) {
    return logic.searchCustomClearances(client, company, CustomClearanceAir.class, params);
  }

  @Transactional
  public boolean changeCustomClearanceAirStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.changeCustomClearanceAirStorageState(client, req);
  }
  
  @Transactional
  public boolean deleteCustomClearanceAirCharges(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteCustomClearanceCharges(client, company, CustomClearanceAir.class, ids);
  }
  
  // Rail

  @Transactional
  public CustomClearanceRail saveCustomClearanceRail(ClientInfo client, Company company, CustomClearanceRail customClearance) {
    return logic.saveCustomClearanceRail(client, company, customClearance);
  }

  @Transactional
  public List<CustomClearanceRail> saveCustomClearanceRails(ClientInfo client, Company company, List<CustomClearanceRail> customClearances) {
    return logic.saveCustomClearanceRails(client, company, customClearances);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchCustomClearanceRails(ClientInfo client, Company company, SqlQueryParams params) {
    return logic.searchCustomClearances(client, company, CustomClearanceRail.class, params);
  }

  @Transactional
  public boolean changeCustomClearanceRailStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.changeCustomClearanceAirStorageState(client, req);
  }
  
  @Transactional
  public boolean deleteCustomClearanceRailCharges(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteCustomClearanceCharges(client, company, CustomClearanceRail.class, ids);
  }
  
  // Sea LCL

  @Transactional
  public CustomClearanceSeaLcl saveCustomClearanceSeaLcl(ClientInfo client, Company company, CustomClearanceSeaLcl customClearance) {
    return logic.saveCustomClearanceSeaLcl(client, company, customClearance);
  }

  @Transactional
  public List<CustomClearanceSeaLcl> saveCustomClearanceSeaLcls(ClientInfo client, Company company, List<CustomClearanceSeaLcl> customClearances) {
    return logic.saveCustomClearanceSeaLcls(client, company, customClearances);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchCustomClearanceSeaLcls(ClientInfo client, Company company, SqlQueryParams params) {
    return logic.searchCustomClearances(client, company, CustomClearanceSeaLcl.class, params);
  }

  @Transactional
  public boolean changeCustomClearanceSeaLclStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.changeCustomClearanceSeaLclStorageState(client, req);
  }
  
  @Transactional
  public boolean deleteCustomClearanceSeaLclCharges(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteCustomClearanceCharges(client, company, CustomClearanceSeaLcl.class, ids);
  }
  
  // Sea FCL 

  @Transactional
  public CustomClearanceSeaFcl saveCustomClearanceSeaFcl(ClientInfo client, Company company, CustomClearanceSeaFcl customClearance) {
    return logic.saveCustomClearanceSeaFcl(client, company, customClearance);
  }

  @Transactional
  public List<CustomClearanceSeaFcl> saveCustomClearanceSeaFcls(ClientInfo client, Company company, List<CustomClearanceSeaFcl> customClearances) {
    return logic.saveCustomClearanceSeaFcls(client, company, customClearances);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchCustomClearanceSeaFcls(ClientInfo client, Company company, SqlQueryParams params) {
    return logic.searchCustomClearances(client, company, CustomClearanceSeaFcl.class, params);
  }

  @Transactional
  public boolean changeCustomClearanceSeaFclStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.changeCustomClearanceSeaFclStorageState(client, req);
  }
  
  @Transactional
  public boolean deleteCustomClearanceSeaFclCharges(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteCustomClearanceCharges(client, company, CustomClearanceSeaFcl.class, ids);
  }
  
  // SurCharge
  @Transactional(readOnly = true)
  public List<CustomClearanceSurCharge> searchCustomClearanceSurChargeCharges(
      ClientInfo client, Company company, SqlQueryParams params) {
    return logic.searchCustomClearanceSurChargeCharge(client, company, params);
  }

  @Transactional
  public CustomClearanceSurCharge saveCustomClearanceSurCharge(ClientInfo client, Company company,
      CustomClearanceSurCharge surCharge) {
    return logic.saveCustomClearanceSurCharge(client, company, surCharge);
  }

  @Transactional
  public List<CustomClearanceSurCharge> saveCustomClearanceSurCharges(ClientInfo client, Company company, List<CustomClearanceSurCharge> surCharges) {
    return logic.saveCustomClearanceSurCharges(client, company, surCharges);
  }

  @Transactional
  public boolean changeCustomClearanceSurChargeStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.changeAdditionalTransportStorageState(client, req);
  }

  @Transactional
  public boolean deleteCustomClearanceSurCharges(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteCustomClearanceSurCharges(client, company, ids);
  }
  
}