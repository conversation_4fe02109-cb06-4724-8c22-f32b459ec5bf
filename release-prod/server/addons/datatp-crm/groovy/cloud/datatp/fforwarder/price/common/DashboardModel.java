package cloud.datatp.fforwarder.price.common;

import lombok.Getter;
import lombok.Setter;
import net.datatp.module.data.db.SqlMapRecord;

import java.util.ArrayList;
import java.util.List;

@Setter @Getter
public class DashboardModel {

  private List<SqlMapRecord> userInputReport = new ArrayList<>();
  private List<SqlMapRecord> pendingRequest = new ArrayList<>();
  private List<SqlMapRecord> recentFeedActivities = new ArrayList<>();
  private List<SqlMapRecord> reportPriceTracker = new ArrayList<>();
  private List<SqlMapRecord> priceCheckRequests = new ArrayList<>();

}