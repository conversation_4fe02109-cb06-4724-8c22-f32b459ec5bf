package cloud.datatp.fforwarder.price.entity;

import cloud.datatp.fforwarder.price.common.*;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.settings.message.entity.MessageType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.util.*;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.entity.Account;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.module.partner.entity.Partner;
import net.datatp.module.resource.location.entity.Location;
import net.datatp.module.settings.currency.entity.Currency;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;


@Entity
@Table(
  name = SeaLclTransportCharge.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = SeaLclTransportCharge.TABLE_NAME + "_code",
      columnNames = {"company_id", "code"}),
  },
  indexes = {
    @Index(columnList = "company_id"),
    @Index(columnList = "valid_from"),
    @Index(columnList = "valid_to"),
    @Index(columnList = "from_location_code"),
    @Index(columnList = "to_location_code"),
    @Index(columnList = "purpose"),
    @Index(columnList = "storage_state")
  }
)
@DeleteGraphs({
  @DeleteGraph(target = TransportAdditionalCharge.class, joinField = "sea_lcl_charge_id", joinType = DeleteGraphJoinType.OneToMany),
  @DeleteGraph(target = CommissionDistribution.class, joinField = "sea_lcl_charge_id", joinType = DeleteGraphJoinType.OneToMany),
  @DeleteGraph(target = TransportFrequency.class, joinField = "sea_lcl_charge_id", joinType = DeleteGraphJoinType.OneToMany),
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
@Slf4j
public class SeaLclTransportCharge extends BaseTransportCharge {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_price_sea_lcl_charge";
  public static final String SEQUENCE = "lgc:lgc_price_sea_lcl_charge";

  @NotNull
  private String code;

  @Embedded
  private SeaLCLPriceGroup priceGroup = new SeaLCLPriceGroup();

  @Column(name = "carrier_partner_id")
  protected Long carrierPartnerId;

  @Column(name = "carrier_label")
  protected String carrierLabel;

  @Column(name = "handling_agent_partner_id")
  protected Long handlingAgentPartnerId;

  @Column(name = "handling_agent_partner_label")
  protected String handlingAgentPartnerLabel;

  @Column(name = "stuffing_note")
  private String stuffingNote;

  @Column(name = "local_charge_at_dest", length = 32 * 1024)
  private String localChargeAtDest;

  @Column(name = "surcharge_note")
  private String surchargeNote;

  @Transient
  private List<MapObject> feedbacks = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @DeleteGraph(target = TransportFrequency.class, joinField = "sea_lcl_charge_id")
  @JoinColumn(name = "sea_lcl_charge_id", referencedColumnName = "id")
  private List<TransportFrequency> transportFrequencies = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @DeleteGraph(target = TransportAdditionalCharge.class, joinField = "sea_lcl_charge_id")
  @JoinColumn(name = "sea_lcl_charge_id", referencedColumnName = "id")
  private List<TransportAdditionalCharge> additionalCharges = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @DeleteGraph(target = CommissionDistribution.class, joinField = "sea_lcl_charge_id")
  @JoinColumn(name = "sea_lcl_charge_id", referencedColumnName = "id")
  private List<CommissionDistribution> commissionDistributions = new ArrayList<>();

  @Access(AccessType.PROPERTY)
  @Column(name = "feedbacks", length = 64 * 1024)
  public String getFeedbackJson() {
    if (this.feedbacks == null) return null;
    return DataSerializer.JSON.toString(this.feedbacks);
  }

  public void setFeedbackJson(String json) {
    if (StringUtil.isEmpty(json)) {
      this.feedbacks = null;
    } else {
      this.feedbacks = DataSerializer.JSON.fromString(json, new TypeReference<>() { });
    }
  }

  public SeaLclTransportCharge(Purpose purpose) {
    this.purpose = purpose;
    editMode = EditMode.DRAFT;
    shareable = ShareableScope.COMPANY;
    currency = "USD";
    validFrom = new Date();
  }

  public SeaLclTransportCharge(Partner carrier, Location from, Location to) {
    this.fromLocationLabel = from.getLabel();
    this.fromLocationCode = from.getCode();
    this.toLocationLabel = to.getLabel();
    this.toLocationCode = to.getCode();
    this.carrierPartnerId = carrier.getId();
    this.carrierLabel = carrier.getLabel();
  }

  public SeaLclTransportCharge withCurrency(Currency currency) {
    this.currency = currency.getName();
    return this;
  }

  public SeaLclTransportCharge withCurrency(String name) {
    this.currency = name;
    return this;
  }

  public SeaLclTransportCharge withAdditionalCharge(TransportAdditionalCharge... additionalCharge) {
    for (TransportAdditionalCharge sel : additionalCharge) {
      if (sel.getCurrency() == null)
        sel.setCurrency(this.currency);
    }
    additionalCharges = Arrays.addToList(additionalCharges, additionalCharge);
    return this;
  }

  public SeaLclTransportCharge withEditMode(EditMode editMode) {
    setEditMode(editMode);
    return this;
  }

  public SeaLclTransportCharge withFrequency(TransportFrequency... fre) {
    transportFrequencies = Arrays.addToList(transportFrequencies, fre);
    return this;
  }

  public List<TransportFrequency> getTransportFrequencies() {
    if (Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    return transportFrequencies;
  }

  public void setTransportFrequencies(List<TransportFrequency> frequencies) {
    if (Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    this.transportFrequencies.clear();
    if (frequencies != null) this.transportFrequencies.addAll(frequencies);
  }

  public List<TransportAdditionalCharge> getAdditionalCharges() {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    return additionalCharges;
  }

  public void setAdditionalCharges(List<TransportAdditionalCharge> addCharges) {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    this.additionalCharges.clear();
    if (addCharges != null) this.additionalCharges.addAll(addCharges);
  }

  public List<CommissionDistribution> getCommissionDistributions() {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    return commissionDistributions;
  }

  public void setCommissionDistributions(List<CommissionDistribution> commissions) {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    this.commissionDistributions.clear();
    if (commissions != null) this.commissionDistributions.addAll(commissions);
  }

  public CRMMessageSystem toPricingFeedbackUpdateMessage(Account assigneeAccount, Account saleAccount, String feedback) {
    CRMMessageSystem message = new CRMMessageSystem();
    message.setContent(SeaNotificationTemplate.buildMailMessage(this, saleAccount, feedback));
    message.setScheduledAt(new Date());
    message.setMessageType(MessageType.MAIL);
    message.setReferenceId(id);
    message.setReferenceType(SeaLclTransportCharge.TABLE_NAME);
    message.setPluginName(PriceFeedbackUpdateMessagePlugin.PLUGIN_TYPE);
    message.setRecipients(new HashSet<>(Collections.singletonList(assigneeAccount.getEmail())));
    MapObject metadata = new MapObject();
    metadata.put("fromEmail", saleAccount.getEmail());
    metadata.put("subject", "PRICE FEEDBACK UPDATE - SEA LCL");
    metadata.put("to", java.util.Collections.singletonList(assigneeAccount.getEmail()));
    message.setMetadata(metadata);
    return message;
  }

  public SeaLclTransportCharge computeFromMapObject(MapObject record) {
    if (record == null) return this;
    mapFrom(record);
    carrierPartnerId = record.getLong("carrierPartnerId", null);
    carrierLabel = record.getString("carrierLabel", null);
    handlingAgentPartnerId = record.getLong("handlingAgentPartnerId", null);
    handlingAgentPartnerLabel = record.getString("handlingAgentPartnerLabel", null);

    surchargeNote = record.getString("surchargeNote", null);
    stuffingNote = record.getString("stuffingNote", null);
    localChargeAtDest = record.getString("localChargeAtDest", null);

    priceGroup = Objects.ensureNotNull(priceGroup, SeaLCLPriceGroup::new).computeFrom(record);

    setTransportFrequencies(Collections.singletonList(TransportFrequency.creator(record)));
    //addCharge:CFS:CBM

    if (record.containsKey("feedbacks")) {
      try {
        Object feedbackData = record.get("feedbacks");
        if (feedbackData instanceof List) {
          this.feedbacks = (List<MapObject>) feedbackData;
        } else if (feedbackData instanceof String) {
          this.feedbacks = DataSerializer.JSON.fromString((String) feedbackData, new TypeReference<>() { });
        } else {
          this.feedbacks = new ArrayList<>();
        }
      } catch (Exception e) {
        log.error("Failed to set feedback", e);
        log.error(e.getMessage(), e);
        this.feedbacks = null;
      }
    }

    Map<String, String> importLCL = new HashMap<>();
    importLCL.put("addCharge:DO:SET", "S_DO FEE");
    importLCL.put("addCharge:THC:CBM", "S_THC");
    importLCL.put("addCharge:CFS:CBM", "S_CFS");
    importLCL.put("addCharge:CIC:CBM", "S_CIC");

    Map<String, String> exportLCL = new HashMap<>();
    exportLCL.put("addCharge:CFS:CBM", "S_CFS");
    exportLCL.put("addCharge:THC:CBM", "S_THC");
    exportLCL.put("addCharge:EBS:CBM", "S_EBS");
    exportLCL.put("addCharge:LSS:CBM", "S_LSS");
    exportLCL.put("addCharge:RR:CBM", "S_RR");
    exportLCL.put("addCharge:GRI:CBM", "S_GRI");
    exportLCL.put("addCharge:DDC:CBM", "S_DDC");
    exportLCL.put("addCharge:B_AFR:SET", "S_B_AFR");
    exportLCL.put("addCharge:BILL:SET", "S_BILL");

    if (this.additionalCharges != null) {
      this.additionalCharges.clear();
    } else {
      this.additionalCharges = new ArrayList<>();
    }


    Map<String, String> addChargeMap = importLCL;
    if (Purpose.EXPORT.equals(purpose)) addChargeMap = exportLCL;

    for (String key : addChargeMap.keySet()) {
      if (record.containsKey(key)) {
        double amount = 0;
        String note = "";
        try {
          amount = record.getDouble(key, 0d);
        } catch (Exception e) {
          note = record.getString(key, "");
        }
        if (amount == 0 && note.isEmpty() && isNew()) continue;
        final String[] split = key.split(":");
        String name = split[1];
        boolean match = false;
        for (TransportAdditionalCharge next : getAdditionalCharges()) {
          if (name.equals(next.getLabel())) {
            next.setUnitPrice(amount);
            next.setNote(note);
            match = true;
            break;
          }
        }
        if (!match) {
          final TransportAdditionalCharge addCharge = getTransportAdditionalCharge(name, split, amount);
          addCharge.setName(addChargeMap.get(key));
          addCharge.setNote(note);
          withAdditionalCharge(addCharge);
        }
      }
    }
    return this;
  }

  private TransportAdditionalCharge getTransportAdditionalCharge(String name, String[] split, double amount) {
    TransportAdditionalCharge addCharge = new TransportAdditionalCharge(name.toUpperCase());
    String unit = split[2];
    addCharge.setPurpose(purpose);
    if (Purpose.EXPORT.equals(purpose)) addCharge.setTarget(ChargeTarget.ORIGIN);
    if (Purpose.IMPORT.equals(purpose)) addCharge.setTarget(ChargeTarget.DESTINATION);
    addCharge.setUnitPrice(amount);
    addCharge.setQuantity(1);
    addCharge.setCurrency("USD");
    addCharge.setTotal(amount);
    addCharge.setUnit(unit);
    return addCharge;
  }

  @Override
  public String getServiceType() {
    return "LCL";
  }

  @Override
  public String getSequence() {
    return SEQUENCE;
  }

  @Override
  public void set(ClientInfo client, Company company) {
    super.set(client, company);
    set(client, company, additionalCharges);
    set(client, company, commissionDistributions);
    set(client, company, transportFrequencies);
  }
}