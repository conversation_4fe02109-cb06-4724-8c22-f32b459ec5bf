package cloud.datatp.fforwarder.price.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.MappedSuperclass;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.ShareableCompanyEntity;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.company.settings.unit.entity.Unit;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.settings.currency.entity.Currency;

import java.io.Serial;

@MappedSuperclass
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class BaseAdditionalCharge extends ShareableCompanyEntity  {
  @Serial
  private static final long serialVersionUID = 1L;

  @NotNull
  protected String                  name;

  protected String                  label;

  @Enumerated(EnumType.STRING)
  @Column(name = "edit_mode")
  protected EditMode                editMode = EditMode.DRAFT;

  protected double                  quantity;

  protected String                  unit;

  @Column(name = "tax_rate")
  protected double                  taxRate;

  protected String                  currency;

  public <T extends BaseAdditionalCharge> T withQuantity(double quantity) {
    this.quantity = quantity;
    return (T) this;
  }

  public <T extends BaseAdditionalCharge> T withTaxRate(double taxRate) {
    this.taxRate = taxRate;
    return (T) this;
  }

  public <T extends BaseAdditionalCharge> T withCurrency(Currency currency) {
    this.currency = currency.getName();
    return (T) this;
  }

  public <T extends BaseAdditionalCharge> T withCurrency(String name) {
    this.currency = name;
    return (T) this;
  }

  public <T extends BaseAdditionalCharge> T withUnit(Unit unit) {
    this.unit = unit.getName();
    return (T) this;
  }

  public <T extends BaseAdditionalCharge> T withPrivateShareableScope() {
    setShareable(ShareableScope.PRIVATE);
    return (T) this;
  }
  
  public String identify() { return name; }
}