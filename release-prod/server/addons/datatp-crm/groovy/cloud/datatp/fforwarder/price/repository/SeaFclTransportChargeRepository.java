package cloud.datatp.fforwarder.price.repository;

import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import net.datatp.module.data.db.entity.StorageState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface SeaFclTransportChargeRepository extends JpaRepository<SeaFclTransportCharge, Serializable> {

  @Query("SELECT s FROM SeaFclTransportCharge s WHERE s.code = :code")
  SeaFclTransportCharge getByCode(@Param("code") String code);

  @Query("SELECT s FROM SeaFclTransportCharge s WHERE s.companyId = :companyId")
  List<SeaFclTransportCharge> findByCompanyId(@Param("companyId") Long companyId);

  @Modifying
  @Query("UPDATE SeaFclTransportCharge s SET s.storageState = :state WHERE s.id IN (:ids)")
  int setStorageState(@Param("ids") List<Long> ids, @Param("state") StorageState state);

  @Query("SELECT s FROM SeaFclTransportCharge s WHERE s.id IN (:ids)")
  List<SeaFclTransportCharge> findByIds(@Param("ids") List<Long> ids);

  @Query("SELECT s FROM SeaFclTransportCharge s WHERE s.companyId = :companyId AND s.validTo < :currentDate AND s.storageState = 'ACTIVE'")
  List<SeaFclTransportCharge> findSeaFclTransportChargesExpired(@Param("companyId") Long companyId, @Param("currentDate") Date currentDate);

}