package cloud.datatp.fforwarder.price;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaLclTransportCharge;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.monitor.activity.StatisticService;
import net.datatp.module.monitor.activity.entity.StatisticKey;
import net.datatp.module.resource.location.LocationLogic;
import net.datatp.module.service.BaseComponent;
import net.datatp.util.ds.MapObject;

@Slf4j
@Getter
@Service("SeaPriceService")
public class SeaPriceService extends BaseComponent {

  @Autowired
  private SeaPriceLogic logic;

  @Autowired
  private LocationLogic locationLogic;

  @Autowired
  private SeqService seqService;
  
  @Autowired
  private StatisticService statService;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(SeaFclTransportCharge.SEQUENCE, 10);
    seqService.createIfNotExists(SeaLclTransportCharge.SEQUENCE, 10);
  }
  // ==================================================
  // LCL
  // ==================================================
  @Transactional(readOnly = true)
  public SeaLclTransportCharge getSeaLclTransportChargeById(ClientInfo client, Company company, Long id) {
    return logic.getSeaLclTransportChargeById(client, company, id);
  }

  @Transactional
  public SeaLclTransportCharge saveSeaLclTransportCharge(ClientInfo client, Company company, SeaLclTransportCharge charge) {
    return logic.saveSeaLclTransportCharge(client, company, charge);
  }

  @Transactional
  public List<MapObject> saveSeaLclTransportCharges(
    ClientInfo client, Company company, List<MapObject> modified) {
    return logic.saveSeaLclModifyRecords(client, company, modified);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchSeaLclTransportCharge(ClientInfo client, Company company, SqlQueryParams params) {
    String fromLocationCode = params.getString("fromLocationCode");
    String toLocationCode   = params.getString("toLocationCode");
    StatisticKey key =
            new StatisticKey(new Date(), client, "fforwarder:pricing", "search-lcl-price")
                    .withSource(fromLocationCode)
                    .withDestination(toLocationCode);
    statService.incr(client, key, 1);
    return logic.searchSeaTransportCharge(client, company, params, SeaLclTransportCharge.class);
  }

  @Transactional
  public boolean changeSeaLclStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.changeSeaLclStorageState(client, req);
  }

  @Transactional
  public boolean deleteSeaLclTransportCharges(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteLclPriceByIds(client, company, ids);
  }

  @Transactional(readOnly = true)
  public List<SeaLclTransportCharge> findSeaLclTransportChargeByIds(ClientInfo client, Company company, List<Long> ids) {
    return logic.findSeaLclTransportChargeByIds(client, company, ids);
  }

  @Transactional(readOnly = true)
  public SeaFclTransportCharge getSeaFclTransportChargeById(ClientInfo client, Company company, Long id) {
    return logic.getSeaFclTransportChargeById(client, company, id);
  }

  @Transactional(readOnly = true)
  public List<SeaFclTransportCharge> findSeaFclTransportChargeByIds(ClientInfo client, Company company, List<Long> ids) {
    return logic.findSeaFclTransportChargeByIds(client, company, ids);
  }

  @Transactional
  public SeaFclTransportCharge saveSeaFclTransportCharge(ClientInfo client, Company company, SeaFclTransportCharge charge) {
    return logic.saveSeaFclTransportCharge(client, company, charge);
  }

  public List<SqlMapRecord> searchSeaFclTransportCharge(ClientInfo client, Company company, SqlQueryParams params) {
    String fromLocationCode = params.getString("fromLocationCode");
    String toLocationCode   = params.getString("toLocationCode");
    StatisticKey key =
            new StatisticKey(new Date(), client, "fforwarder:pricing", "search-fcl-price")
                    .withSource(fromLocationCode)
                    .withDestination(toLocationCode);
    statService.incr(client, key, 1);
    return logic.searchSeaTransportCharge(client, company, params, SeaFclTransportCharge.class);
  }

  @Transactional
  public boolean changeSeaFclStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.changeSeaFclStorageState(client, req);
  }

  @Transactional
  public boolean deleteSeaFclTransportCharges(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteFclPriceByIds(client, company, ids);
  }

}