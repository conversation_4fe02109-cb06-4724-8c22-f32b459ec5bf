package cloud.datatp.fforwarder.price.http;

import cloud.datatp.fforwarder.price.entity.*;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.TransportationMode;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;

import java.util.List;

public class Params {

  @Getter
  @Setter
  @NoArgsConstructor
  static public class XlsxPriceParserReq {
    @JsonProperty("uploadResource")
    private UploadResource uploadResource;

    private TransportationMode mode;
    private Purpose purpose;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  static public class ComputePurpose {
    private TransportationMode mode;
    private String fromLocationCode;
    private String toLocationCode;
  }

  @Getter
  @Setter
  @NoArgsConstructor
  static public class SaveModifiedPrice {
    private List<MapObject> records;
    private TransportationMode mode;
  }

  @Getter
  static public class ShareableUpdate {
    private TransportationMode mode;
    private ShareableScope scope;
    private List<Long> targetIds;

    public String computeTargetTableName() {
      if (TransportationMode.isSeaFCLTransport(mode)) return SeaFclTransportCharge.TABLE_NAME;
      if (TransportationMode.isSeaLCLTransport(mode)) return SeaLclTransportCharge.TABLE_NAME;
      if (TransportationMode.isAirTransport(mode)) return AirTransportCharge.TABLE_NAME;
      if (TransportationMode.isTruckContainer(mode)) return TruckContainerTransportCharge.TABLE_NAME;
      if (TransportationMode.isTruckRegular(mode)) return TruckRegularTransportCharge.TABLE_NAME;
      throw RuntimeError.IllegalArgument("Unsupported mode: " + mode);
    }
  }


}