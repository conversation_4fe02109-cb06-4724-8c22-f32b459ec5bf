package cloud.datatp.fforwarder.price.entity;

import java.io.Serial;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(
  name = CustomClearanceSurCharge.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = CustomClearanceSurCharge.TABLE_NAME + "_name",
      columnNames = { "company_id", "name", "cc_type" })
  },
  indexes = {
    @Index(columnList = "name")
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class CustomClearanceSurCharge extends BaseAdditionalCharge  {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_price_cc_sur_charge";
  
  public static enum CustomClearanceSurChargeMethod { QUANTITY }
  
  @Enumerated(EnumType.STRING)
  @Column(name = "cc_type")
  private CustomClearanceType            customClearanceType;
  
  @Enumerated(EnumType.STRING)
  private CustomClearanceSurChargeMethod method = CustomClearanceSurChargeMethod.QUANTITY;
  
  public CustomClearanceSurCharge(String name, String label) {
    this.name      = name;
    this.label     = label;
  }
}