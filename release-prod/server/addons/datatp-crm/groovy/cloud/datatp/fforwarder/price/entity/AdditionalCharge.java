package cloud.datatp.fforwarder.price.entity;

import java.io.Serial;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import cloud.datatp.fforwarder.price.common.ChargeTarget;

import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.TransportationMode;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.company.settings.unit.entity.Unit;
import net.datatp.module.settings.currency.entity.Currency;
import net.datatp.util.ds.MapObject;

@Entity
@Table(
  name = AdditionalCharge.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = AdditionalCharge.TABLE_NAME + "_name",
      columnNames = { "company_id", "name", "transportation_mode", "cc_type" })
  },
  indexes = {
    @Index(columnList = "name")
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class AdditionalCharge  extends BaseAdditionalCharge  {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_price_additional_charge";

  public enum AdditionalChargeMethod  {
    QUANTITY, CHARGEABLE_WEIGHT, GROSS_WEIGHT, VOLUME;

    static public AdditionalChargeMethod parse(String token) {
      if(token == null) return QUANTITY;
      return valueOf(token.toUpperCase());
    }
  }

  public enum AdditionalChargeType    {
    LOCAL_CHARGE, SURCHARGE, OTHER_CHARGE;

    static public AdditionalChargeType parse(String token) {
      if(token == null) return OTHER_CHARGE;
      return valueOf(token.toUpperCase());
    }
  }

  @Enumerated(EnumType.STRING)
  @Column(name="transportation_mode")
  private TransportationMode      transportationMode;

  @Enumerated(EnumType.STRING)
  @Column(name = "cc_type")
  private CustomClearanceType     customClearanceType;

  @Enumerated(EnumType.STRING)
  private AdditionalChargeType    type   = AdditionalChargeType.OTHER_CHARGE;

  @Enumerated(EnumType.STRING)
  private AdditionalChargeMethod  method = AdditionalChargeMethod.QUANTITY;

  @Enumerated(EnumType.STRING)
  private ChargeTarget target = ChargeTarget.NONE;

  @Enumerated(EnumType.STRING)
  private Purpose purpose = Purpose.EXPORT;

  public AdditionalCharge(String name, String label) {
    this.name      = name;
    this.label     = label;
  }

  public AdditionalCharge withQuantity(double quantity) {
    this.quantity = quantity;
    return this;
  }

  public AdditionalCharge withTaxRate(double taxRate) {
    this.taxRate = taxRate;
    return this;
  }

  public AdditionalCharge withCurrency(Currency currency) {
    this.currency = currency.getName();
    return this;
  }

  public AdditionalCharge withCurrency(String name) {
    this.currency = name;
    return this;
  }

  public AdditionalCharge withUnit(Unit unit) {
    this.unit = unit.getName();
    return this;
  }

  public AdditionalCharge withPrivateShareableScope() {
    setShareable(ShareableScope.PRIVATE);
    return this;
  }
  public AdditionalCharge computeFromMapObject(MapObject record) {
    if(record == null) return this;
    shareable = ShareableScope.ORGANIZATION;
    name = record.getString("name", null);
    label = record.getString("label", null);
    quantity = record.getDouble("quantity", null);
    unit = record.getString("unit", null);
    taxRate = record.getDouble("taxRate", null);
    currency = record.getString("currency", "USD");
    
    final String transportationModeStr = record.getString("transportationMode", "");
    if (!transportationModeStr.isEmpty()) transportationMode = TransportationMode.valueOf(transportationModeStr);
    
    final String customClearanceTypeStr = record.getString("customClearanceType", "");
    if (!customClearanceTypeStr.isEmpty()) customClearanceType = CustomClearanceType.parse(customClearanceTypeStr);
    
    final String typeStr = record.getString("type", "");
    if (!typeStr.isEmpty())  type = AdditionalChargeType.parse(typeStr);
    
    final String methodStr = record.getString("method", ""); 
    if (!methodStr.isEmpty()) method = AdditionalChargeMethod.parse(methodStr);
    
    final String targetStr = record.getString("target", "");
    if (!targetStr.isEmpty())  target = ChargeTarget.parse(targetStr);
    
    final String purposeStr = record.getString("purpose", "");
    if (!purposeStr.isEmpty()) purpose = Purpose.parse(purposeStr);
    
    return this;
  }
}