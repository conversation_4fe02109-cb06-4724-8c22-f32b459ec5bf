package cloud.datatp.fforwarder.price.repository;

import cloud.datatp.fforwarder.price.entity.TruckRegularTransportCharge;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.entity.StorageState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Repository
public interface TruckRegularTransportChargeRepository extends JpaRepository<TruckRegularTransportCharge, Serializable> {

  @Query("SELECT t FROM TruckRegularTransportCharge t WHERE t.id IN (:ids)")
  List<TruckRegularTransportCharge> findByIds(@Param("ids") List<Long> ids);

  @Query("SELECT t FROM TruckRegularTransportCharge t WHERE t.companyId = :companyId")
  List<TruckRegularTransportCharge> findByCompanyId(@Param("companyId") Long companyId);

  @Modifying
  @Query("UPDATE TruckRegularTransportCharge t SET t.storageState = :state WHERE t.id IN (:ids)")
  int setStorageState(@Param("ids") List<Long> ids, @Param("state") StorageState state);

  @Query("SELECT t FROM TruckRegularTransportCharge t WHERE t.companyId = :companyId AND t.validTo < :currentDate AND t.storageState = 'ACTIVE'")
  List<TruckRegularTransportCharge> findTruckRegularTransportChargeExpired(@Param("companyId") Long companyId, @Param("currentDate") Date currentDate);

}