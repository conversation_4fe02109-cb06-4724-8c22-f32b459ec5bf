package cloud.datatp.fforwarder.price.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

class BulkCargoInquiryRequestSql extends Executor {

    public class SearchBulkCargoInquiryRequest extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                WITH filtered_requests AS (
                    SELECT c.*, c.saleman_branch_name
                    FROM lgc_price_bulk_cargo_inquiry_request c
                    WHERE ${FILTER_BY_STORAGE_STATE("c", sqlParams)}
                        ${AND_FILTER_BY_OPTION("c.status", "status", sqlParams)}
                        ${AND_FILTER_BY_PARAM('c.company_id', 'companyIdParam', sqlParams)}
                        ${AND_FILTER_BY_PARAM('c.saleman_account_id', 'salemanAccountId', sqlParams)}
                        ${AND_FILTER_BY_PARAM('c.pricing_account_id', 'pricingAccountId', sqlParams)}
                        ${AND_SEARCH_BY_PARAMS(['c.code', "c.mail_subject", "c.mail_to", "c.mail_cc"], "search", sqlParams)}
                        ${addAndClause(sqlParams, "mailPattern", "(c.mail_to ILIKE '%' || :mailPattern || '%' OR c.pricing_account_id = :accessAccountId)")}
                        ${AND_FILTER_BY_RANGE("c.request_date", "requestDate", sqlParams)}
                        ${AND_FILTER_BY_RANGE("c.pricing_date", "pricingDate", sqlParams)}
                        AND (
                            'System' = :space 
                            OR ('Company' = :space AND c.company_id = :companyId)
                            OR ('User' = :space AND c.company_id = :companyId AND EXISTS (
                                SELECT 1 
                                FROM lgc_price_bulk_cargo_inquiry_request r 
                                WHERE :accessAccountId IN (r.saleman_account_id, r.pricing_account_id)
                               )
                            )
                        )
                )
                SELECT 
                    r.*
                FROM filtered_requests r
                ORDER BY r.request_date DESC
                ${MAX_RETURN(sqlParams)}
            """
            return query;
        }
    }

    public class SearchBulkCargoInquiryRequestSpace extends ExecutableSqlBuilder {
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
                WITH filtered_requests AS (
                    SELECT c.*, cc.label as company_label
                    FROM lgc_price_bulk_cargo_inquiry_request c
                    JOIN company_company cc ON cc.id = c.company_id
                    WHERE ${FILTER_BY_STORAGE_STATE("c", sqlParams)}
                      ${AND_FILTER_BY_OPTION("c.status", "status", sqlParams)}
                      ${AND_FILTER_BY_RANGE("c.request_date", "requestDate", sqlParams)}
                      ${AND_FILTER_BY_RANGE("c.pricing_date", "pricingDate", sqlParams)}
                      ${AND_FILTER_BY_PARAM("c.id", "requestIds", sqlParams)}
                      AND (
                        'System' = :space 
                        OR ('Company' = :space AND c.company_id = :companyId)
                        OR ('User' = :space AND c.company_id = :companyId AND EXISTS (
                            SELECT 1 
                            FROM account_account acc 
                            WHERE acc.id IN (c.saleman_account_id, c.pricing_account_id)
                              AND acc.id = :accessAccountId
                            )
                        )
                      )
                )
                SELECT 
                    r.*,
                    r.cargo_type                           as purpose,
                    'Cargo'                                as mode,
                    'lgc_price_bulk_cargo_inquiry_request' as entity_name
                FROM filtered_requests r
                ORDER BY r.request_date DESC
                ${MAX_RETURN(sqlParams)}
            """
            return query;
        }
    }

    public BulkCargoInquiryRequestSql() {
        register(new SearchBulkCargoInquiryRequest());
        register(new SearchBulkCargoInquiryRequestSpace());
    }

}