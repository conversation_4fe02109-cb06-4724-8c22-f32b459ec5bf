package cloud.datatp.fforwarder.price.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

class ContainerPriceSql extends Executor {

    public class SearchContainerPrice extends ExecutableSqlBuilder {
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
                SELECT  
                  charge.id                                     AS id,
                  charge.pickup_location_code                   AS pickup_location_code,
                  charge.pickup_location_label                  AS pickup_location_label,
                  charge.pickup_address                         AS pickup_address,
                  charge.delivery_location_code                 AS delivery_location_code,
                  charge.delivery_location_label                AS delivery_location_label,
                  charge.delivery_address                       AS delivery_address,
                  charge.delivery_address_normalized            AS delivery_address_normalized,
                  charge.priority                               AS priority,
                  
                  charge.target_reference_id                    AS target_reference_id,
                  charge.target_reference_label                 AS target_reference_label,
                  charge.target_customer                        AS target_customer,
                  
                  charge.carrier_partner_id                     AS carrier_partner_id,
                  charge.carrier_label                          AS carrier_label,
                  
                  charge.assignee_account_id                    AS assignee_account_id,
                  charge.assignee_label                         AS assignee_label,
                  charge.km_2_way                               AS km2way,
                  charge.oil_price_in_effect                    AS oil_price_in_effect,
            
                  -- Tariff
                  charge.cont_dry_20_less_10_ton_price          AS cont_dry_20_lt_10_ton_price,
                  charge.cont_dry_20_geq_10_ton_price           AS cont_dry_20_geq_10_ton_price,
                  charge.cont_dry_20_geq_17_ton_price           AS cont_dry_20_geq_17_ton_price,
                  charge.cont_dry_40_less_17_ton_price          AS cont_dry_40_lt_17_ton_price,
                  charge.cont_dry_40_geq_17_ton_price           AS cont_dry_40_geq_17_ton_price,
            
                  charge.cont_high_cube_20_less_17_ton_price    AS cont_high_cube_20_lt_17_ton_price,
                  charge.cont_high_cube_20_geq_17_ton_price     AS cont_high_cube_20_geq_17_ton_price,
                  charge.cont_high_cube_40_less_17_ton_price    AS cont_high_cube_40_lt_17_ton_price,
                  charge.cont_high_cube_40_geq_17_ton_price     AS cont_high_cube_40_geq_17_ton_price,
            
                  charge.cont_reefer_20_less_17_ton_price       AS cont_reefer_20_lt_17_ton_price,
                  charge.cont_reefer_20_geq_17_ton_price        AS cont_reefer_20_geq_17_ton_price,
                  charge.cont_reefer_40_less_17_ton_price       AS cont_reefer_40_lt_17_ton_price,
                  charge.cont_reefer_40_geq_17_ton_price        AS cont_reefer_40_geq_17_ton_price,
            
                  charge.cont_open_top_20_less_17_ton_price     AS cont_open_top_20_lt_17_ton_price,
                  charge.cont_open_top_20_geq_17_ton_price      AS cont_open_top_20_geq_17_ton_price,
                  charge.cont_open_top_40_less_17_ton_price     AS cont_open_top_40_lt_17_ton_price,
                  charge.cont_open_top_40_geq_17_ton_price      AS cont_open_top_40_geq_17_ton_price,
            
                  charge.cont_flat_rack_20_less_17_ton_price    AS cont_flat_rack_20_lt_17_ton_price,
                  charge.cont_flat_rack_20_geq_17_ton_price     AS cont_flat_rack_20_geq_17_ton_price,
                  charge.cont_flat_rack_40_less_17_ton_price    AS cont_flat_rack_40_lt_17_ton_price,
                  charge.cont_flat_rack_40_geq_17_ton_price     AS cont_flat_rack_40_geq_17_ton_price,
            
                  charge.cont_tank_20_less_17_ton_price         AS cont_tank_20_lt_17_ton_price,
                  charge.cont_tank_20_geq_17_ton_price          AS cont_tank_20_geq_17_ton_price,
                  charge.cont_tank_40_less_17_ton_price         AS cont_tank_40_lt_17_ton_price,
                  charge.cont_tank_40_geq_17_ton_price          AS cont_tank_40_geq_17_ton_price,
                  -- End Tariff
            
                  charge.currency                               AS currency,
                  charge.valid_from                             AS valid_from,
                  charge.valid_to                               AS valid_to,
                  charge.note                                   AS note,
            
                  charge.storage_state                          AS storage_state,
                  charge.edit_mode                              AS edit_mode,
                  charge.shareable                              AS shareable,
                  charge.created_by                             AS created_by,
                  charge.created_time                           AS created_time,
                  charge.modified_by                            AS modified_by,
                  charge.modified_time                          AS modified_time,                            
                  charge.company_id                             AS company_id                            
                FROM lgc_price_truck_container_charge AS charge
                WHERE
                  ${FILTER_BY_STORAGE_STATE('charge', sqlParams)}
                  AND charge.is_cbt_transportation IS FALSE
                  ${AND_FILTER_BY_PARAM('charge.pickup_location_code', 'pickupLocationCode', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.delivery_location_code', 'deliveryLocationCode', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.assignee_account_id', 'pricingCreatorId', sqlParams)}
                  ${AND_FILTER_BY_PARAM('charge.carrier_partner_id', 'carrierPartnerId', sqlParams)}
                  ${AND_FILTER_BY_RANGE('charge.modified_time', 'modifiedTime', sqlParams)}
                  AND (
                    (charge.shareable = 'COMPANY' AND charge.company_id = :companyId) OR 
                    (
                        charge.shareable = 'DESCENDANTS' AND charge.company_id IN (
                            SELECT 
                                distinct cc.id 
                            FROM company_company cc 
                            WHERE cc.parent_id IN (:companyIds) OR cc.id IN (:companyIds)
                    )) OR
                    (charge.shareable = 'ORGANIZATION')
                  )
                ORDER BY charge.delivery_address_normalized ASC, 
                    charge.modified_time DESC, charge.pickup_location_code, charge.delivery_location_code, 
                    charge.priority DESC
                ${MAX_RETURN(sqlParams)}
              """
            return query;
        }
    }

    public ContainerPriceSql() {
        register(new SearchContainerPrice());
    }
}