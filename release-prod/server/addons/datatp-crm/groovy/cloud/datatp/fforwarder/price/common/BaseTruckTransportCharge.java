package cloud.datatp.fforwarder.price.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.MappedSuperclass;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.ShareableCompanyEntity;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.util.bean.BeanUtil;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

import java.io.Serial;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@MappedSuperclass
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public abstract class BaseTruckTransportCharge extends ShareableCompanyEntity {

  public static final String PRICE_SEQUENCE = "lgc:lgc_price_truck_charge";

  @Serial
  private static final long serialVersionUID = 1L;

  public static enum ChargeTargetCustomer {
    Partner, PartnerGroup;
  }

  @Column(name = "is_cbt_transportation")
  protected boolean isCBT;

  @Column(name = "target_customer")
  @Enumerated(EnumType.STRING)
  protected ChargeTargetCustomer targetCustomer = ChargeTargetCustomer.Partner;

  @Column(name = "target_reference_id")
  protected Long targetReferenceId;

  @Column(name = "target_reference_label")
  protected String targetReferenceLabel;

  @Column(name = "handling_agent_partner_id")
  protected Long handlingAgentPartnerId;

  @Column(name = "handling_agent_partner_label")
  protected String handlingAgentPartnerLabel;

  @Column(name = "carrier_route")
  protected String carrierRoute;

  @Column(name = "carrier_partner_id")
  protected Long carrierPartnerId;

  @Column(name = "carrier_label")
  protected String carrierLabel;

  protected String label = "N/A";

  @Column(name = "km_2_way")
  protected double km2way;

  @Column(name = "oil_price_in_effect")
  protected double oilPriceInEffect;

  @Column(name = "pickup_province")
  protected String pickupProvince;

  @Column(name = "pickup_location_code")
  protected String pickupLocationCode;

  @Column(name = "pickup_location_label")
  protected String pickupLocationLabel;

  @Column(name = "pickup_address")
  protected String pickupAddress;

  @Column(name = "delivery_province")
  protected String deliveryProvince;

  @Column(name = "delivery_location_code")
  protected String deliveryLocationCode;

  @Column(name = "delivery_location_label")
  protected String deliveryLocationLabel;

  @Column(name = "delivery_address")
  protected String deliveryAddress;

  protected String route;

  @Column(name = "assignee_account_id")
  protected Long assigneeAccountId;

  @Column(name = "assignee_label")
  protected String assigneeLabel;

  @Enumerated(EnumType.STRING)
  @Column(name = "edit_mode")
  protected EditMode editMode = EditMode.VALIDATED;

  protected String currency;

  @Column(length = 1024 * 32)
  protected String note;

  @Setter(value = AccessLevel.NONE)
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "valid_from")
  protected Date validFrom;

  @Setter(value = AccessLevel.NONE)
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "valid_to")
  protected Date validTo;

  @Column(name = "transit_border")
  protected String transitBorder;

  @Column(name = "frequency")
  protected String frequency;

  @Column(name = "transit_time")
  protected String transitTime;

  protected void mapFrom(MapObject record) {
    shareable = ShareableScope.COMPANY;
    isCBT = record.getBoolean("isCBT", false);
    pickupLocationCode = record.getString("pickupLocationCode", null);
    pickupLocationLabel = record.getString("pickupLocationLabel", null);
    pickupAddress = record.getString("pickupAddress", null);
    pickupProvince = record.getString("pickupProvince", null);

    deliveryLocationCode = record.getString("deliveryLocationCode", null);
    deliveryLocationLabel = record.getString("deliveryLocationLabel", null);
    deliveryAddress = record.getString("deliveryAddress", null);
    deliveryProvince = record.getString("deliveryProvince", null);

    km2way = record.getDouble("km2way", 0.0);
    oilPriceInEffect = record.getDouble("oilPriceInEffect", 0.0);

    carrierRoute = record.getString("carrierRoute", null);

    targetCustomer = ChargeTargetCustomer.valueOf(record.getString("targetCustomer", "Partner"));

    targetReferenceId = record.getLong("targetReferenceId", null);
    targetReferenceLabel = record.getString("targetReferenceLabel", null);

    carrierPartnerId = record.getLong("carrierPartnerId", null);
    carrierLabel = record.getString("carrierLabel", null);

    handlingAgentPartnerId = record.getLong("handlingAgentPartnerId", null);
    handlingAgentPartnerLabel = record.getString("handlingAgentPartnerLabel", null);

    currency = record.getString("currency", "VND");

    final String validFrom = record.getString("validFrom");
    if (StringUtil.isNotEmpty(validFrom)) setValidFrom(DateUtil.parseCompactDate(validFrom));

    final String validTo = record.getString("validTo");
    if (StringUtil.isNotEmpty(validTo)) setValidTo(DateUtil.parseCompactDate(validTo));

    transitBorder = record.getString("transitBorder", null);
    frequency = record.getString("frequency", null);
    transitTime = record.getString("transitTime", null);

    note = record.getString("note", "");
    route = pickupProvince + " - " + deliveryProvince;
  }

  public void setValidTo(Date validTo) {
    if (validTo == null) validTo = new Date();
    LocalDate localDate = validTo.toInstant()
        .atZone(ZoneId.systemDefault())
        .toLocalDate();
    LocalDateTime endOfDay = localDate.atTime(LocalTime.MAX);
    this.validTo = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
  }

  public void setValidFrom(Date validFrom) {
    if (validFrom == null) validFrom = new Date();
    LocalDate localDate = validFrom.toInstant()
        .atZone(ZoneId.systemDefault())
        .toLocalDate();
    LocalDateTime endOfDay = localDate.atTime(LocalTime.MIN);
    this.validFrom = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
  }

  protected void copy(BaseTruckTransportCharge other) {
    List<Field> fields = BeanUtil.getFields(BaseTruckTransportCharge.class);
    BeanUtil.copyAllFields(this, other, fields);
  }

  public void setCurrency(String currency) {
    if (StringUtil.isEmpty(currency)) this.currency = "VND";
    else this.currency = currency;
  }

  public BaseTruckTransportCharge withPrivateShareableScope() {
    setShareable(ShareableScope.PRIVATE);
    return this;
  }

  public boolean isSameCompany(Long companyId) {
    return this.companyId.equals(companyId);
  }

}