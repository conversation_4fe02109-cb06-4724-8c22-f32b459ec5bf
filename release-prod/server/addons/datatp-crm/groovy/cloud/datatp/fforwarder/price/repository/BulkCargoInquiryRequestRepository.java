package cloud.datatp.fforwarder.price.repository;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import cloud.datatp.fforwarder.price.entity.BulkCargoInquiryRequest;

public interface BulkCargoInquiryRequestRepository extends JpaRepository<BulkCargoInquiryRequest, Serializable> {

  @Query("SELECT p FROM BulkCargoInquiryRequest p WHERE p.companyId = :companyId")
  List<BulkCargoInquiryRequest> findByCompany(@Param("companyId") Long companyId);

}