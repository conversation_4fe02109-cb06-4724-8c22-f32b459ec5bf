package cloud.datatp.fforwarder.price.common;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.ds.MapObject;

@Embeddable
@Getter @Setter
@NoArgsConstructor
public class SeaLCLPriceGroup {

  @Column(name = "freight_charge_lcl")
  private double freightChargeLCL;

  @Column(name = "minimum_charge_lcl")
  private double minimumChargeLCL;

  @Column(name = "less_2_cbm_price")
  private double less2CbmPrice;

  @Column(name = "less_3_cbm_price")
  private double less3CbmPrice;

  @Column(name = "less_5_cbm_price")
  private double less5CbmPrice;

  @Column(name = "less_7_cbm_price")
  private double less7CbmPrice;

  @Column(name = "less_10_cbm_price")
  private double less10CbmPrice;

  @Column(name = "geq_10_cbm_price")
  private double geq10CbmPrice;

  @Column(name = "freight_charge_lcl_note")
  private String freightChargeLCLNote;

  @Column(name = "minimum_charge_lcl_note")
  private String minimumChargeLCLNote;

  @Column(name = "less_2_cbm_price_note")
  private String less2CbmPriceNote;

  @Column(name = "less_3_cbm_price_note")
  private String less3CbmPriceNote;

  @Column(name = "less_5_cbm_price_note")
  private String less5CbmPriceNote;

  @Column(name = "less_7_cbm_price_note")
  private String less7CbmPriceNote;

  @Column(name = "less_10_cbm_price_note")
  private String less10CbmPriceNote;

  @Column(name = "geq_10_cbm_price_note")
  private String geq10CbmPriceNote;

  public SeaLCLPriceGroup computeFrom(MapObject record) {
    freightChargeLCLNote = record.getString("freightChargeLCLNote", "");
    minimumChargeLCLNote = record.getString("minimumChargeLCLNote", "");
    less2CbmPriceNote = record.getString("less2CbmPriceNote", "");
    less3CbmPriceNote = record.getString("less3CbmPriceNote", "");
    less5CbmPriceNote = record.getString("less5CbmPriceNote", "");
    less7CbmPriceNote = record.getString("less7CbmPriceNote", "");
    less10CbmPriceNote = record.getString("less10CbmPriceNote", "");
    geq10CbmPriceNote = record.getString("geq10CbmPriceNote", "");

    try {
      freightChargeLCL = record.getDouble("freightChargeLCL", 0D);
    } catch (RuntimeException ex) {
      freightChargeLCL = 0;
      freightChargeLCLNote = record.getString("freightChargeLCL", "");
    }

    try {
      minimumChargeLCL = record.getDouble("minimumChargeLCL", 0D);
    } catch (RuntimeException ex) {
      minimumChargeLCL = 0;
      minimumChargeLCLNote = record.getString("minimumChargeLCL", "");
    }

    try {
      less2CbmPrice = record.getDouble("less2CbmPrice", 0D);
    } catch (RuntimeException ex) {
      less2CbmPrice = 0;
      less2CbmPriceNote = record.getString("less2CbmPrice", "");
    }

    try {
      less3CbmPrice = record.getDouble("less3CbmPrice", 0D);
    } catch (RuntimeException ex) {
      less3CbmPrice = 0;
      less3CbmPriceNote = record.getString("less3CbmPrice", "");
    }

    try {
      less5CbmPrice = record.getDouble("less5CbmPrice", 0D);
    } catch (RuntimeException ex) {
      less5CbmPrice = 0;
      less5CbmPriceNote = record.getString("less5CbmPrice", "");
    }

    try {
      less7CbmPrice = record.getDouble("less7CbmPrice", 0D);
    } catch (RuntimeException ex) {
      less7CbmPrice = 0;
      less7CbmPriceNote = record.getString("less7CbmPrice", "");
    }

    try {
      less5CbmPrice = record.getDouble("less5CbmPrice", 0D);
    } catch (RuntimeException ex) {
      less5CbmPrice = 0;
      less5CbmPriceNote = record.getString("less5CbmPrice", "");
    }
    try {
      less10CbmPrice = record.getDouble("less10CbmPrice", 0D);
    } catch (RuntimeException ex) {
      less10CbmPrice = 0;
      less10CbmPriceNote = record.getString("less10CbmPrice", "");
    }

    try {
      geq10CbmPrice = record.getDouble("geq10CbmPrice", 0D);
    } catch (RuntimeException ex) {
      geq10CbmPrice = 0;
      geq10CbmPriceNote = record.getString("geq10CbmPrice", "");
    }

    if(freightChargeLCL != 0) {
      minimumChargeLCL = freightChargeLCL;
      less2CbmPrice = freightChargeLCL;
      less3CbmPrice = freightChargeLCL;
      less5CbmPrice = freightChargeLCL;
      less7CbmPrice = freightChargeLCL;
      less10CbmPrice = freightChargeLCL;
      geq10CbmPrice = freightChargeLCL;
    }

    return this;
  }

  public double computePriceValue(double chargeableVolume) {
    if(freightChargeLCL != 0) return freightChargeLCL;
    if (chargeableVolume < 1) {
      return minimumChargeLCL;
    } else if (chargeableVolume < 2) {
      return less2CbmPrice;
    } else if (chargeableVolume < 3) {
      return less3CbmPrice;
    } else if (chargeableVolume < 5) {
      return less5CbmPrice;
    } else if (chargeableVolume < 7) {
      return less7CbmPrice;
    } else if (chargeableVolume < 10) {
      return less10CbmPrice;
    } else {
      return geq10CbmPrice;
    }
  }

}