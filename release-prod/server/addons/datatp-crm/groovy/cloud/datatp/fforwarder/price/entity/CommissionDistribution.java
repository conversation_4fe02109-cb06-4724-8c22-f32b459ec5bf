package cloud.datatp.fforwarder.price.entity;

import cloud.datatp.fforwarder.settings.ContainerTypeDep;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyPersistable;
import net.datatp.module.partner.entity.Partner;
import net.datatp.module.settings.currency.entity.Currency;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;

import java.io.Serial;

@Entity
@Table(name = CommissionDistribution.TABLE_NAME)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class CommissionDistribution extends CompanyPersistable {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_price_commission";

  public enum CommissionDistributionType {
    EMPLOYEE, PARTNER, UNKNOWN;

    public static CommissionDistributionType parse(String token) {
      if (StringUtil.isBlank(token)) return UNKNOWN;
      return valueOf(token.toUpperCase());
    }
  }

  public enum CommissionDistributionMethod {
    QUANTITY, CHARGEABLE_WEIGHT, TOTAL_CHARGE, FINAL_CHARGE, FIXED
  }

  @Enumerated(EnumType.STRING)
  private CommissionDistributionType type;

  @Enumerated(EnumType.STRING)
  private CommissionDistributionMethod method = CommissionDistributionMethod.TOTAL_CHARGE;

  @Enumerated(EnumType.STRING)
  @Column(name = "container_type")
  private ContainerTypeDep containerType;

  private String label;

  @Column(name = "unit_price")
  private double unitPrice;

  private String unit;

  private double percentage;

  private double quantity;
  private double total;
  private String currency;

  @Column(name = "payable_to_id")
  private Long payableToId;

  @Column(name = "payable_to_label")
  private String payableToLabel;

  @Column(name="sea_fcl_charge_id", updatable=false, insertable=false)
  private Long seaFclChargeId;

  @Column(name="sea_lcl_charge_id", updatable=false, insertable=false)
  private Long seaLclChargeId;

  public CommissionDistribution(CommissionDistributionType type, double percentage) {
    this.type = type;
    this.percentage = percentage;
  }

  public CommissionDistribution(double amount) {
    type = CommissionDistributionType.PARTNER;
    quantity = 1;
    method = CommissionDistributionMethod.FIXED;
    currency = "USD";
    unitPrice = amount;
    total = unitPrice * quantity;
  }

  public CommissionDistribution withMethod(CommissionDistributionMethod method) {
    this.method = method;
    return this;
  }

  public CommissionDistribution withContainerType(ContainerTypeDep type) {
    this.containerType = type;
    if(StringUtil.isEmpty(label)) label = "Commission " + type.name();
    return this;
  }

  public CommissionDistribution withUnitPrice(double unitPrice) {
    this.unitPrice = unitPrice;
    return this;
  }

  public CommissionDistribution withPayableTo(Partner payableTo) {
    this.payableToId = payableTo.getId();
    this.payableToLabel = payableTo.getLabel();
    return this;
  }

  public CommissionDistribution withQuantity(double quantity) {
    this.quantity = quantity;
    return this;
  }

  public double getTotal() {
    total = quantity * unitPrice;
    return total;
  }

  public CommissionDistribution withCurrency(Currency currency) {
    this.currency = currency.getName();
    return this;
  }

  public Long getTransportPriceId() {
    Long chargeId = null;
    if(Objects.nonNull(seaFclChargeId)) chargeId = seaFclChargeId;
    if(Objects.nonNull(seaLclChargeId)) chargeId = seaLclChargeId;
    return chargeId;
  }

}