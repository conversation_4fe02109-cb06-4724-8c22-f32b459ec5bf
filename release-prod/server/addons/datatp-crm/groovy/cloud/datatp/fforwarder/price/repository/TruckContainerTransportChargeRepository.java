package cloud.datatp.fforwarder.price.repository;

import cloud.datatp.fforwarder.price.entity.TruckContainerTransportCharge;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.entity.StorageState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public interface TruckContainerTransportChargeRepository extends JpaRepository<TruckContainerTransportCharge, Serializable> {

  @Query("SELECT t FROM TruckContainerTransportCharge t WHERE t.companyId = :companyId")
  List<TruckContainerTransportCharge> findByCompanyId(@Param("companyId") Long companyId);

  @Modifying
  @Query("UPDATE TruckContainerTransportCharge t SET t.storageState = :state WHERE t.id IN (:ids)")
  int setStorageState(@Param("ids") List<Long> ids, @Param("state") StorageState state);

  @Query("SELECT t FROM TruckContainerTransportCharge t WHERE t.id IN (:ids)")
  List<TruckContainerTransportCharge> findByIds(@Param("ids") List<Long> ids);

  @Query("SELECT t FROM TruckContainerTransportCharge t WHERE t.companyId = :companyId AND t.validTo < :currentDate AND t.storageState = 'ACTIVE'")
  List<TruckContainerTransportCharge> findTruckContainerTransportChargeExpired(@Param("companyId") Long companyId, @Param("currentDate") Date currentDate);

}