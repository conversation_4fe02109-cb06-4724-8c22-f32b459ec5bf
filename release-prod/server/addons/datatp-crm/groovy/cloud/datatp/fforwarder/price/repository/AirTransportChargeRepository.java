package cloud.datatp.fforwarder.price.repository;

import cloud.datatp.fforwarder.price.entity.AirTransportCharge;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.entity.StorageState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Repository
public interface AirTransportChargeRepository extends JpaRepository<AirTransportCharge, Serializable> {

  @Query("SELECT a FROM AirTransportCharge a WHERE a.companyId = :companyId")
  List<AirTransportCharge> findByCompanyId(@Param("companyId") Long companyId);

  @Modifying
  @Query("update Employee e SET e.storageState = :state WHERE e.id IN :ids")
  int setStorageState(@Param("ids") Collection<Long> ids, @Param("state") StorageState state);

  @Query("SELECT a FROM AirTransportCharge a WHERE a.id IN (:ids)")
  List<AirTransportCharge> findByIds(@Param("ids") List<Long> ids);

  @Query("SELECT a FROM AirTransportCharge a WHERE a.companyId = :companyId AND a.validTo < :currentDate AND a.storageState = 'ACTIVE'")
  List<AirTransportCharge> findAirTransportChargesExpired(@Param("companyId") Long companyId, @Param("currentDate") Date currentDate);

}