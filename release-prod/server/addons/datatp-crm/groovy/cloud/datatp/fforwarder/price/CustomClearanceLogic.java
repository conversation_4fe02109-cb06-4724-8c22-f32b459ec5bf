package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.price.calculator.AdditionalChargeCalculator;
import cloud.datatp.fforwarder.price.calculator.CustomClearanceSurChargeCalculator;
import cloud.datatp.fforwarder.price.entity.BaseCustomClearance;
import cloud.datatp.fforwarder.price.entity.CustomClearanceAir;
import cloud.datatp.fforwarder.price.entity.CustomClearanceRail;
import cloud.datatp.fforwarder.price.entity.CustomClearanceSeaFcl;
import cloud.datatp.fforwarder.price.entity.CustomClearanceSeaLcl;
import cloud.datatp.fforwarder.price.entity.CustomClearanceSurCharge;
import cloud.datatp.fforwarder.price.repository.CustomClearanceAirRepository;
import cloud.datatp.fforwarder.price.repository.CustomClearanceRailRepository;
import cloud.datatp.fforwarder.price.repository.CustomClearanceSeaFclRepository;
import cloud.datatp.fforwarder.price.repository.CustomClearanceSeaLclRepository;
import cloud.datatp.fforwarder.price.repository.CustomClearanceSurChargeRepository;
import java.util.List;
import java.util.Objects;
import javax.sql.DataSource;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.ShareableUtil;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.ParamFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.query.SqlUtil;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.util.ds.Collections;
import net.datatp.util.error.RuntimeError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CustomClearanceLogic extends DAOService {
  @Autowired
  private CustomClearanceAirRepository ccAirRepo;

  @Autowired
  private CustomClearanceRailRepository ccRailRepo;

  @Autowired
  private CustomClearanceSeaLclRepository ccSeaLCLRepo;

  @Autowired
  private CustomClearanceSeaFclRepository ccSeaFCLRepo;

  @Autowired
  private CustomClearanceSurChargeRepository surChargeRepo;

  @Autowired
  private DataSource dataSource;

  // Common
  public <T extends BaseCustomClearance> List<SqlMapRecord> searchCustomClearances(
    ClientInfo client, Company company, Class<T> entity, SqlQueryParams params) {
    ShareableUtil.enrichShareableParams(client, company, params);
    params.addParam("draftMode", EditMode.DRAFT.toString());

    SqlQuery query =
      new SqlQuery()
        .ADD_TABLE( new EntityTable(entity).selectAllFields())
        .FILTER(SqlUtil.createShareableClause(entity))
        .FILTER(SearchFilter.isearch(entity, "code", "label"))
        .FILTER(new ParamFilter(entity, "terminalLocationCode", "=", "locationCode"))
        .FILTER(new ParamFilter(entity, "carrierPartnerId", "=", "carrier"))
        .FILTER(
          OptionFilter.storageState(entity),
          OptionFilter.create(entity, "editMode", EditMode.ALL),
          RangeFilter.date(entity, "validFrom"),
          RangeFilter.date(entity, "validTo"),
          RangeFilter.modifiedTime(entity))
        .ORDERBY(new String[] {"label", "modifiedTime"}, "modifiedTime", "DESC");
    if(params.hasParam("excludeDraftMode")) {
      query.FILTER( new ClauseFilter(entity, "editMode", "!=", ":draftMode"));
    }
    return query(client, query, params).getSqlMapRecords();
  }

  // Air

  public CustomClearanceAir saveCustomClearanceAir(ClientInfo client, Company company, CustomClearanceAir customClearance) {
    customClearance.set(client, company);
    AdditionalChargeCalculator.calculate(customClearance.getAdditionalCharges());
    CustomClearanceSurChargeCalculator.calculate(customClearance.getTransportCustomClearanceSurCharges());
    return ccAirRepo.save(customClearance);
  }

  public List<CustomClearanceAir> saveCustomClearanceAirs(ClientInfo client, Company company, List<CustomClearanceAir> charges) {
    if(Objects.isNull(charges)) return null;
    for(CustomClearanceAir dCharge : charges) {
      CustomClearanceAir charge = ccAirRepo.getById(dCharge.getId());
      charge.setPrice(dCharge.getPrice());
      saveCustomClearanceAir(client, company, charge);
    }
    return charges;
  }

  public boolean changeCustomClearanceAirStorageState(ClientInfo client, ChangeStorageStateRequest request) {
    List<Long> chargeIds = request.getEntityIds();
    if (chargeIds.isEmpty()) throw RuntimeError.IllegalArgument("No Records were selected!!");
    ccAirRepo.setStorageState(chargeIds, request.getNewStorageState());
    return true;
}

  // Rail

  public CustomClearanceRail saveCustomClearanceRail(ClientInfo client, Company company, CustomClearanceRail customClearance) {
    customClearance.set(client, company);
    AdditionalChargeCalculator.calculate(customClearance.getAdditionalCharges());
    CustomClearanceSurChargeCalculator.calculate(customClearance.getTransportCustomClearanceSurCharges());
    return ccRailRepo.save(customClearance);
  }

  public List<CustomClearanceRail> saveCustomClearanceRails(ClientInfo client, Company company, List<CustomClearanceRail> charges) {
    if(Objects.isNull(charges)) return null;
    for(CustomClearanceRail dCharge : charges) {
      CustomClearanceRail charge = ccRailRepo.getById(dCharge.getId());
      charge.setContainer20(dCharge.getContainer20());
      charge.setContainer40(dCharge.getContainer40());
      saveCustomClearanceRail(client, company, charge);
    }
    return charges;
  }

  public boolean changeCustomClearanceRailStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    ccRailRepo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  // Sea LCL

  public CustomClearanceSeaLcl saveCustomClearanceSeaLcl(ClientInfo client, Company company, CustomClearanceSeaLcl customClearance) {
    customClearance.set(client, company);
    AdditionalChargeCalculator.calculate(customClearance.getAdditionalCharges());
    CustomClearanceSurChargeCalculator.calculate(customClearance.getTransportCustomClearanceSurCharges());
    return ccSeaLCLRepo.save(customClearance);
  }

  public List<CustomClearanceSeaLcl> saveCustomClearanceSeaLcls(ClientInfo client, Company company, List<CustomClearanceSeaLcl> charges) {
    if(Objects.isNull(charges)) return null;
    for(CustomClearanceSeaLcl dCharge : charges) {
      CustomClearanceSeaLcl charge = ccSeaLCLRepo.getById(dCharge.getId());
      charge.setPrice(dCharge.getPrice());
      saveCustomClearanceSeaLcl(client, company, charge);
    }
    return charges;
  }

  public boolean changeCustomClearanceSeaLclStorageState(ClientInfo client, ChangeStorageStateRequest request) {
    List<Long> chargeIds = request.getEntityIds();
    if (chargeIds.isEmpty()) throw RuntimeError.IllegalArgument("No Records were selected!!");
    ccSeaLCLRepo.setStorageState(chargeIds, request.getNewStorageState());
    return true;
  }

  // FCL

  public CustomClearanceSeaFcl saveCustomClearanceSeaFcl(ClientInfo client, Company company, CustomClearanceSeaFcl customClearance) {
    customClearance.set(client, company);
    AdditionalChargeCalculator.calculate(customClearance.getAdditionalCharges());
    CustomClearanceSurChargeCalculator.calculate(customClearance.getTransportCustomClearanceSurCharges());
    return ccSeaFCLRepo.save(customClearance);
  }

  public List<CustomClearanceSeaFcl> saveCustomClearanceSeaFcls(ClientInfo client, Company company, List<CustomClearanceSeaFcl> charges) {
    if(Objects.isNull(charges)) return null;
    for(CustomClearanceSeaFcl dCharge : charges) {
      CustomClearanceSeaFcl charge = ccSeaFCLRepo.getById(dCharge.getId());
      charge.withPrice(dCharge.getContainer20(), dCharge.getContainer40());
      saveCustomClearanceSeaFcl(client, company, charge);
    }
    return charges;
  }

  public boolean changeCustomClearanceSeaFclStorageState(ClientInfo client, ChangeStorageStateRequest request) {
    List<Long> chargeIds = request.getEntityIds();
    if (chargeIds.isEmpty()) throw RuntimeError.IllegalArgument("No Records were selected!!");
    ccSeaFCLRepo.setStorageState(chargeIds, request.getNewStorageState());
    return true;
  }

  //Sur Charge

  public CustomClearanceSurCharge saveCustomClearanceSurCharge(
    ClientInfo client, Company company, CustomClearanceSurCharge surCharge) {
    surCharge.set(client, company);
    return surChargeRepo.save(surCharge);
  }

  public List<CustomClearanceSurCharge> saveCustomClearanceSurCharges(ClientInfo client, Company company, List<CustomClearanceSurCharge> charges) {
    if(Objects.isNull(charges)) return null;
    for(CustomClearanceSurCharge dCharge : charges) {
      CustomClearanceSurCharge charge = surChargeRepo.getById(dCharge.getId());
      charge.setLabel(dCharge.getLabel());
      charge.withQuantity(dCharge.getQuantity()).withTaxRate(dCharge.getTaxRate());
      saveCustomClearanceSurCharge(client, company, charge);
    }
    return charges;
  }

  public List<CustomClearanceSurCharge> searchCustomClearanceSurChargeCharge(ClientInfo client, Company company, SqlQueryParams params) {
    ShareableUtil.enrichShareableParams(client, company, params);

    SqlQuery query =
      new SqlQuery()
        .ADD_TABLE(new EntityTable(CustomClearanceSurCharge.class).selectAllFields())
        .FILTER(SqlUtil.createShareableClause(CustomClearanceSurCharge.class))
        .FILTER(
          SearchFilter.isearch(CustomClearanceSurCharge.class, "name", "label"))
        .FILTER(new ParamFilter(CustomClearanceSurCharge.class, "customClearanceType", "=" , "customClearanceType"))
        .FILTER(
          OptionFilter.storageState(CustomClearanceSurCharge.class),
          RangeFilter.modifiedTime(CustomClearanceSurCharge.class))
        .ORDERBY(new String[] {"label", "modifiedTime"}, "modifiedTime", "DESC");

    return query(client, query, params, CustomClearanceSurCharge.class);
  }

  public boolean changeAdditionalTransportStorageState(ClientInfo client, ChangeStorageStateRequest request) {
    List<Long> chargeIds = request.getEntityIds();
    if (chargeIds.isEmpty()) throw RuntimeError.IllegalArgument("No Records were selected!!");
    surChargeRepo.setStorageState(chargeIds, request.getNewStorageState());
    return true;
  }
  

  public List<CustomClearanceAir> findCustomClearanceAirByCompany(ClientInfo client, Company company) {
    return ccAirRepo.findByCompanyId(company.getId());
  }

  public List<CustomClearanceSeaLcl> findCustomClearanceSeaLCLByCompany(ClientInfo client, Company company) {
    return ccSeaLCLRepo.findByCompanyId(company.getId());
  }

  public List<CustomClearanceSeaFcl> findCustomClearanceSeaFCLByCompany(ClientInfo client, Company company) {
    return ccSeaFCLRepo.findByCompanyId(company.getId());
  }

  public List<CustomClearanceRail> findCustomClearanceRailByCompany(ClientInfo client, Company company) {
    return ccRailRepo.findByCompanyId(company.getId());
  }

  public boolean deleteCustomClearanceCharges(ClientInfo client, Company company, Class<? extends BaseCustomClearance> entity, List<Long> ids) {
    if (Collections.isEmpty(ids)) return false;
    DBConnectionUtil connectionUtil = new DBConnectionUtil(getJdbcDataSource());
    DeleteGraphBuilder graph = new DeleteGraphBuilder(connectionUtil, company.getId(), entity, ids);
    int count = graph.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count > 0;
  }

  public boolean deleteCustomClearanceSurCharges(ClientInfo client, Company company, List<Long> ids) {
    if (Collections.isEmpty(ids)) return false;
    DBConnectionUtil connectionUtil = new DBConnectionUtil(getJdbcDataSource());
    DeleteGraphBuilder graph = new DeleteGraphBuilder(connectionUtil, company.getId(), CustomClearanceSurCharge.class, ids);
    int count = graph.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count > 0;
  }
}