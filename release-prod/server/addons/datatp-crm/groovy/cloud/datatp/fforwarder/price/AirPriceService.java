package cloud.datatp.fforwarder.price;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.fforwarder.price.entity.AirTransportCharge;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.monitor.activity.StatisticService;
import net.datatp.module.monitor.activity.entity.StatisticKey;
import net.datatp.module.service.BaseComponent;
import net.datatp.util.ds.MapObject;

@Getter
@Service("AirPriceService")
public class AirPriceService extends BaseComponent {
  @Autowired
  private AirPriceLogic logic;

  @Autowired
  private SeqService seqService;

  @Autowired
  private StatisticService statService;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(AirTransportCharge.SEQUENCE, 10);
  }

  @Transactional(readOnly = true)
  public AirTransportCharge getById(ClientInfo client, Company company, Long id) {
    return logic.getById(client, company, id);
  }

  @Transactional(readOnly = true)
  public List<AirTransportCharge> getByIds(ClientInfo client, Company company, List<Long> ids) {
    return logic.findByIds(client, company, ids);
  }

  @Transactional
  public AirTransportCharge save(ClientInfo client, Company company, AirTransportCharge charge) {
    return logic.saveAirPrice(client, company, charge);
  }

  //TODO: Dan - review to clean this code, use method on Misc Component
  @Transactional
  public List<MapObject> saveAirTransportCharges(
    ClientInfo client, Company company, List<MapObject> modified) {
    return logic.saveAirTransportCharges(client, company, modified);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> search(ClientInfo client, Company company, SqlQueryParams params) {
    String fromLocationCode = params.getString("fromLocationCode");
    String toLocationCode   = params.getString("toLocationCode");
    StatisticKey key =
      new StatisticKey(new Date(), client, "fforwarder:pricing", "search-air-price")
        .withSource(fromLocationCode)
        .withDestination(toLocationCode);
    statService.incr(client, key, 1);
    return logic.search(client, company, params);
  }

  @Transactional
  public boolean updateStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return logic.updateStorageState(client, req);
  }

  @Transactional
  public boolean deleteByIds(ClientInfo client, Company company, List<Long> ids) {
    return logic.deleteByIds(client, company, ids);
  }

}