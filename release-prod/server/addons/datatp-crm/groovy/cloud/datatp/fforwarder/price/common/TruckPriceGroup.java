package cloud.datatp.fforwarder.price.common;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.ds.MapObject;

@Embeddable
@Getter @Setter
@NoArgsConstructor
public class TruckPriceGroup {

  public enum TruckType {
    Box, Reefer, Tarpaulin;
  }

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "type")
  private TruckType type = TruckType.Box;

  @Column(name = "truck1_ton25_price")
  private double truck1Ton25Price;

  @Column(name = "truck1_ton5_price")
  private double truck1Ton5Price;

  @Column(name = "truck2_ton5_price")
  private double truck2Ton5Price;

  @Column(name = "truck3_ton5_price")
  private double truck3Ton5Price;

  @Column(name = "truck5_ton_price")
  private double truck5TonPrice;

  @Column(name = "truck7_ton_price")
  private double truck7TonPrice;

  @Column(name = "truck8_ton_price")
  private double truck8TonPrice;

  @Column(name = "truck9_ton_price")
  private double truck9TonPrice;

  @Column(name = "truck10_ton_price")
  private double truck10TonPrice;

  @Column(name = "truck12_ton_price")
  private double truck12TonPrice;

  @Column(name = "truck15_ton_price")
  private double truck15TonPrice;

  @Column(name = "truck30_ton_price")
  private double truck30TonPrice;

  // Custom Clearance Fee
  @Column(name = "custom_fee_at_lang_son")
  private double customFeeAtLangSon;

  @Column(name = "custom_fee_at_pin_xiang")
  private double customFeeAtPinXiang;

  @Column(name = "stackable_usd_per_kg")
  private double stackableUSDPerKG;

  @Column(name = "nonstackable_usd_per_kg")
  private double nonstackableUSDPerKG;

  public TruckPriceGroup mapFrom(MapObject record) {
    setTruck1Ton25Price(record.getDouble("truck1Ton25Price", 0d));
    setTruck1Ton5Price(record.getDouble("truck1Ton5Price", 0d));

    truck2Ton5Price = record.getDouble("truck2Ton5Price", 0d);
    truck3Ton5Price = record.getDouble("truck3Ton5Price", 0d);
    truck5TonPrice = record.getDouble("truck5TonPrice", 0d);
    truck7TonPrice = record.getDouble("truck7TonPrice", 0d);
    truck8TonPrice = record.getDouble("truck8TonPrice", 0d);
    truck9TonPrice = record.getDouble("truck9TonPrice", 0d);
    truck10TonPrice = record.getDouble("truck10TonPrice", 0d);
    truck12TonPrice = record.getDouble("truck12TonPrice", 0d);
    setTruck15TonPrice(record.getDouble("truck15TonPrice", 0d));
    truck30TonPrice = record.getDouble("truck30TonPrice", 0d);

    customFeeAtLangSon = record.getDouble("customFeeAtLangSon", 0d);
    customFeeAtPinXiang = record.getDouble("customFeeAtPinXiang", 0d);

    stackableUSDPerKG  = record.getDouble("stackableUSDPerKG", 0d);
    nonstackableUSDPerKG  = record.getDouble("nonstackableUSDPerKG", 0d);
    return this;
  }

}