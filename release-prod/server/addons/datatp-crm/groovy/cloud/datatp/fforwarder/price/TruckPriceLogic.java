package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.price.calculator.AdditionalChargeCalculator;
import cloud.datatp.fforwarder.price.common.BaseTruckTransportCharge;
import cloud.datatp.fforwarder.price.entity.TruckContainerTransportCharge;
import cloud.datatp.fforwarder.price.entity.TruckRegularTransportCharge;
import cloud.datatp.fforwarder.price.repository.TruckContainerTransportChargeRepository;
import cloud.datatp.fforwarder.price.repository.TruckRegularTransportChargeRepository;
import lombok.Getter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.hr.entity.Employee;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;
import net.datatp.util.text.TokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Getter
public class TruckPriceLogic extends TransportPriceLogic {

  @Autowired
  private TruckRegularTransportChargeRepository truckRepo;

  @Autowired
  private TruckContainerTransportChargeRepository containerRepo;

  public <T extends BaseTruckTransportCharge> List<SqlMapRecord> search(
      ClientInfo client, Company company, SqlQueryParams sqlParams, Class<T> entity) {
    final boolean isRegular = entity.getName().equals(TruckRegularTransportCharge.class.getName());
    final boolean isCBT = sqlParams.hasParam("isCBT") && Boolean.parseBoolean(sqlParams.getString("isCBT"));
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("companyIds", company.findCompanyIdPaths());

    if (isRegular) {
      if (isCBT) {
        return searchDbRecords(client, company, "CBTTruckPriceSql.groovy", "SearchCBTTruckPrice", sqlParams);
      } else {
        return searchDbRecords(client, company, "TruckPriceSql.groovy", "SearchTruckPrice", sqlParams);
      }
    } else {
      if (isCBT) {
        return searchDbRecords(client, company, "CBTContainerPriceSql.groovy", "SearchCBTContainerPrice", sqlParams);
      } else {
        return searchDbRecords(client, company, "ContainerPriceSql.groovy", "SearchContainerPrice", sqlParams);
      }
    }
  }

  private List<SqlMapRecord> searchDbRecords(ClientInfo client, Company company, String scriptName, String executableName, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/" + scriptName;
    return searchDbRecords(client, scriptDir, scriptFile, executableName, sqlParams);
  }

  // ============================================
  // TRUCK REGULAR
  // ============================================

  public TruckRegularTransportCharge getTruckRegularById(ClientInfo client, Company company, Long id) {
    return truckRepo.findById(id).get();
  }

  public List<TruckRegularTransportCharge> findTruckRegularsByCompany(ClientInfo client, Company company) {
    return truckRepo.findByCompanyId(company.getId());
  }

  public List<TruckRegularTransportCharge> findTruckByIds(ClientInfo client, Company company, List<Long> ids) {
    return truckRepo.findByIds(ids);
  }

  public TruckRegularTransportCharge saveTruckRegularCharge(ClientInfo client, Company company, TruckRegularTransportCharge charge) {
    if (charge.isNew()) {
      final String code = generateTransportPriceCode(charge);
      charge.setCode(code);
      final Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
      Objects.assertNotNull(employee, "Employee not found!!!, login id: " + client.getRemoteUser());
      charge.setAssigneeAccountId(employee.getAccountId());
      charge.setAssigneeLabel(employee.getLabel());
    } else {
      Objects.assertTrue(charge.isSameCompany(company.getId()), "Not allowed to save data with the different company");
    }
    if (charge.getLabel().isEmpty()) charge.setLabel(charge.getCode());
    charge.setRoute(charge.getPickupProvince() + "-" + charge.getDeliveryProvince());
    if (StringUtil.isEmpty(charge.getCarrierLabel())) charge.setCarrierRoute(charge.getRoute() + "-" + "N/A");
    else charge.setCarrierRoute(charge.getRoute() + "-" + charge.getCarrierLabel());
    AdditionalChargeCalculator.calculate(charge.getAdditionalCharges());
    charge.set(client, company);
    return truckRepo.save(charge);
  }

  public List<TruckRegularTransportCharge> saveTruckCharges(ClientInfo client, Company company, List<MapObject> modified) {
    List<TruckRegularTransportCharge> savedRecords = new ArrayList<>();
    if (Collections.isNotEmpty(modified)) {
      for (MapObject sel : modified) {
        final Long id = sel.getLong("id", null);
        TruckRegularTransportCharge price = new TruckRegularTransportCharge();
        if (id != null) {
          price = getTruckRegularById(client, company, id);
          Objects.assertNotNull(price, "Truck Charge not found: id = " + id);
        }
        price = price.computeFromMapObject(sel);
        TruckRegularTransportCharge updated = saveTruckRegularCharge(client, company, price);
        savedRecords.add(updated);
      }
    }
    return savedRecords;
  }

  public List<MapObject> saveTruckRegulars(ClientInfo client, Company company, List<MapObject> modified) {
    if (Collections.isNotEmpty(modified)) {
      for (MapObject sel : modified) {
        final Long id = sel.getLong("id", null);
        TruckRegularTransportCharge price = new TruckRegularTransportCharge();
        if (id != null) {
          price = getTruckRegularById(client, company, id);
          Objects.assertNotNull(price, "Truck Regular not found: id = " + id);
        }
        price = price.computeFromMapObject(sel);
        TruckRegularTransportCharge updated = saveTruckRegularCharge(client, company, price);
        sel.put("id", updated.getId());
      }
    }
    return modified;
  }

  public List<SqlMapRecord> searchTruckRegularTransportCharges(ClientInfo client, Company company, SqlQueryParams params) {
    return search(client, company, params, TruckRegularTransportCharge.class);
  }


  public boolean changeTruckStorageState(ClientInfo client, ChangeStorageStateRequest request) {
    List<Long> chargeIds = request.getEntityIds();
    if (chargeIds.isEmpty()) throw RuntimeError.IllegalArgument("No Records were selected!!");
    truckRepo.setStorageState(chargeIds, request.getNewStorageState());
    return true;
  }

  public List<TruckRegularTransportCharge> findTruckRegularTransportChargeExpired(ClientInfo client, Company company, Date currentDate) {
    return truckRepo.findTruckRegularTransportChargeExpired(company.getId(), currentDate);
  }

  public boolean deleteTruckRegularByIds(ClientInfo client, Company company, List<Long> ids) {
    if (Collections.isEmpty(ids)) return false;
    List<TruckRegularTransportCharge> charges = findTruckByIds(client, company, ids);
    final Long companyId = company.getId();
    for (TruckRegularTransportCharge charge : charges) {
      Objects.assertTrue(charge.isSameCompany(companyId), "Records from other companies can not be deleted");
    }
    DBConnectionUtil connectionUtil = new DBConnectionUtil(getPrimaryDataSource());
    DeleteGraphBuilder graph = new DeleteGraphBuilder(connectionUtil, company.getId(), TruckRegularTransportCharge.class, ids);
    int count = graph.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count > 0;
  }

  // ============================================
  // TRUCK CONTAINER
  // ============================================

  public TruckContainerTransportCharge getTruckContainerById(ClientInfo client, Company company, Long id) {
    return containerRepo.findById(id).get();
  }

  public List<TruckContainerTransportCharge> findContainerByIds(ClientInfo client, Company company, List<Long> ids) {
    return containerRepo.findByIds(ids);
  }

  public List<TruckContainerTransportCharge> findTruckContainerTransportChargeExpired(ClientInfo client, Company company, Date currentDate) {
    return containerRepo.findTruckContainerTransportChargeExpired(company.getId(), currentDate);
  }

  public TruckContainerTransportCharge saveTruckContainerCharge(ClientInfo client, Company company, TruckContainerTransportCharge charge) {
    if (charge.isNew()) {
      final String code = generateTransportPriceCode(charge);
      charge.setCode(code);
      final Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
      Objects.assertNotNull(employee, "Employee not found!!!, login id: " + client.getRemoteUser());
      charge.setAssigneeAccountId(employee.getAccountId());
      charge.setAssigneeLabel(employee.getLabel());
    } else {
      Objects.assertTrue(charge.isSameCompany(company.getId()), "Not allowed to save data with the different company");
    }
    final String pickupLocationCode = charge.getPickupLocationCode();
    final String label = TokenUtil.labelWithToken(pickupLocationCode, charge.getDeliveryLocationCode());
    charge.setLabel(label.toUpperCase());
    charge.setRoute(charge.getPickupProvince() + "-" + charge.getDeliveryProvince());
    if (StringUtil.isEmpty(charge.getCarrierLabel())) charge.setCarrierRoute(charge.getRoute() + "-" + "N/A");
    else charge.setCarrierRoute(charge.getRoute() + "-" + charge.getCarrierLabel());
    AdditionalChargeCalculator.calculate(charge.getAdditionalCharges());
    charge.set(client, company);
    return containerRepo.save(charge);
  }

  public List<MapObject> saveTruckContainerCharges(ClientInfo client, Company company, List<MapObject> modified) {
    if (Collections.isNotEmpty(modified)) {
      for (MapObject sel : modified) {
        final Long id = sel.getLong("id", null);
        TruckContainerTransportCharge price = new TruckContainerTransportCharge();
        if (id != null) {
          price = getTruckContainerById(client, company, id);
          Objects.assertNotNull(price, "Truck Container not found: id = " + id);
        }
        price = price.computeFromMapObject(sel);
        TruckContainerTransportCharge updated = saveTruckContainerCharge(client, company, price);
        sel.put("id", updated.getId());
      }
    }
    return modified;
  }

  public List<SqlMapRecord> searchTruckContainerTransportCharges(ClientInfo client, Company company, SqlQueryParams params) {
    return search(client, company, params, TruckContainerTransportCharge.class);
  }

  public boolean changeContainerStorageState(ClientInfo client, ChangeStorageStateRequest request) {
    List<Long> chargeIds = request.getEntityIds();
    if (chargeIds.isEmpty()) throw RuntimeError.IllegalArgument("No Records were selected!!");
    containerRepo.setStorageState(chargeIds, request.getNewStorageState());
    return true;
  }

  public List<TruckContainerTransportCharge> findTruckContainerByCompany(ClientInfo client, Company company) {
    return containerRepo.findByCompanyId(company.getId());
  }

  public boolean deleteTruckContainerByIds(ClientInfo client, Company company, List<Long> ids) {
    if (Collections.isEmpty(ids)) return false;
    List<TruckContainerTransportCharge> charges = findContainerByIds(client, company, ids);
    final Long companyId = company.getId();
    for (TruckContainerTransportCharge charge : charges) {
      Objects.assertTrue(charge.isSameCompany(companyId), "Records from other companies can not be deleted");
    }
    DBConnectionUtil connectionUtil = new DBConnectionUtil(getJdbcDataSource());
    DeleteGraphBuilder graph = new DeleteGraphBuilder(connectionUtil, company.getId(), TruckContainerTransportCharge.class, ids);
    int count = graph.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count > 0;
  }

  public List<TruckContainerTransportCharge> updateContainerCharges(ClientInfo client, Company company, List<MapObject> records) {
    List<TruckContainerTransportCharge> savedRecords = new ArrayList<>();
    if (Collections.isNotEmpty(records)) {
      for (MapObject sel : records) {
        final Long id = sel.getLong("id", null);
        TruckContainerTransportCharge price = new TruckContainerTransportCharge();
        if (id != null) {
          price = getTruckContainerById(client, company, id);
          Objects.assertNotNull(price, "Truck Container not found: id = " + id);
        }
        price = price.computeFromMapObject(sel);
        final TruckContainerTransportCharge updated = saveTruckContainerCharge(client, company, price);
        savedRecords.add(updated);
      }
    }
    return savedRecords;
  }

}