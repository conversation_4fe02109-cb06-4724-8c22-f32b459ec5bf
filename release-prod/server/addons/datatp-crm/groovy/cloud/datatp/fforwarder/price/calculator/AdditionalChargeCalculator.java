package cloud.datatp.fforwarder.price.calculator;

import java.util.List;

import cloud.datatp.fforwarder.price.entity.TransportAdditionalCharge;

import net.datatp.util.ds.Collections;

public class AdditionalChargeCalculator {
  
  public static void calculate(List<TransportAdditionalCharge> charges) {
    if(Collections.isEmpty(charges)) return;
    
    for(TransportAdditionalCharge charge : charges ) {
      double total = charge.getQuantity() * charge.getUnitPrice();
      double totalTax = total * charge.getTaxRate();
      
      charge.setTotal(total);
      charge.setTotalTax(totalTax);
      charge.setFinalCharge(total + totalTax);
    }
  }
}