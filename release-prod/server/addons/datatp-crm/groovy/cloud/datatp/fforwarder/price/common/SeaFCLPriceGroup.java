package cloud.datatp.fforwarder.price.common;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.ds.MapObject;

@Embeddable
@Getter @Setter
@NoArgsConstructor
public class SeaFCLPriceGroup {

  /*
    NONE US ROUTE: 20/40DC - 40HC
    US ROUTE: 20/40DC - 40/45HC - 40NOR
    REEFER COMMODITY: 20/40RF
   */

  @Column(name = "dry_20_price")
  private double dry20Price;

  @Column(name = "commission_dry_20_price")
  private double commissionDry20Price;

  @Column(name = "dry_40_price")
  private double dry40Price;

  @Column(name = "commission_dry_40_price")
  private double commissionDry40Price;

  @Column(name = "high_cube_40_price")
  private double highCube40Price;

  @Column(name = "commission_high_cube_40_price")
  private double commissionHighCube40Price;

  @Column(name = "high_cube_45_price")
  private double highCube45Price;

  @Column(name = "commission_high_cube_45_price")
  private double commissionHighCube45Price;

  @Column(name = "nor_40_price")
  private double nor40Price;

  @Column(name = "commission_nor_40_price")
  private double commissionNor40Price;

  @Column(name = "reefer_20_price")
  private double reefer20Price;

  @Column(name = "commission_reefer_20_price")
  private double commissionReefer20Price;

  @Column(name = "reefer_40_price")
  private double reefer40Price;

  @Column(name = "commission_reefer_40_price")
  private double commissionReefer40Price;

  @Column(name = "dry_20_price_note")
  private String dry20PriceNote;

  @Column(name = "dry_40_price_note")
  private String dry40PriceNote;

  @Column(name = "high_cube_40_price_note")
  private String highCube40PriceNote;

  @Column(name = "high_cube_45_price_note")
  private String highCube45PriceNote;

  @Column(name = "nor_40_price_note")
  private String nor40PriceNote;

  @Column(name = "reefer_20_price_note")
  private String reefer20PriceNote;

  @Column(name = "reefer_40_price_note")
  private String reefer40PriceNote;

  public SeaFCLPriceGroup computeFrom(MapObject record) {

    dry20PriceNote = record.getString("dry20PriceNote", "");
    dry40PriceNote = record.getString("dry40PriceNote", "");
    highCube40PriceNote = record.getString("highCube40PriceNote", "");
    highCube45PriceNote = record.getString("highCube45PriceNote", "");
    nor40PriceNote = record.getString("nor40PriceNote", "");
    reefer20PriceNote = record.getString("reefer20PriceNote", "");
    reefer40PriceNote = record.getString("reefer40PriceNote", "");

    if(record.containsKey("commission20DC")) {
      commissionDry20Price = record.getDouble("commission20DC", 0D);
      commissionDry40Price = record.getDouble("commission40DC", 0D);
      commissionHighCube40Price = record.getDouble("commission40HC", 0D);
      commissionHighCube45Price = record.getDouble("commission45HC", 0D);
      commissionNor40Price = record.getDouble("commission40NOR", 0D);
      commissionReefer20Price = record.getDouble("commission20RF", 0D);
      commissionReefer40Price = record.getDouble("commission40RF", 0D);
    } else {
      commissionDry20Price = record.getDouble("commissionDry20Price", 0D);
      commissionDry40Price = record.getDouble("commissionDry40Price", 0D);
      commissionHighCube40Price = record.getDouble("commissionHighCube40Price", 0D);
      commissionHighCube45Price = record.getDouble("commissionHighCube45Price", 0D);
      commissionNor40Price = record.getDouble("commissionNor40Price", 0D);
      commissionReefer20Price = record.getDouble("commissionReefer20Price", 0D);
      commissionReefer40Price = record.getDouble("commissionReefer40Price", 0D);
    }

    try {
      dry20Price = record.getDouble("dry20Price", 0D);
    } catch (RuntimeException ex) {
      dry20Price = 0;
      dry20PriceNote = record.getString("dry20Price", "");
    }

    try {
      dry40Price = record.getDouble("dry40Price", 0D);
    } catch (RuntimeException ex) {
      dry40Price = 0;
      dry40PriceNote = record.getString("dry40Price", "");
    }

    try {
      highCube40Price = record.getDouble("highCube40Price", 0D);
    } catch (RuntimeException ex) {
      highCube40Price = 0;
      highCube40PriceNote = record.getString("highCube40Price", "");
    }

    try {
      highCube45Price = record.getDouble("highCube45Price", 0D);
    } catch (RuntimeException ex) {
      highCube45Price = 0;
      highCube45PriceNote = record.getString("highCube45Price", "");
    }

    try {
      nor40Price = record.getDouble("nor40Price", 0D);
    } catch (RuntimeException ex) {
      nor40Price = 0;
      nor40PriceNote = record.getString("nor40Price", "");
    }

    try {
      reefer20Price = record.getDouble("reefer20Price", 0D);
    } catch (RuntimeException ex) {
      reefer20Price = 0;
      reefer20PriceNote = record.getString("reefer20Price", "");
    }

    try {
      reefer40Price = record.getDouble("reefer40Price", 0D);
    } catch (RuntimeException ex) {
      reefer40Price = 0;
      reefer40PriceNote = record.getString("reefer40Price", "");
    }
    return this;
  }
}