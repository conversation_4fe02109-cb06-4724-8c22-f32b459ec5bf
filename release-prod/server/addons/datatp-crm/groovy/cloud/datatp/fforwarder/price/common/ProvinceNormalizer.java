package cloud.datatp.fforwarder.price.common;

import java.text.Normalizer;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import net.datatp.module.resource.location.entity.District;
import net.datatp.module.resource.location.entity.Location;
import net.datatp.module.resource.location.entity.State;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
public class ProvinceNormalizer {

  private final Map<String, Location> provinceCache = new HashMap<>(); // location type state's type
  private final Map<String, Location> districtCache = new HashMap<>(); // location has district's type

  private final Map<String, State> stateCache = new HashMap<>(); // state
  private final Map<String, District> districtStateCache = new HashMap<>(); // district

  private final Map<String, Location> locationKCNCache = new HashMap<>(); // location

  private boolean isCached = false;

  public String normalizeProvinceToken(String input, boolean removeTone) {
    if (input == null) input = "";
    String normalized = input.replaceFirst("(?i)^(tỉnh|thành phố)\\s*", "");
    if (removeTone) normalized = removeVietnameseTone(normalized);
    return normalized;
  }

  public String normalizeDistrictToken(String input) {
    if (input == null) input = "";
    String normalized = input.replaceFirst("(?i)^(quận|thị xã|huyện|thành phố)\\s*", "");
    normalized = removeVietnameseTone(normalized);
    return normalized;
  }

  public static String removeVietnameseTone(String normalized) {
    normalized = normalized.replaceAll("[àáạảãâầấậẩẫăằắặẳẵ]", "a");
    normalized = normalized.replaceAll("[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ]", "a");
    normalized = normalized.replaceAll("[èéẹẻẽêềếệểễ]", "e");
    normalized = normalized.replaceAll("[ÈÉẸẺẼÊỀẾỆỂỄ]", "e");
    normalized = normalized.replaceAll("[ìíịỉĩ]", "i");
    normalized = normalized.replaceAll("[ÌÍỊỈĨ]", "i");
    normalized = normalized.replaceAll("[òóọỏõôồốộổỗơờớợởỡ]", "o");
    normalized = normalized.replaceAll("[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ]", "o");
    normalized = normalized.replaceAll("[ùúụủũưừứựửữ]", "u");
    normalized = normalized.replaceAll("[ÙÚỤỦŨƯỪỨỰỬỮ]", "u");
    normalized = normalized.replaceAll("[ỳýỵỷỹ]", "y");
    normalized = normalized.replaceAll("[ỲÝỴỶỸ]", "y");
    normalized = normalized.replaceAll("[đ]", "d");
    normalized = normalized.replaceAll("[Đ]", "d");
    normalized = Normalizer.normalize(normalized, Normalizer.Form.NFD);
    normalized = normalized.replaceAll("[!@%^*()+=<>?/,.:;'\"&#\\[\\]~$_`\\-{}|\\\\]", " ");
    normalized = normalized.toLowerCase();
    normalized = normalized.trim().replaceAll("\\s+", " ");
    return normalized;
  }

  // ---------------- Location Province -------------------
  public void cacheProvinces(List<Location> provinces) {
    if (!isCached) {
      for (Location province : provinces) {
        String normalizedProvince = normalizeProvinceToken(province.getShortLabel(), true);
        provinceCache.put(normalizedProvince, province);
      }
      isCached = true;
    }
  }

  public Location findLocationProvince(String userInput) {
    String normalizedInput = normalizeProvinceToken(userInput, true);
    return provinceCache.getOrDefault(normalizedInput, null);
  }

  // ---------------- Province (State entity) -------------------
  public void cacheProvince(List<State> states) {
    if (!isCached) {
      for (State state : states) {
        String normalizedProvince = normalizeProvinceToken(state.getLabel(), true);
        stateCache.put(normalizedProvince, state);
      }
      isCached = true;
    }
  }

  public State findStateProvince(String userInput) {
    String normalizedInput = normalizeProvinceToken(userInput, true);
    return stateCache.getOrDefault(normalizedInput, null);
  }

  // ---------------- Province (District entity) -------------------
  public void cacheDistrict(List<District> districts) {
    if (!isCached) {
      for (District district : districts) {
        String normalizedDistrict = normalizeDistrictToken(district.getLabel());
        districtStateCache.put(normalizedDistrict, district);
      }
      isCached = true;
    }
  }

  public District findDistrictProvince(String userInput) {
    String normalizedInput = normalizeDistrictToken(userInput);
    return districtStateCache.getOrDefault(normalizedInput, null);
  }

  // ---------------- Location -------------------
  public void cacheLocationKCN(List<Location> locations) {
    if (!isCached) {
      for (Location location : locations) {
        String normalizedLocation = removeVietnameseTone(location.getShortLabel().replace(" ", "").toLowerCase());
        locationKCNCache.put(normalizedLocation, location);
      }
      isCached = true;
    }
  }

  public Location findLocationKCN(String userInput) {
    String normalizedInput = removeVietnameseTone(userInput);
    return locationKCNCache.getOrDefault(normalizedInput, null);
  }

}