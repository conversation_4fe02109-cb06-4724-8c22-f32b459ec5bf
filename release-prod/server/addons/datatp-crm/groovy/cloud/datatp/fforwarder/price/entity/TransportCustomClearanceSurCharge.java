package cloud.datatp.fforwarder.price.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import cloud.datatp.fforwarder.price.entity.CustomClearanceSurCharge.CustomClearanceSurChargeMethod;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyPersistable;

@Entity
@Table(
  name = TransportCustomClearanceSurCharge.TABLE_NAME
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class TransportCustomClearanceSurCharge extends CompanyPersistable {
  public static final String TABLE_NAME = "lgc_price_transport_cc_sur_charge";

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  private String label;

  @Enumerated(EnumType.STRING)
  private CustomClearanceSurChargeMethod method = CustomClearanceSurChargeMethod.QUANTITY;

  private double quantity;

  @Column(name = "unit_price")
  private double unitPrice;

  private String unit;

  @Column(name = "tax_rate")
  private double taxRate;

  @Column(name = "total_tax")
  private double totalTax;

  private double total;

  @Column(name = "final_charge")
  private double finalCharge;

  private String currency;

  @Column(name = "ref_quantity")
  private double refQuantity;

  @Column(name = "ref_unit_price")
  private double refUnitPrice;

  @Column(name = "ref_unit")
  private String refUnit;

  @Column(name = "ref_tax_rate")
  private double refTaxRate;

  @Column(name = "ref_total_tax")
  private double refTotalTax;

  @Column(name = "ref_currency")
  private String refCurrency;

  @Column(name = "ref_total")
  private double refTotal;

  @Column(name = "ref_final_charge")
  private double refFinalCharge;

  @Column(length = 1024 * 32)
  private String note;

  public TransportCustomClearanceSurCharge(CustomClearanceSurCharge surCharge) {
    this.label  = surCharge.getLabel();
    this.name   = surCharge.getName();
    this.method = surCharge.getMethod();

    this.quantity    = surCharge.getQuantity();
    this.unit        = surCharge.getUnit();
    this.currency    = surCharge.getCurrency();

    this.refQuantity  = surCharge.getQuantity();
    this.refTaxRate   = surCharge.getTaxRate();
    this.refUnit      = surCharge.getUnit();
    this.refCurrency  = surCharge.getCurrency();
  }
}