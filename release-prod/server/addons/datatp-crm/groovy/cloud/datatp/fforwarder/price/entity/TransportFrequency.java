package cloud.datatp.fforwarder.price.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyPersistable;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.StringUtil;

@Entity
@Table(name = TransportFrequency.TABLE_NAME)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class TransportFrequency extends CompanyPersistable {
  public static final String TABLE_NAME = "lgc_price_transport_frequency";

  public enum Day {
    Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday, Other;
    static public Day parse(String token) {
      if (!StringUtil.isEmpty(token)) {
        if(token.equalsIgnoreCase("mon")) return Monday;
        if(token.equalsIgnoreCase("tue")) return Tuesday;
        if(token.equalsIgnoreCase("wed")) return Wednesday;
        if(token.equalsIgnoreCase("thu")) return Thursday;
        if(token.equalsIgnoreCase("fri")) return Friday;
        if(token.equalsIgnoreCase("sat")) return Saturday;
        if(token.equalsIgnoreCase("sun")) return Sunday;
        else return Other;
      } else {
        return Other;
      }
    }
  }

  @Enumerated(EnumType.STRING)
  private Day    day;

  private String label;

  @Column(name = "depart_time")
  private String departTime;

  @Column(name = "cut_off_time")
  private String cutOffTime;

  private String transit = "DIRECT";

  @Column(name = "transit_label")
  private String transitLabel = "DIRECT";

  @Column(name = "transit_time")
  private String  transitTime;

  @Column(name="sea_fcl_charge_id", updatable=false, insertable=false)
  private Long seaFclChargeId;

  @Column(name="air_charge_id", updatable=false, insertable=false)
  private Long airChargeId;

  @Column(name="sea_lcl_charge_id", updatable=false, insertable=false)
  private Long seaLclChargeId;

  public TransportFrequency(Day day, String departTime, String transitTime) {
    this.day = day;
    this.label = day.name();
    this.departTime = departTime;
    this.transitTime = transitTime;
    this.transitLabel = "DIRECT";
  }

  public static TransportFrequency creator(MapObject record) {
    TransportFrequency fre = new TransportFrequency();
    fre.setDay(Day.Other);
    fre.setLabel(record.getString("frequency", ""));
    fre.setTransit(record.getString("transitPort", ""));
    fre.setTransitLabel(record.getString("transitPort", ""));
    fre.setTransitTime(record.getString("transitTime", ""));
    fre.setCutOffTime(record.getString("cutoff", ""));
    fre.setDepartTime(record.getString("etd", ""));
    return fre;
  }

  public TransportFrequency clone() {
    final TransportFrequency fre = new TransportFrequency();
    fre.setLabel(label);
    fre.setCutOffTime(cutOffTime);
    fre.setTransit(transit);
    fre.setTransitLabel(transitLabel);
    fre.setDepartTime(departTime);
    fre.setTransitTime(transitTime);
    return fre;
  }

  public TransportFrequency withTransit(String locCode, String locLabel) {
    transit = locCode;
    transitLabel = locLabel;
    return this;
  }

}