package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.price.entity.*;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.ExecutableUnit;
import net.datatp.lib.executable.Executor;
import net.datatp.module.backend.Notification;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.StorageState;
import org.springframework.context.ApplicationContext;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class TransportPriceLogicExecutor extends Executor {
  public static class ArchiveTransportPrice extends ExecutableUnit {
    @Override
    public Notification execute(ApplicationContext appContext, ExecutableContext ctx) {
      SeaPriceLogic seaPriceLogic = appContext.getBean(SeaPriceLogic.class);
      AirPriceLogic airPriceLogic = appContext.getBean(AirPriceLogic.class);
      TruckPriceLogic truckPriceLogic = appContext.getBean(TruckPriceLogic.class);
      Company company = (Company) ctx.getCompany();
      ClientInfo client = ctx.getClient();

      Date currentDate = new Date();
      StorageState storageState = StorageState.ARCHIVED;

      Notification notification = new Notification(Notification.Type.info, "Transport Price Archive", "");

      int fclCount = 0, lclCount = 0, airCount = 0, truckRegularCount = 0, truckContainerCount = 0;

      /*
      List<SeaFclTransportCharge> expiredFclCharges = seaPriceLogic.findSeaFclTransportChargeExpired(client, company, currentDate);
      if (expiredFclCharges != null && !expiredFclCharges.isEmpty()) {
        List<Long> ids = expiredFclCharges.stream().map(SeaFclTransportCharge::getId).collect(Collectors.toList());
        ChangeStorageStateRequest request = new ChangeStorageStateRequest();
        request.setEntityIds(ids);
        request.setNewStorageState(storageState);
        seaPriceLogic.changeSeaFclStorageState(client, request);
        fclCount = ids.size();
      }
       */

      List<SeaLclTransportCharge> expiredLclCharges = seaPriceLogic.findSeaLclTransportChargeExpired(client, company, currentDate);
      if (expiredLclCharges != null && !expiredLclCharges.isEmpty()) {
        List<Long> ids = expiredLclCharges.stream().map(SeaLclTransportCharge::getId).collect(Collectors.toList());
        ChangeStorageStateRequest request = new ChangeStorageStateRequest();
        request.setEntityIds(ids);
        request.setNewStorageState(storageState);
        seaPriceLogic.changeSeaLclStorageState(client, request);
        lclCount = ids.size();
      }

      List<AirTransportCharge> expiredAirCharges = airPriceLogic.findAirTransportChargeExpired(client, company, currentDate);
      if (expiredAirCharges != null && !expiredAirCharges.isEmpty()) {
        List<Long> ids = expiredAirCharges.stream().map(AirTransportCharge::getId).collect(Collectors.toList());
        ChangeStorageStateRequest request = new ChangeStorageStateRequest();
        request.setEntityIds(ids);
        request.setNewStorageState(storageState);
        airPriceLogic.updateStorageState(client, request);
        airCount = ids.size();
      }

      List<TruckRegularTransportCharge> expiredTruckCharges = truckPriceLogic.findTruckRegularTransportChargeExpired(client, company, currentDate);
      if (expiredTruckCharges != null && !expiredTruckCharges.isEmpty()) {
        List<Long> ids = expiredTruckCharges.stream().map(TruckRegularTransportCharge::getId).collect(Collectors.toList());
        ChangeStorageStateRequest request = new ChangeStorageStateRequest();
        request.setEntityIds(ids);
        request.setNewStorageState(storageState);
        truckPriceLogic.changeTruckStorageState(client, request);
        truckRegularCount = ids.size();
      }

      List<TruckContainerTransportCharge> expiredContainerCharges = truckPriceLogic.findTruckContainerTransportChargeExpired(client, company, currentDate);
      if (expiredContainerCharges != null && !expiredContainerCharges.isEmpty()) {
        List<Long> ids = expiredContainerCharges.stream().map(TruckContainerTransportCharge::getId).collect(Collectors.toList());
        ChangeStorageStateRequest request = new ChangeStorageStateRequest();
        request.setEntityIds(ids);
        request.setNewStorageState(storageState);
        truckPriceLogic.changeContainerStorageState(client, request);
        truckContainerCount = ids.size();
      }

      StringBuilder msg = new StringBuilder();
      msg.append("Transport Price Archival Summary\n")
          .append(String.format("Sea FCL: %d\n", fclCount))
          .append(String.format("Sea LCL: %d\n", lclCount))
          .append(String.format("Air: %d\n", airCount))
          .append(String.format("Truck Regular: %d\n", truckRegularCount))
          .append(String.format("Truck Container: %d\n", truckContainerCount));

      return notification
          .withMessage(msg.toString());
    }
  }

  public TransportPriceLogicExecutor() {
    register(new ArchiveTransportPrice());
  }
}