2025-07-10T11:43:10.451+07:00  INFO 9257 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 9257 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-10T11:43:10.452+07:00  INFO 9257 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-10T11:43:11.220+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.328+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 104 ms. Found 22 JPA repository interfaces.
2025-07-10T11:43:11.338+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.340+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T11:43:11.340+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.347+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 9 JPA repository interfaces.
2025-07-10T11:43:11.348+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.352+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-07-10T11:43:11.352+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.357+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T11:43:11.369+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.374+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-07-10T11:43:11.384+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.389+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-10T11:43:11.393+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.397+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T11:43:11.397+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.398+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T11:43:11.403+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.409+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-10T11:43:11.414+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.417+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T11:43:11.417+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.421+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T11:43:11.422+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.431+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 12 JPA repository interfaces.
2025-07-10T11:43:11.432+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.438+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 4 JPA repository interfaces.
2025-07-10T11:43:11.439+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.439+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T11:43:11.439+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.440+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-10T11:43:11.440+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.444+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-10T11:43:11.445+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.446+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T11:43:11.446+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.446+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T11:43:11.447+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.459+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-07-10T11:43:11.473+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.479+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-07-10T11:43:11.480+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.483+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T11:43:11.483+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.488+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-10T11:43:11.488+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.493+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T11:43:11.494+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.498+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T11:43:11.498+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.501+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-07-10T11:43:11.502+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.512+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-07-10T11:43:11.512+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.528+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 25 JPA repository interfaces.
2025-07-10T11:43:11.543+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.555+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-07-10T11:43:11.556+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.561+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T11:43:11.561+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.563+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-07-10T11:43:11.569+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.570+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T11:43:11.570+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.577+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-10T11:43:11.582+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.622+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 39 ms. Found 67 JPA repository interfaces.
2025-07-10T11:43:11.622+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.624+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T11:43:11.624+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T11:43:11.626+07:00  INFO 9257 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T11:43:11.818+07:00  INFO 9257 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-10T11:43:11.822+07:00  INFO 9257 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-10T11:43:12.095+07:00  WARN 9257 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-10T11:43:12.308+07:00  INFO 9257 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-10T11:43:12.310+07:00  INFO 9257 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-10T11:43:12.321+07:00  INFO 9257 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-10T11:43:12.321+07:00  INFO 9257 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1750 ms
2025-07-10T11:43:12.371+07:00  WARN 9257 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T11:43:12.371+07:00  INFO 9257 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-10T11:43:12.504+07:00  INFO 9257 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@73daf6a6
2025-07-10T11:43:12.505+07:00  INFO 9257 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-10T11:43:12.509+07:00  WARN 9257 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T11:43:12.509+07:00  INFO 9257 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-10T11:43:12.513+07:00  INFO 9257 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@18ea9bec
2025-07-10T11:43:12.513+07:00  INFO 9257 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-10T11:43:12.513+07:00  WARN 9257 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T11:43:12.513+07:00  INFO 9257 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-10T11:43:12.962+07:00  INFO 9257 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6dbfd081
2025-07-10T11:43:12.963+07:00  INFO 9257 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-10T11:43:12.963+07:00  WARN 9257 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T11:43:12.963+07:00  INFO 9257 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-10T11:43:12.974+07:00  INFO 9257 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@8872669
2025-07-10T11:43:12.975+07:00  INFO 9257 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-10T11:43:12.975+07:00  INFO 9257 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-10T11:43:13.028+07:00  INFO 9257 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-10T11:43:13.030+07:00  INFO 9257 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@57e351a{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10283956197699364826/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@4098ef14{STARTED}}
2025-07-10T11:43:13.031+07:00  INFO 9257 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@57e351a{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10283956197699364826/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@4098ef14{STARTED}}
2025-07-10T11:43:13.032+07:00  INFO 9257 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@2f26634a{STARTING}[12.0.15,sto=0] @3167ms
2025-07-10T11:43:13.094+07:00  INFO 9257 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T11:43:13.123+07:00  INFO 9257 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-10T11:43:13.137+07:00  INFO 9257 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T11:43:13.282+07:00  INFO 9257 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T11:43:13.318+07:00  WARN 9257 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T11:43:13.961+07:00  INFO 9257 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T11:43:13.969+07:00  INFO 9257 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@156eb310] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T11:43:14.026+07:00  INFO 9257 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T11:43:14.221+07:00  INFO 9257 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-10T11:43:14.223+07:00  INFO 9257 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-10T11:43:14.230+07:00  INFO 9257 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T11:43:14.232+07:00  INFO 9257 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T11:43:14.263+07:00  INFO 9257 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T11:43:14.272+07:00  WARN 9257 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T11:43:17.062+07:00  INFO 9257 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T11:43:17.063+07:00  INFO 9257 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@236fee7d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T11:43:17.202+07:00  INFO 9257 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T11:43:17.235+07:00  INFO 9257 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-10T11:43:17.239+07:00  INFO 9257 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-10T11:43:17.239+07:00  INFO 9257 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T11:43:17.246+07:00  WARN 9257 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T11:43:17.374+07:00  INFO 9257 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10T11:43:17.836+07:00  INFO 9257 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T11:43:17.838+07:00  INFO 9257 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T11:43:17.872+07:00  INFO 9257 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-10T11:43:17.915+07:00  INFO 9257 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-10T11:43:18.004+07:00  INFO 9257 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-10T11:43:18.031+07:00  INFO 9257 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T11:43:18.055+07:00  INFO 9257 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 10639191ms : this is harmless.
2025-07-10T11:43:18.063+07:00  INFO 9257 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-10T11:43:18.066+07:00  INFO 9257 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T11:43:18.080+07:00  INFO 9257 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 10639178ms : this is harmless.
2025-07-10T11:43:18.082+07:00  INFO 9257 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-10T11:43:18.095+07:00  INFO 9257 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-10T11:43:18.096+07:00  INFO 9257 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-10T11:43:20.673+07:00  INFO 9257 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@11:30:00+0700 to 10/07/2025@11:45:00+0700
2025-07-10T11:43:20.673+07:00  INFO 9257 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@11:30:00+0700 to 10/07/2025@11:45:00+0700
2025-07-10T11:43:21.259+07:00  INFO 9257 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-10T11:43:21.259+07:00  INFO 9257 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T11:43:21.260+07:00  WARN 9257 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T11:43:21.512+07:00  INFO 9257 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-10T11:43:21.512+07:00  INFO 9257 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-10T11:43:21.512+07:00  INFO 9257 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-10T11:43:21.512+07:00  INFO 9257 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-10T11:43:21.512+07:00  INFO 9257 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-10T11:43:23.117+07:00  WARN 9257 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 06f772ca-5c81-452a-a923-24b76b914cff

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10T11:43:23.121+07:00  INFO 9257 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-10T11:43:23.419+07:00  INFO 9257 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-10T11:43:23.422+07:00  INFO 9257 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T11:43:23.422+07:00  INFO 9257 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T11:43:23.422+07:00  INFO 9257 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T11:43:23.469+07:00  INFO 9257 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10T11:43:23.469+07:00  INFO 9257 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-10T11:43:23.470+07:00  INFO 9257 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-10T11:43:23.478+07:00  INFO 9257 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@206de653{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T11:43:23.479+07:00  INFO 9257 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-10T11:43:23.480+07:00  INFO 9257 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-10T11:43:23.510+07:00  INFO 9257 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-10T11:43:23.510+07:00  INFO 9257 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-10T11:43:23.516+07:00  INFO 9257 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.406 seconds (process running for 13.65)
2025-07-10T11:43:25.360+07:00  INFO 9257 --- [qtp47827984-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T11:43:28.283+07:00  INFO 9257 --- [qtp47827984-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0ostrhflavg6u7y0zuv9e5k890
2025-07-10T11:43:28.283+07:00  INFO 9257 --- [qtp47827984-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0178enn267pjx3fij0llctpyvz1
2025-07-10T11:43:28.408+07:00  INFO 9257 --- [qtp47827984-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0ostrhflavg6u7y0zuv9e5k890, token = 1693a710aef019a0a3d5c25d1b48d1d9
2025-07-10T11:43:28.409+07:00  INFO 9257 --- [qtp47827984-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0178enn267pjx3fij0llctpyvz1, token = 1693a710aef019a0a3d5c25d1b48d1d9
2025-07-10T11:43:28.813+07:00  INFO 9257 --- [qtp47827984-35] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T11:43:28.813+07:00  INFO 9257 --- [qtp47827984-40] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T11:43:29.881+07:00 DEBUG 9257 --- [qtp47827984-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:43:29.914+07:00 DEBUG 9257 --- [qtp47827984-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:44:05.522+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:44:26.603+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T11:44:26.624+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:44:26.624+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:44:26.624+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:44:26.624+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:44:29.876+07:00 DEBUG 9257 --- [qtp47827984-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:44:59.679+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:44:59.680+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:44:59.680+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:45:06.693+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:45:06.696+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T11:45:06.696+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:45:06.697+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@11:45:06+0700
2025-07-10T11:45:06.704+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@11:45:00+0700 to 10/07/2025@12:00:00+0700
2025-07-10T11:45:06.705+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@11:45:00+0700 to 10/07/2025@12:00:00+0700
2025-07-10T11:45:07.731+07:00 DEBUG 9257 --- [botTaskExecutor-1] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T11:45:29.760+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:45:29.762+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:45:29.762+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:45:29.874+07:00 DEBUG 9257 --- [qtp47827984-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:45:57.802+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:45:57.804+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:45:57.804+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:46:04.816+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:46:25.903+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-07-10T11:46:25.912+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:46:25.912+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:46:25.912+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:46:25.913+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:46:29.894+07:00 DEBUG 9257 --- [qtp47827984-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:46:59.967+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:46:59.969+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:46:59.969+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:47:06.985+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:47:29.019+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:47:29.020+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:47:29.021+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:47:29.886+07:00 DEBUG 9257 --- [qtp47827984-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:47:57.075+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:47:57.078+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:47:57.079+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:48:04.098+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:48:29.885+07:00 DEBUG 9257 --- [qtp47827984-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:48:30.154+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T11:48:30.163+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:48:30.164+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:48:30.164+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:48:30.165+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:49:00.290+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:49:00.292+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:49:00.292+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:49:06.303+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:49:28.343+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:49:28.346+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:49:28.347+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:49:29.880+07:00 DEBUG 9257 --- [qtp47827984-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:49:56.392+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:49:56.394+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:49:56.395+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:50:03.409+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:50:03.410+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:50:29.867+07:00 DEBUG 9257 --- [qtp47827984-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:50:30.468+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-10T11:50:30.476+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:50:30.476+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:50:30.476+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:50:30.477+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:50:59.537+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:50:59.539+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:50:59.539+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:51:06.550+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:51:27.588+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:51:27.590+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user louis.vnhph
2025-07-10T11:51:27.591+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:51:29.871+07:00 DEBUG 9257 --- [qtp47827984-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:51:33.464+07:00  INFO 9257 --- [qtp47827984-39] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph logout successfully 
2025-07-10T11:51:38.258+07:00  INFO 9257 --- [qtp47827984-58] n.d.module.session.ClientSessionManager  : Add a client session id = node0178enn267pjx3fij0llctpyvz1, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T11:51:38.268+07:00  INFO 9257 --- [qtp47827984-58] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T11:51:39.642+07:00 DEBUG 9257 --- [qtp47827984-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:51:39.642+07:00 DEBUG 9257 --- [qtp47827984-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:51:55.644+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:51:55.646+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:51:55.647+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:52:02.661+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:52:29.750+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-07-10T11:52:29.758+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:52:29.758+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:52:29.759+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:52:29.767+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T11:52:39.666+07:00 DEBUG 9257 --- [qtp47827984-74] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:52:58.827+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:52:58.828+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:52:58.828+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:53:05.843+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:53:26.876+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:53:26.877+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:53:26.877+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:53:39.673+07:00 DEBUG 9257 --- [qtp47827984-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:53:59.928+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:53:59.929+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:53:59.930+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:54:06.938+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:54:13.048+07:00  INFO 9257 --- [Scheduler-96119738-1] n.d.m.session.AppHttpSessionListener     : The session node0ostrhflavg6u7y0zuv9e5k890 is destroyed.
2025-07-10T11:54:30.025+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 6
2025-07-10T11:54:30.035+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:54:30.035+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:54:30.035+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:54:30.039+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T11:54:39.671+07:00 DEBUG 9257 --- [qtp47827984-77] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:54:58.076+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:54:58.077+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:54:58.077+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:55:05.086+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:55:05.089+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:55:26.125+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:55:26.126+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:55:26.126+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:55:39.687+07:00 DEBUG 9257 --- [qtp47827984-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:56:00.183+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:56:00.184+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:56:00.184+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:56:06.194+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:56:29.262+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 1
2025-07-10T11:56:29.281+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:56:29.282+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:56:29.282+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:56:29.283+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-07-10T11:56:39.686+07:00 DEBUG 9257 --- [qtp47827984-69] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:56:57.323+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:56:57.324+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:56:57.325+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:57:04.335+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:57:30.378+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:57:30.381+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:57:30.381+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:57:39.734+07:00 DEBUG 9257 --- [qtp47827984-73] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:58:00.435+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:58:00.438+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T11:58:00.439+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:58:06.447+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:13:28.548+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 24
2025-07-10T12:13:28.560+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:13:28.560+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0178enn267pjx3fij0llctpyvz1, remote user nhat.le
2025-07-10T12:13:28.560+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:13:28.561+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-10T12:13:40.496+07:00  INFO 9257 --- [qtp47827984-61] n.d.m.session.AppHttpSessionListener     : The session node0178enn267pjx3fij0llctpyvz1 is destroyed.
2025-07-10T12:13:40.507+07:00  INFO 9257 --- [qtp47827984-61] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T12:13:42.742+07:00  WARN 9257 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=15m29s981ms).
2025-07-10T12:13:42.742+07:00  WARN 9257 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=15m29s980ms).
2025-07-10T12:13:43.166+07:00  WARN 9257 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m29s980ms).
2025-07-10T12:13:43.192+07:00  WARN 9257 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=15m29s980ms).
2025-07-10T12:13:56.603+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:13:56.604+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:14:03.625+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:14:29.683+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:14:29.685+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:14:43.474+07:00  INFO 9257 --- [qtp47827984-61] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node09gd9i3e24ezz4hx7t8gbbwt2
2025-07-10T12:14:43.475+07:00  INFO 9257 --- [qtp47827984-71] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01ve37mdfdzaqsr5v7fws1fp5j3
2025-07-10T12:14:43.531+07:00  INFO 9257 --- [qtp47827984-61] n.d.module.session.ClientSessionManager  : Add a client session id = node09gd9i3e24ezz4hx7t8gbbwt2, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T12:14:43.531+07:00  INFO 9257 --- [qtp47827984-71] n.d.module.session.ClientSessionManager  : Add a client session id = node01ve37mdfdzaqsr5v7fws1fp5j3, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T12:14:43.581+07:00  INFO 9257 --- [qtp47827984-71] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T12:14:43.581+07:00  INFO 9257 --- [qtp47827984-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T12:14:46.812+07:00 DEBUG 9257 --- [qtp47827984-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:14:46.812+07:00 DEBUG 9257 --- [qtp47827984-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:14:59.783+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:14:59.784+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ve37mdfdzaqsr5v7fws1fp5j3, remote user nhat.le
2025-07-10T12:14:59.785+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:15:06.808+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T12:15:06.809+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T12:15:06.809+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@12:15:06+0700
2025-07-10T12:15:06.816+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@12:15:00+0700 to 10/07/2025@12:30:00+0700
2025-07-10T12:15:06.816+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@12:15:00+0700 to 10/07/2025@12:30:00+0700
2025-07-10T12:15:06.817+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 12 PM every day
2025-07-10T12:15:06.817+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-07-10T12:15:06.817+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-07-10T12:15:06.817+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:15:12.834+07:00 DEBUG 9257 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T12:15:27.893+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-07-10T12:15:27.898+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:15:27.898+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ve37mdfdzaqsr5v7fws1fp5j3, remote user nhat.le
2025-07-10T12:15:27.898+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:15:27.899+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T12:15:47.501+07:00 DEBUG 9257 --- [qtp47827984-100] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:15:55.959+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:15:55.960+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ve37mdfdzaqsr5v7fws1fp5j3, remote user nhat.le
2025-07-10T12:15:55.960+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:16:02.981+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:16:30.061+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:16:30.062+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ve37mdfdzaqsr5v7fws1fp5j3, remote user nhat.le
2025-07-10T12:16:30.062+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:16:47.494+07:00 DEBUG 9257 --- [qtp47827984-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:16:59.157+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:16:59.158+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ve37mdfdzaqsr5v7fws1fp5j3, remote user nhat.le
2025-07-10T12:16:59.158+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:17:06.171+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:17:27.240+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T12:17:27.245+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:17:27.245+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ve37mdfdzaqsr5v7fws1fp5j3, remote user nhat.le
2025-07-10T12:17:27.245+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:17:27.245+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T12:17:47.513+07:00 DEBUG 9257 --- [qtp47827984-100] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:18:00.346+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:18:00.347+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ve37mdfdzaqsr5v7fws1fp5j3, remote user nhat.le
2025-07-10T12:18:00.347+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:18:02.357+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:18:30.451+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:18:30.452+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ve37mdfdzaqsr5v7fws1fp5j3, remote user nhat.le
2025-07-10T12:18:30.453+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:18:47.516+07:00 DEBUG 9257 --- [qtp47827984-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:18:58.524+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:18:58.524+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ve37mdfdzaqsr5v7fws1fp5j3, remote user nhat.le
2025-07-10T12:18:58.525+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:19:05.547+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:19:26.608+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T12:19:26.610+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:19:26.610+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ve37mdfdzaqsr5v7fws1fp5j3, remote user nhat.le
2025-07-10T12:19:26.610+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:19:26.611+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T12:19:47.514+07:00 DEBUG 9257 --- [qtp47827984-100] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:19:59.707+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:19:59.708+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ve37mdfdzaqsr5v7fws1fp5j3, remote user nhat.le
2025-07-10T12:19:59.708+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:20:06.729+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:20:06.730+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T12:20:13.018+07:00  INFO 9257 --- [Scheduler-96119738-1] n.d.m.session.AppHttpSessionListener     : The session node01ve37mdfdzaqsr5v7fws1fp5j3 is destroyed.
2025-07-10T12:20:29.797+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:20:29.797+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:20:57.876+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:20:57.877+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:21:04.893+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:21:25.964+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T12:21:25.971+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:21:25.972+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:21:25.972+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T12:21:37.517+07:00  INFO 9257 --- [qtp47827984-60] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T12:22:00.065+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:22:00.066+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:22:06.080+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:22:29.159+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:22:29.161+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:22:40.517+07:00  INFO 9257 --- [qtp47827984-69] n.d.module.session.ClientSessionManager  : Add a client session id = node09gd9i3e24ezz4hx7t8gbbwt2, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T12:22:40.518+07:00  INFO 9257 --- [qtp47827984-60] n.d.module.session.ClientSessionManager  : Add a client session id = node09gd9i3e24ezz4hx7t8gbbwt2, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T12:22:40.540+07:00  INFO 9257 --- [qtp47827984-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T12:22:40.541+07:00  INFO 9257 --- [qtp47827984-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T12:22:43.641+07:00 DEBUG 9257 --- [qtp47827984-100] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:22:43.685+07:00 DEBUG 9257 --- [qtp47827984-101] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:22:57.238+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:22:57.239+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:22:57.240+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:23:04.264+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:23:30.342+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-10T12:23:30.353+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:23:30.354+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:23:30.355+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:23:30.356+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T12:23:44.515+07:00 DEBUG 9257 --- [qtp47827984-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:24:00.428+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:24:00.428+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:24:00.428+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:24:06.448+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:24:28.515+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:24:28.516+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:24:28.516+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:24:44.517+07:00 DEBUG 9257 --- [qtp47827984-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:24:56.595+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:24:56.596+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:24:56.596+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:25:03.603+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:25:03.604+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T12:25:29.663+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-10T12:25:29.666+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:25:29.666+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:25:29.666+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:25:29.666+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T12:25:44.537+07:00 DEBUG 9257 --- [qtp47827984-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:25:59.770+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:25:59.771+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:25:59.771+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:26:06.788+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:26:27.843+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:26:27.844+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:26:27.844+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:26:44.527+07:00 DEBUG 9257 --- [qtp47827984-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:26:55.920+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:26:55.921+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:26:55.921+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:27:02.931+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:27:30.021+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T12:27:30.025+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:27:30.025+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:27:30.025+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:27:30.026+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T12:27:44.535+07:00 DEBUG 9257 --- [qtp47827984-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T12:27:59.127+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:27:59.129+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:27:59.129+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:28:06.150+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:28:27.224+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:28:27.226+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:28:27.226+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:29:00.296+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:29:00.297+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:29:00.297+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:29:02.313+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T12:44:30.408+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 5
2025-07-10T12:44:30.412+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:44:30.413+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09gd9i3e24ezz4hx7t8gbbwt2, remote user nhat.le
2025-07-10T12:44:30.413+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T12:44:30.413+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T12:44:37.510+07:00  INFO 9257 --- [qtp47827984-81] n.d.m.session.AppHttpSessionListener     : The session node09gd9i3e24ezz4hx7t8gbbwt2 is destroyed.
2025-07-10T12:44:37.514+07:00  INFO 9257 --- [qtp47827984-81] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T12:44:42.923+07:00  WARN 9257 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=15m29s993ms).
2025-07-10T12:44:42.923+07:00  WARN 9257 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=15m29s990ms).
2025-07-10T12:44:43.352+07:00  WARN 9257 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m30s).
2025-07-10T12:44:43.385+07:00  WARN 9257 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=15m30s).
2025-07-10T12:44:58.478+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T12:44:58.479+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:00:04.947+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:00:04.949+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T13:00:04.950+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@13:00:04+0700
2025-07-10T13:00:04.975+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@13:00:00+0700 to 10/07/2025@13:15:00+0700
2025-07-10T13:00:04.975+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@13:00:00+0700 to 10/07/2025@13:15:00+0700
2025-07-10T13:00:04.975+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T13:00:10.986+07:00 DEBUG 9257 --- [botTaskExecutor-3] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T13:00:12.373+07:00  WARN 9257 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=15m29s449ms).
2025-07-10T13:00:12.373+07:00  WARN 9257 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=15m29s450ms).
2025-07-10T13:00:12.802+07:00  WARN 9257 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m29s450ms).
2025-07-10T13:00:12.835+07:00  WARN 9257 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=15m29s450ms).
2025-07-10T13:00:26.018+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:00:26.019+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:00:39.900+07:00  INFO 9257 --- [qtp47827984-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node051gdsjjb6dky1v9oxux07l8mx5
2025-07-10T13:00:39.900+07:00  INFO 9257 --- [qtp47827984-81] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0c7fdenpzjsurogyg4kbbekfp4
2025-07-10T13:00:39.953+07:00  INFO 9257 --- [qtp47827984-81] n.d.module.session.ClientSessionManager  : Add a client session id = node0c7fdenpzjsurogyg4kbbekfp4, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T13:00:39.954+07:00  INFO 9257 --- [qtp47827984-34] n.d.module.session.ClientSessionManager  : Add a client session id = node051gdsjjb6dky1v9oxux07l8mx5, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T13:00:39.997+07:00  INFO 9257 --- [qtp47827984-81] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T13:00:40.007+07:00  INFO 9257 --- [qtp47827984-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T13:00:48.570+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:00:48.570+07:00 DEBUG 9257 --- [qtp47827984-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:17:59.687+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:17:59.695+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node051gdsjjb6dky1v9oxux07l8mx5, remote user nhat.le
2025-07-10T13:17:59.697+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:18:06.698+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:18:12.945+07:00  WARN 9257 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=17m30s565ms).
2025-07-10T13:18:12.945+07:00  WARN 9257 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=17m30s566ms).
2025-07-10T13:18:13.371+07:00  WARN 9257 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m30s565ms).
2025-07-10T13:18:13.402+07:00  WARN 9257 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=17m30s566ms).
2025-07-10T13:18:29.771+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 6
2025-07-10T13:18:29.780+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:18:29.780+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node051gdsjjb6dky1v9oxux07l8mx5, remote user nhat.le
2025-07-10T13:18:29.780+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:18:29.781+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:18:49.500+07:00  INFO 9257 --- [qtp47827984-100] n.d.m.session.AppHttpSessionListener     : The session node051gdsjjb6dky1v9oxux07l8mx5 is destroyed.
2025-07-10T13:18:49.514+07:00  INFO 9257 --- [qtp47827984-100] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T13:34:01.318+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:34:01.320+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:34:08.336+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:34:16.414+07:00  WARN 9257 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=15m33s467ms).
2025-07-10T13:34:16.414+07:00  WARN 9257 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=15m33s467ms).
2025-07-10T13:34:16.834+07:00  WARN 9257 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m33s466ms).
2025-07-10T13:34:16.863+07:00  WARN 9257 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=15m33s466ms).
2025-07-10T13:34:29.409+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:34:29.411+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:42:40.057+07:00  WARN 9257 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=8m23s643ms).
2025-07-10T13:42:40.057+07:00  WARN 9257 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=8m23s643ms).
2025-07-10T13:42:40.481+07:00  WARN 9257 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=8m23s647ms).
2025-07-10T13:42:40.510+07:00  WARN 9257 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=8m23s647ms).
2025-07-10T13:42:47.651+07:00  INFO 9257 --- [qtp47827984-100] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01b6y87yzaz0j11icn2ncz7figi6
2025-07-10T13:42:47.651+07:00  INFO 9257 --- [qtp47827984-74] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0b6hwrlu5ekl21kxw6jhonx2537
2025-07-10T13:42:47.696+07:00  INFO 9257 --- [qtp47827984-100] n.d.module.session.ClientSessionManager  : Add a client session id = node01b6y87yzaz0j11icn2ncz7figi6, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T13:42:47.696+07:00  INFO 9257 --- [qtp47827984-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0b6hwrlu5ekl21kxw6jhonx2537, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T13:42:47.767+07:00  INFO 9257 --- [qtp47827984-74] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T13:42:47.767+07:00  INFO 9257 --- [qtp47827984-100] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T13:42:49.165+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:42:49.165+07:00 DEBUG 9257 --- [qtp47827984-100] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:42:57.104+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:42:57.106+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:42:57.106+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:42:58.112+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:43:05.128+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:43:26.203+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 1
2025-07-10T13:43:26.214+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:43:26.214+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:43:26.214+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:43:26.215+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:43:49.207+07:00 DEBUG 9257 --- [qtp47827984-74] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:43:54.254+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:43:54.255+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:43:54.255+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:44:06.266+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:44:27.309+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:44:27.310+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:44:27.310+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:44:49.574+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:44:57.365+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:44:57.367+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:44:57.368+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:45:04.379+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T13:45:04.380+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:45:25.416+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:45:25.420+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:45:25.420+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:45:25.420+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:45:25.421+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:45:49.603+07:00 DEBUG 9257 --- [qtp47827984-74] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:45:53.464+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:45:53.464+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:45:53.464+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:46:06.490+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:46:27.521+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:46:27.522+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:46:27.522+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:46:49.588+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:46:56.574+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:46:56.574+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:46:56.574+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:47:03.587+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:47:24.717+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T13:47:24.721+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:47:24.721+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:47:24.721+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:47:24.722+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:47:49.606+07:00 DEBUG 9257 --- [qtp47827984-74] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:47:52.772+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:47:52.772+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:47:52.772+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:48:06.799+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:48:26.829+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:48:26.831+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:48:26.831+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:48:55.878+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:48:55.879+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:48:55.879+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:49:02.888+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:49:23.929+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:49:23.932+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:49:23.932+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:49:23.932+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:49:23.933+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:49:34.602+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:49:56.983+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:49:56.983+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:49:56.983+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:50:06.003+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T13:50:06.004+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:50:27.031+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:50:27.032+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:50:27.032+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:50:34.572+07:00 DEBUG 9257 --- [qtp47827984-74] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:50:55.085+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:50:55.086+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:50:55.086+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:51:02.099+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:51:23.148+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T13:51:23.154+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:51:23.154+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:51:23.155+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:51:23.156+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:51:34.682+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:51:57.213+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:51:57.214+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0b6hwrlu5ekl21kxw6jhonx2537, remote user nhat.le
2025-07-10T13:51:57.215+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:52:05.225+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:52:10.116+07:00  INFO 9257 --- [Scheduler-96119738-1] n.d.m.session.AppHttpSessionListener     : The session node0c7fdenpzjsurogyg4kbbekfp4 is destroyed.
2025-07-10T13:52:10.116+07:00  INFO 9257 --- [Scheduler-96119738-1] n.d.m.session.AppHttpSessionListener     : The session node0b6hwrlu5ekl21kxw6jhonx2537 is destroyed.
2025-07-10T13:52:21.255+07:00  INFO 9257 --- [qtp47827984-100] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T13:52:24.017+07:00  INFO 9257 --- [qtp47827984-74] n.d.module.session.ClientSessionManager  : Add a client session id = node01b6y87yzaz0j11icn2ncz7figi6, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T13:52:24.017+07:00  INFO 9257 --- [qtp47827984-100] n.d.module.session.ClientSessionManager  : Add a client session id = node01b6y87yzaz0j11icn2ncz7figi6, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T13:52:24.035+07:00  INFO 9257 --- [qtp47827984-74] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T13:52:24.035+07:00  INFO 9257 --- [qtp47827984-100] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T13:52:25.347+07:00 DEBUG 9257 --- [qtp47827984-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:52:25.347+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:52:26.259+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:52:26.259+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:52:26.259+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:52:54.313+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:52:54.315+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:52:54.315+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:53:06.328+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:53:25.369+07:00 DEBUG 9257 --- [qtp47827984-81] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:53:27.389+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-10T13:53:27.401+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:53:27.402+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:53:27.402+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:53:27.403+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:53:57.462+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:53:57.465+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:53:57.466+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:54:04.480+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:54:25.377+07:00 DEBUG 9257 --- [qtp47827984-74] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:54:25.518+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:54:25.518+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:54:25.518+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:54:53.562+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:54:53.564+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:54:53.565+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:55:00.580+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T13:55:00.584+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@13:55:00+0700
2025-07-10T13:55:00.606+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@13:45:00+0700 to 10/07/2025@14:00:00+0700
2025-07-10T13:55:00.607+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@13:45:00+0700 to 10/07/2025@14:00:00+0700
2025-07-10T13:55:06.615+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:55:06.616+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T13:55:06.621+07:00 DEBUG 9257 --- [botTaskExecutor-3] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T13:55:25.376+07:00 DEBUG 9257 --- [qtp47827984-81] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:55:26.659+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T13:55:26.667+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:55:26.668+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:55:26.668+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:55:26.669+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:55:56.721+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:55:56.724+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:55:56.725+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:56:03.732+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:56:24.775+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:56:24.777+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:56:24.777+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:56:25.381+07:00 DEBUG 9257 --- [qtp47827984-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:56:52.826+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:56:52.828+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:56:52.829+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:57:06.850+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:57:25.383+07:00 DEBUG 9257 --- [qtp47827984-81] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:57:26.897+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T13:57:26.903+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:57:26.903+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:57:26.903+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:57:26.904+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:57:55.951+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:57:55.954+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:57:55.954+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:58:02.963+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:58:24.004+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:58:24.006+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:58:24.006+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:58:25.366+07:00 DEBUG 9257 --- [qtp47827984-145] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:58:57.049+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:58:57.051+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:58:57.051+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:59:06.066+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T13:59:25.361+07:00 DEBUG 9257 --- [qtp47827984-100] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T13:59:27.112+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T13:59:27.124+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:59:27.124+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:59:27.124+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T13:59:27.124+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T13:59:55.178+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T13:59:55.179+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T13:59:55.179+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:00:02.193+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:00:02.194+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T14:00:02.195+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@14:00:02+0700
2025-07-10T14:00:02.215+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@14:00:00+0700 to 10/07/2025@14:15:00+0700
2025-07-10T14:00:02.215+07:00  INFO 9257 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@14:00:00+0700 to 10/07/2025@14:15:00+0700
2025-07-10T14:00:02.219+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T14:00:08.228+07:00 DEBUG 9257 --- [botTaskExecutor-1] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T14:00:23.256+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:00:23.257+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:00:23.257+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:00:25.366+07:00 DEBUG 9257 --- [qtp47827984-74] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:00:57.312+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:00:57.313+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:00:57.313+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:01:05.329+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:01:25.383+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:01:26.371+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:01:26.376+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:01:26.376+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:01:26.376+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:01:26.376+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:01:54.416+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:01:54.418+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:01:54.418+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:02:06.427+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:02:25.355+07:00 DEBUG 9257 --- [qtp47827984-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:02:27.455+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:02:27.456+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:02:27.456+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:02:57.511+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:02:57.512+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:02:57.512+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:03:04.528+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:03:25.379+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:03:25.581+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-07-10T14:03:25.589+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:03:25.589+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:03:25.589+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:03:25.589+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:03:53.627+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:03:53.629+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:03:53.629+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:04:06.651+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:04:25.386+07:00 DEBUG 9257 --- [qtp47827984-74] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:04:26.680+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:04:26.680+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:04:26.681+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:04:56.750+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:04:56.752+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:04:56.753+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:05:03.762+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:05:03.764+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T14:05:17.095+07:00  WARN 9257 --- [rw housekeeper] com.zaxxer.hikari.pool.ProxyLeakTask     : Connection leak detection triggered for org.postgresql.jdbc.PgConnection@48f67429 on thread qtp47827984-74, stack trace follows

java.lang.Exception: Apparent connection leak detected
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:127)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.internal.NonContextualJdbcConnectionAccess.obtainConnection(NonContextualJdbcConnectionAccess.java:46)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.acquireConnectionIfNeeded(LogicalConnectionManagedImpl.java:113)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.getPhysicalConnection(LogicalConnectionManagedImpl.java:143)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.beginTransaction(HibernateJpaDialect.java:164)
	at org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:420)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:532)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:639)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:374)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.price.TruckPriceService$$SpringCGLIB$$0.searchTruckContainerTransportCharges(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:153)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:138)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:104)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:51)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-07-10T14:05:17.137+07:00  WARN 9257 --- [rw housekeeper] com.zaxxer.hikari.pool.ProxyLeakTask     : Connection leak detection triggered for org.postgresql.jdbc.PgConnection@f02f4c3 on thread qtp47827984-215, stack trace follows

java.lang.Exception: Apparent connection leak detected
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:127)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.internal.NonContextualJdbcConnectionAccess.obtainConnection(NonContextualJdbcConnectionAccess.java:46)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.acquireConnectionIfNeeded(LogicalConnectionManagedImpl.java:113)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.getPhysicalConnection(LogicalConnectionManagedImpl.java:143)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.beginTransaction(HibernateJpaDialect.java:164)
	at org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:420)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:532)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:639)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:374)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.price.TruckPriceService$$SpringCGLIB$$0.searchTruckContainerTransportCharges(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:153)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:138)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:104)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:51)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-07-10T14:05:24.809+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-07-10T14:05:24.818+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:05:24.819+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:05:24.819+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:05:24.825+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T14:05:25.359+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:05:39.986+07:00  INFO 9257 --- [qtp47827984-215] com.zaxxer.hikari.pool.ProxyLeakTask     : Previously reported leaked connection org.postgresql.jdbc.PgConnection@f02f4c3 on thread qtp47827984-215 was returned to the pool (unleaked)
2025-07-10T14:05:40.083+07:00  INFO 9257 --- [qtp47827984-74] com.zaxxer.hikari.pool.ProxyLeakTask     : Previously reported leaked connection org.postgresql.jdbc.PgConnection@48f67429 on thread qtp47827984-74 was returned to the pool (unleaked)
2025-07-10T14:05:52.876+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:05:52.877+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:05:52.877+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:06:06.905+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:06:25.361+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:06:26.945+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:06:26.945+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:06:26.945+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:06:55.991+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:06:55.993+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:06:55.993+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:07:03.004+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:07:24.062+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T14:07:24.070+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:07:24.070+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:07:24.070+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:07:24.076+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:07:25.366+07:00 DEBUG 9257 --- [qtp47827984-213] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:07:57.123+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:07:57.125+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:07:57.125+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:08:06.148+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:08:25.355+07:00 DEBUG 9257 --- [qtp47827984-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:08:27.184+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:08:27.185+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:08:27.185+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:08:55.229+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:08:55.230+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:08:55.230+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:09:02.244+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:09:23.300+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-07-10T14:09:23.305+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:09:23.305+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:09:23.305+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:09:23.306+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:09:25.369+07:00 DEBUG 9257 --- [qtp47827984-213] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:09:57.363+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:09:57.364+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:09:57.364+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:10:03.373+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-07-10T14:10:05.383+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:10:05.384+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T14:10:25.370+07:00 DEBUG 9257 --- [qtp47827984-74] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:10:26.412+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:10:26.412+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:10:26.412+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:10:54.458+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:10:54.460+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:10:54.460+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:11:06.485+07:00  INFO 9257 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:11:25.370+07:00 DEBUG 9257 --- [qtp47827984-213] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:11:27.532+07:00  INFO 9257 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T14:11:27.537+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:11:27.537+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01b6y87yzaz0j11icn2ncz7figi6, remote user nhat.le
2025-07-10T14:11:27.537+07:00 DEBUG 9257 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:11:27.537+07:00  INFO 9257 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-10T14:11:53.994+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@206de653{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-10T14:11:53.995+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T14:11:53.996+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T14:11:53.996+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-10T14:11:53.996+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-10T14:11:54.013+07:00  INFO 9257 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:11:54.057+07:00  INFO 9257 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-10T14:11:54.062+07:00  INFO 9257 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-10T14:11:54.086+07:00  INFO 9257 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:11:54.088+07:00  INFO 9257 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:11:54.089+07:00  INFO 9257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-10T14:11:54.090+07:00  INFO 9257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-10T14:11:54.090+07:00  INFO 9257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-10T14:11:54.269+07:00  INFO 9257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-10T14:11:54.269+07:00  INFO 9257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-10T14:11:54.270+07:00  INFO 9257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-10T14:11:54.270+07:00  INFO 9257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-10T14:11:54.271+07:00  INFO 9257 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-10T14:11:54.274+07:00  INFO 9257 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@2f26634a{STOPPING}[12.0.15,sto=0]
2025-07-10T14:11:54.282+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10T14:11:54.286+07:00  INFO 9257 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@57e351a{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10283956197699364826/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@4098ef14{STOPPED}}
