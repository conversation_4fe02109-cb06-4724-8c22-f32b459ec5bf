2025-07-10T14:22:39.218+07:00  INFO 21716 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 21716 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-10T14:22:39.219+07:00  INFO 21716 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-10T14:22:39.937+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.002+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 22 JPA repository interfaces.
2025-07-10T14:22:40.011+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.012+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T14:22:40.012+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.055+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 42 ms. Found 9 JPA repository interfaces.
2025-07-10T14:22:40.056+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.059+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T14:22:40.059+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.063+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T14:22:40.073+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.078+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-10T14:22:40.088+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.092+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-07-10T14:22:40.096+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.099+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T14:22:40.099+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.099+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:22:40.104+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.110+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T14:22:40.114+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.116+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T14:22:40.117+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.120+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T14:22:40.122+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.130+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-10T14:22:40.130+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.133+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T14:22:40.133+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.133+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:22:40.134+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.134+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-10T14:22:40.135+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.139+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-10T14:22:40.139+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.140+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T14:22:40.140+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.140+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:22:40.140+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.151+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-10T14:22:40.161+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.167+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-07-10T14:22:40.167+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.170+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T14:22:40.170+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.174+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-10T14:22:40.174+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.179+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-07-10T14:22:40.179+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.183+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T14:22:40.183+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.187+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-07-10T14:22:40.187+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.196+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-07-10T14:22:40.196+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.211+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 25 JPA repository interfaces.
2025-07-10T14:22:40.224+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.237+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-07-10T14:22:40.237+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.241+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T14:22:40.241+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.242+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T14:22:40.247+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.248+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:22:40.248+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.256+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-10T14:22:40.260+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.297+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 37 ms. Found 67 JPA repository interfaces.
2025-07-10T14:22:40.297+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.299+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T14:22:40.299+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:22:40.301+07:00  INFO 21716 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T14:22:40.523+07:00  INFO 21716 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-10T14:22:40.526+07:00  INFO 21716 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-10T14:22:40.792+07:00  WARN 21716 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-10T14:22:40.982+07:00  INFO 21716 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-10T14:22:40.984+07:00  INFO 21716 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-10T14:22:40.995+07:00  INFO 21716 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-10T14:22:40.995+07:00  INFO 21716 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1667 ms
2025-07-10T14:22:41.043+07:00  WARN 21716 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:22:41.043+07:00  INFO 21716 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-10T14:22:41.203+07:00  INFO 21716 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@2ba0b7cf
2025-07-10T14:22:41.203+07:00  INFO 21716 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-10T14:22:41.208+07:00  WARN 21716 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:22:41.208+07:00  INFO 21716 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-10T14:22:41.216+07:00  INFO 21716 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@64e6f678
2025-07-10T14:22:41.216+07:00  INFO 21716 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-10T14:22:41.217+07:00  WARN 21716 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:22:41.217+07:00  INFO 21716 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-10T14:22:41.704+07:00  INFO 21716 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@74691a1f
2025-07-10T14:22:41.705+07:00  INFO 21716 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-10T14:22:41.705+07:00  WARN 21716 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:22:41.705+07:00  INFO 21716 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-10T14:22:41.713+07:00  INFO 21716 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@485902cd
2025-07-10T14:22:41.714+07:00  INFO 21716 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-10T14:22:41.714+07:00  INFO 21716 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-10T14:22:41.763+07:00  INFO 21716 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-10T14:22:41.765+07:00  INFO 21716 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@6f5690f3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7435143353903373921/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@11ed241c{STARTED}}
2025-07-10T14:22:41.766+07:00  INFO 21716 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@6f5690f3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7435143353903373921/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@11ed241c{STARTED}}
2025-07-10T14:22:41.767+07:00  INFO 21716 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@70f5f6c7{STARTING}[12.0.15,sto=0] @3123ms
2025-07-10T14:22:41.822+07:00  INFO 21716 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T14:22:41.850+07:00  INFO 21716 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-10T14:22:41.864+07:00  INFO 21716 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T14:22:41.986+07:00  INFO 21716 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T14:22:42.011+07:00  WARN 21716 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T14:22:42.637+07:00  INFO 21716 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T14:22:42.644+07:00  INFO 21716 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@51b59c1a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T14:22:42.673+07:00  INFO 21716 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:22:42.843+07:00  INFO 21716 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.groovy", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-07-10T14:22:42.845+07:00  INFO 21716 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-10T14:22:42.852+07:00  INFO 21716 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T14:22:42.853+07:00  INFO 21716 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T14:22:42.885+07:00  INFO 21716 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T14:22:42.890+07:00  WARN 21716 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T14:22:45.584+07:00  INFO 21716 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T14:22:45.585+07:00  INFO 21716 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5a2f119] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T14:22:45.682+07:00  INFO 21716 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:22:45.718+07:00  INFO 21716 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-10T14:22:45.722+07:00  INFO 21716 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-10T14:22:45.723+07:00  INFO 21716 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T14:22:45.729+07:00  WARN 21716 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T14:22:45.857+07:00  INFO 21716 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10T14:22:46.318+07:00  INFO 21716 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T14:22:46.321+07:00  INFO 21716 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T14:22:46.356+07:00  INFO 21716 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-10T14:22:46.393+07:00  INFO 21716 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-10T14:22:46.440+07:00  INFO 21716 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-10T14:22:46.469+07:00  WARN 21716 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 88.7MB of free physical memory - some paging will therefore occur.
2025-07-10T14:22:46.469+07:00  INFO 21716 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T14:22:46.495+07:00  INFO 21716 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 20210062ms : this is harmless.
2025-07-10T14:22:46.502+07:00  INFO 21716 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-10T14:22:46.505+07:00  WARN 21716 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 92.5MB of free physical memory - some paging will therefore occur.
2025-07-10T14:22:46.505+07:00  INFO 21716 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T14:22:46.516+07:00  INFO 21716 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 20210049ms : this is harmless.
2025-07-10T14:22:46.518+07:00  INFO 21716 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-10T14:22:46.532+07:00  INFO 21716 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-10T14:22:46.533+07:00  INFO 21716 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-10T14:22:49.135+07:00  INFO 21716 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@14:15:00+0700 to 10/07/2025@14:30:00+0700
2025-07-10T14:22:49.135+07:00  INFO 21716 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@14:15:00+0700 to 10/07/2025@14:30:00+0700
2025-07-10T14:22:49.736+07:00  INFO 21716 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-10T14:22:49.736+07:00  INFO 21716 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T14:22:49.737+07:00  WARN 21716 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T14:22:50.156+07:00  INFO 21716 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-10T14:22:50.156+07:00  INFO 21716 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-10T14:22:50.156+07:00  INFO 21716 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-10T14:22:50.156+07:00  INFO 21716 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-10T14:22:50.156+07:00  INFO 21716 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-10T14:22:51.650+07:00  WARN 21716 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: a32c1025-bc4d-448d-af52-9d3ada355191

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10T14:22:51.654+07:00  INFO 21716 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-10T14:22:51.966+07:00  INFO 21716 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T14:22:51.967+07:00  INFO 21716 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-10T14:22:51.967+07:00  INFO 21716 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-10T14:22:51.967+07:00  INFO 21716 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T14:22:51.967+07:00  INFO 21716 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-10T14:22:51.967+07:00  INFO 21716 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T14:22:51.967+07:00  INFO 21716 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T14:22:51.967+07:00  INFO 21716 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-10T14:22:51.967+07:00  INFO 21716 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T14:22:51.967+07:00  INFO 21716 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T14:22:51.967+07:00  INFO 21716 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-10T14:22:51.967+07:00  INFO 21716 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-10T14:22:51.970+07:00  INFO 21716 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T14:22:51.981+07:00  INFO 21716 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T14:22:51.981+07:00  INFO 21716 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T14:22:52.029+07:00  INFO 21716 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10T14:22:52.029+07:00  INFO 21716 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-10T14:22:52.032+07:00  INFO 21716 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-10T14:22:52.041+07:00  INFO 21716 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@3628c8e8{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T14:22:52.042+07:00  INFO 21716 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-10T14:22:52.043+07:00  INFO 21716 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-10T14:22:52.071+07:00  INFO 21716 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-10T14:22:52.071+07:00  INFO 21716 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-10T14:22:52.076+07:00  INFO 21716 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.172 seconds (process running for 13.433)
2025-07-10T14:23:06.011+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:23:17.150+07:00  INFO 21716 --- [qtp1844974364-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01kzndyj9k2jdh10p7ur528h7hs0
2025-07-10T14:23:17.150+07:00  INFO 21716 --- [qtp1844974364-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0av0l9ot06z3l1isvzbt0q0z8w1
2025-07-10T14:23:17.258+07:00  INFO 21716 --- [qtp1844974364-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01kzndyj9k2jdh10p7ur528h7hs0, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:23:17.276+07:00  INFO 21716 --- [qtp1844974364-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0av0l9ot06z3l1isvzbt0q0z8w1, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:23:17.342+07:00  INFO 21716 --- [qtp1844974364-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:23:17.342+07:00  INFO 21716 --- [qtp1844974364-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:23:19.077+07:00 DEBUG 21716 --- [qtp1844974364-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:23:19.094+07:00 DEBUG 21716 --- [qtp1844974364-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:23:27.856+07:00  INFO 21716 --- [qtp1844974364-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01kzndyj9k2jdh10p7ur528h7hs0, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:23:27.857+07:00  INFO 21716 --- [qtp1844974364-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01kzndyj9k2jdh10p7ur528h7hs0, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:23:27.867+07:00  INFO 21716 --- [qtp1844974364-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:23:27.871+07:00  INFO 21716 --- [qtp1844974364-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:23:29.588+07:00 DEBUG 21716 --- [qtp1844974364-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:23:29.588+07:00 DEBUG 21716 --- [qtp1844974364-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:23:55.141+07:00  INFO 21716 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-07-10T14:23:55.157+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:23:55.158+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:23:55.158+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:23:55.160+07:00  INFO 21716 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T14:24:02.172+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:24:28.208+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:24:28.212+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:24:28.212+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:24:29.631+07:00 DEBUG 21716 --- [qtp1844974364-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:24:58.272+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:24:58.274+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:24:58.274+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:25:05.283+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:25:05.286+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T14:25:26.318+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:25:26.319+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:25:26.319+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:25:29.613+07:00 DEBUG 21716 --- [qtp1844974364-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:25:54.373+07:00  INFO 21716 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:25:54.379+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:25:54.379+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:25:54.379+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:25:54.379+07:00  INFO 21716 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:26:06.393+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:26:28.436+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:26:28.437+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:26:28.438+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:26:29.613+07:00 DEBUG 21716 --- [qtp1844974364-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:26:57.495+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:26:57.495+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:26:57.496+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:27:04.504+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:27:25.540+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:27:25.542+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:27:25.542+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:27:29.598+07:00 DEBUG 21716 --- [qtp1844974364-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:27:58.623+07:00  INFO 21716 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-10T14:27:58.639+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:27:58.639+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:27:58.639+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:27:58.640+07:00  INFO 21716 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:28:06.659+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:28:28.706+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:28:28.708+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:28:28.708+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:28:29.598+07:00 DEBUG 21716 --- [qtp1844974364-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:28:56.752+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:28:56.758+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:28:56.758+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:29:03.770+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:29:24.809+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:29:24.810+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:29:24.810+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:29:29.613+07:00 DEBUG 21716 --- [qtp1844974364-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:29:58.891+07:00  INFO 21716 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:29:58.904+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:29:58.905+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:29:58.905+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:29:58.905+07:00  INFO 21716 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:30:06.925+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:30:06.928+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T14:30:06.930+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T14:30:06.932+07:00  INFO 21716 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@14:30:06+0700
2025-07-10T14:30:06.964+07:00  INFO 21716 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@14:30:00+0700 to 10/07/2025@14:45:00+0700
2025-07-10T14:30:06.964+07:00  INFO 21716 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@14:30:00+0700 to 10/07/2025@14:45:00+0700
2025-07-10T14:30:13.008+07:00 DEBUG 21716 --- [botTaskExecutor-1] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T14:30:28.017+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:30:28.018+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:30:28.019+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:30:29.620+07:00 DEBUG 21716 --- [qtp1844974364-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:30:56.061+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:30:56.062+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:30:56.062+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:31:03.076+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:31:24.108+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:31:24.110+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:31:24.110+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:31:29.606+07:00 DEBUG 21716 --- [qtp1844974364-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:31:58.192+07:00  INFO 21716 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:31:58.217+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:31:58.217+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:31:58.217+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:31:58.217+07:00  INFO 21716 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:32:06.238+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:32:27.269+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:32:27.270+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:32:27.270+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:32:29.623+07:00 DEBUG 21716 --- [qtp1844974364-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:32:41.788+07:00  INFO 21716 --- [Scheduler-1408662262-1] n.d.m.session.AppHttpSessionListener     : The session node0av0l9ot06z3l1isvzbt0q0z8w1 is destroyed.
2025-07-10T14:32:55.304+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:32:55.305+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:33:02.316+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:33:28.351+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:33:28.353+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:33:29.630+07:00  INFO 21716 --- [qtp1844974364-35] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T14:33:58.404+07:00  INFO 21716 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 6
2025-07-10T14:33:58.420+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:33:58.420+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:33:58.421+07:00  INFO 21716 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:34:05.432+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:34:26.466+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:34:26.467+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:34:30.683+07:00  INFO 21716 --- [qtp1844974364-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01kzndyj9k2jdh10p7ur528h7hs0, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:34:30.699+07:00  INFO 21716 --- [qtp1844974364-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:34:30.720+07:00  INFO 21716 --- [qtp1844974364-69] n.d.module.session.ClientSessionManager  : Add a client session id = node01kzndyj9k2jdh10p7ur528h7hs0, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:34:30.735+07:00  INFO 21716 --- [qtp1844974364-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:34:32.083+07:00 DEBUG 21716 --- [qtp1844974364-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:34:32.083+07:00 DEBUG 21716 --- [qtp1844974364-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:34:54.520+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:34:54.522+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:34:54.522+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:35:06.540+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T14:35:06.544+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:35:28.582+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:35:28.584+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:35:28.585+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:35:32.110+07:00 DEBUG 21716 --- [qtp1844974364-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:35:57.651+07:00  INFO 21716 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-10T14:35:57.668+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:35:57.669+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:35:57.669+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:35:57.673+07:00  INFO 21716 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T14:36:04.688+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:36:25.721+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:36:25.722+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:36:25.723+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:36:32.101+07:00 DEBUG 21716 --- [qtp1844974364-64] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:36:58.777+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:36:58.780+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:36:58.780+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:37:06.791+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:37:28.836+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:37:28.838+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:37:28.838+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:37:32.096+07:00 DEBUG 21716 --- [qtp1844974364-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:37:56.889+07:00  INFO 21716 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:37:56.896+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:37:56.897+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:37:56.897+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:37:56.900+07:00  INFO 21716 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:38:03.908+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:38:24.943+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:38:24.944+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:38:24.945+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:38:32.103+07:00 DEBUG 21716 --- [qtp1844974364-64] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:38:58.997+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:38:58.998+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:38:58.998+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:39:06.006+07:00  INFO 21716 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:39:28.053+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:39:28.054+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01kzndyj9k2jdh10p7ur528h7hs0, remote user nhat.le
2025-07-10T14:39:28.054+07:00 DEBUG 21716 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:39:32.119+07:00 DEBUG 21716 --- [qtp1844974364-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:39:37.898+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@3628c8e8{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-10T14:39:37.899+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-10T14:39:37.915+07:00  INFO 21716 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:39:37.993+07:00  INFO 21716 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-10T14:39:37.998+07:00  INFO 21716 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-10T14:39:38.028+07:00  INFO 21716 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:39:38.032+07:00  INFO 21716 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:39:38.032+07:00  INFO 21716 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-10T14:39:38.034+07:00  INFO 21716 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-10T14:39:38.034+07:00  INFO 21716 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-10T14:39:38.174+07:00  INFO 21716 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-10T14:39:38.175+07:00  INFO 21716 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-10T14:39:38.175+07:00  INFO 21716 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-10T14:39:38.175+07:00  INFO 21716 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-10T14:39:38.176+07:00  INFO 21716 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-10T14:39:38.179+07:00  INFO 21716 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@70f5f6c7{STOPPING}[12.0.15,sto=0]
2025-07-10T14:39:38.185+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10T14:39:38.187+07:00  INFO 21716 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@6f5690f3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7435143353903373921/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@11ed241c{STOPPED}}
