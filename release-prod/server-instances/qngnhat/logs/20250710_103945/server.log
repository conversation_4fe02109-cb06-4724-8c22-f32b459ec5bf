2025-07-10T10:39:46.309+07:00  INFO 98540 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 98540 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-10T10:39:46.311+07:00  INFO 98540 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-10T10:39:47.133+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.234+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 97 ms. Found 22 JPA repository interfaces.
2025-07-10T10:39:47.243+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.244+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T10:39:47.245+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.252+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 9 JPA repository interfaces.
2025-07-10T10:39:47.253+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.255+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T10:39:47.256+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.259+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T10:39:47.270+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.275+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-10T10:39:47.285+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.289+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-10T10:39:47.293+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.296+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T10:39:47.296+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.296+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:39:47.301+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.307+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T10:39:47.312+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.315+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T10:39:47.315+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.318+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T10:39:47.320+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.329+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-07-10T10:39:47.329+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.332+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T10:39:47.332+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.332+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:39:47.332+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.333+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-10T10:39:47.333+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.337+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-10T10:39:47.337+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.339+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T10:39:47.339+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.339+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:39:47.339+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.349+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-07-10T10:39:47.358+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.364+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-07-10T10:39:47.365+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.367+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T10:39:47.367+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.372+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-10T10:39:47.372+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.377+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T10:39:47.377+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.381+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T10:39:47.381+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.384+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T10:39:47.384+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.393+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-07-10T10:39:47.394+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.408+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 25 JPA repository interfaces.
2025-07-10T10:39:47.418+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.431+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-07-10T10:39:47.431+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.435+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T10:39:47.435+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.437+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T10:39:47.441+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.441+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:39:47.442+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.449+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-10T10:39:47.452+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.489+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 37 ms. Found 67 JPA repository interfaces.
2025-07-10T10:39:47.489+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.490+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T10:39:47.491+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:39:47.493+07:00  INFO 98540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T10:39:47.697+07:00  INFO 98540 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-10T10:39:47.700+07:00  INFO 98540 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-10T10:39:47.964+07:00  WARN 98540 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-10T10:39:48.180+07:00  INFO 98540 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-10T10:39:48.182+07:00  INFO 98540 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-10T10:39:48.193+07:00  INFO 98540 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-10T10:39:48.193+07:00  INFO 98540 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1757 ms
2025-07-10T10:39:48.242+07:00  WARN 98540 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:39:48.243+07:00  INFO 98540 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-10T10:39:48.332+07:00  INFO 98540 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@602854ed
2025-07-10T10:39:48.333+07:00  INFO 98540 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-10T10:39:48.338+07:00  WARN 98540 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:39:48.338+07:00  INFO 98540 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-10T10:39:48.341+07:00  INFO 98540 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@5808573f
2025-07-10T10:39:48.342+07:00  INFO 98540 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-10T10:39:48.342+07:00  WARN 98540 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:39:48.342+07:00  INFO 98540 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-10T10:39:48.837+07:00  INFO 98540 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@f57ced1
2025-07-10T10:39:48.837+07:00  INFO 98540 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-10T10:39:48.837+07:00  WARN 98540 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:39:48.837+07:00  INFO 98540 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-10T10:39:48.852+07:00  INFO 98540 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@39fbee74
2025-07-10T10:39:48.852+07:00  INFO 98540 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-10T10:39:48.852+07:00  INFO 98540 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-10T10:39:48.905+07:00  INFO 98540 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-10T10:39:48.907+07:00  INFO 98540 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@3f19f197{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2164088700757137258/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@6757ed9e{STARTED}}
2025-07-10T10:39:48.908+07:00  INFO 98540 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@3f19f197{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2164088700757137258/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@6757ed9e{STARTED}}
2025-07-10T10:39:48.910+07:00  INFO 98540 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@6cc91654{STARTING}[12.0.15,sto=0] @3233ms
2025-07-10T10:39:48.974+07:00  INFO 98540 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T10:39:49.006+07:00  INFO 98540 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-10T10:39:49.022+07:00  INFO 98540 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T10:39:49.146+07:00  INFO 98540 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T10:39:49.170+07:00  WARN 98540 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T10:39:49.812+07:00  INFO 98540 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T10:39:49.821+07:00  INFO 98540 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3fe19a69] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T10:39:49.855+07:00  INFO 98540 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T10:39:50.043+07:00  INFO 98540 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-07-10T10:39:50.046+07:00  INFO 98540 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-10T10:39:50.053+07:00  INFO 98540 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T10:39:50.055+07:00  INFO 98540 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T10:39:50.087+07:00  INFO 98540 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T10:39:50.091+07:00  WARN 98540 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T10:39:53.052+07:00  INFO 98540 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T10:39:53.053+07:00  INFO 98540 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@227f88ee] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T10:39:53.148+07:00  INFO 98540 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T10:39:53.186+07:00  INFO 98540 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-10T10:39:53.191+07:00  INFO 98540 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-10T10:39:53.191+07:00  INFO 98540 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T10:39:53.197+07:00  WARN 98540 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T10:39:53.334+07:00  INFO 98540 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10T10:39:53.801+07:00  INFO 98540 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T10:39:53.803+07:00  INFO 98540 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T10:39:53.838+07:00  INFO 98540 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-10T10:39:53.878+07:00  INFO 98540 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-10T10:39:53.920+07:00  INFO 98540 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-10T10:39:53.947+07:00  INFO 98540 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T10:39:53.969+07:00  INFO 98540 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 6837942ms : this is harmless.
2025-07-10T10:39:53.982+07:00  INFO 98540 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-10T10:39:53.985+07:00  INFO 98540 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T10:39:53.999+07:00  INFO 98540 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 6837929ms : this is harmless.
2025-07-10T10:39:54.001+07:00  INFO 98540 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-10T10:39:54.016+07:00  INFO 98540 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-10T10:39:54.017+07:00  INFO 98540 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-10T10:39:56.690+07:00  INFO 98540 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@10:30:00+0700 to 10/07/2025@10:45:00+0700
2025-07-10T10:39:56.690+07:00  INFO 98540 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@10:30:00+0700 to 10/07/2025@10:45:00+0700
2025-07-10T10:39:57.311+07:00  INFO 98540 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-10T10:39:57.311+07:00  INFO 98540 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T10:39:57.312+07:00  WARN 98540 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T10:39:57.724+07:00  INFO 98540 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-10T10:39:57.724+07:00  INFO 98540 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-10T10:39:57.724+07:00  INFO 98540 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-10T10:39:57.724+07:00  INFO 98540 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-10T10:39:57.724+07:00  INFO 98540 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-10T10:39:59.189+07:00  WARN 98540 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 6a536924-841f-493d-9a19-9d955ed847a2

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10T10:39:59.192+07:00  INFO 98540 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-10T10:39:59.582+07:00  INFO 98540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T10:39:59.582+07:00  INFO 98540 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-10T10:39:59.583+07:00  INFO 98540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-10T10:39:59.583+07:00  INFO 98540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T10:39:59.583+07:00  INFO 98540 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-10T10:39:59.583+07:00  INFO 98540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T10:39:59.583+07:00  INFO 98540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T10:39:59.583+07:00  INFO 98540 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-10T10:39:59.583+07:00  INFO 98540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T10:39:59.583+07:00  INFO 98540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T10:39:59.583+07:00  INFO 98540 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-10T10:39:59.583+07:00  INFO 98540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-10T10:39:59.587+07:00  INFO 98540 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T10:39:59.587+07:00  INFO 98540 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T10:39:59.587+07:00  INFO 98540 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T10:39:59.637+07:00  INFO 98540 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10T10:39:59.637+07:00  INFO 98540 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-10T10:39:59.639+07:00  INFO 98540 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-10T10:39:59.647+07:00  INFO 98540 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@31f4dfc4{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T10:39:59.648+07:00  INFO 98540 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-10T10:39:59.649+07:00  INFO 98540 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-10T10:39:59.683+07:00  INFO 98540 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-10T10:39:59.683+07:00  INFO 98540 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-10T10:39:59.689+07:00  INFO 98540 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.742 seconds (process running for 14.012)
2025-07-10T10:40:06.602+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:40:06.605+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T10:40:41.495+07:00  INFO 98540 --- [qtp1244881865-60] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0190iee6rlto4tgovaf6j4emuz0
2025-07-10T10:40:41.495+07:00  INFO 98540 --- [qtp1244881865-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node05o86yxkre7vj1re0fb81qucmx1
2025-07-10T10:40:41.734+07:00  INFO 98540 --- [qtp1244881865-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0190iee6rlto4tgovaf6j4emuz0, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:40:41.734+07:00  INFO 98540 --- [qtp1244881865-35] n.d.module.session.ClientSessionManager  : Add a client session id = node05o86yxkre7vj1re0fb81qucmx1, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:40:41.818+07:00  INFO 98540 --- [qtp1244881865-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:40:41.829+07:00  INFO 98540 --- [qtp1244881865-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:40:43.263+07:00 DEBUG 98540 --- [qtp1244881865-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:40:43.263+07:00 DEBUG 98540 --- [qtp1244881865-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:41:02.736+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-10T10:41:02.760+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:41:02.761+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:41:02.761+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:41:02.763+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T10:41:02.764+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:41:35.827+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:41:35.827+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:41:35.828+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:41:43.269+07:00 DEBUG 98540 --- [qtp1244881865-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:42:05.890+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:42:05.892+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:42:05.893+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:42:05.893+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:42:33.950+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:42:33.954+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:42:33.955+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:42:43.262+07:00 DEBUG 98540 --- [qtp1244881865-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:43:02.063+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-07-10T10:43:02.066+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:43:02.066+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:43:02.066+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:43:02.067+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:43:07.073+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:43:36.120+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:43:36.122+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:43:36.122+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:43:43.235+07:00 DEBUG 98540 --- [qtp1244881865-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:44:05.172+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:44:05.175+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:44:05.176+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:44:05.177+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:44:33.252+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:44:33.253+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:44:33.254+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:44:43.251+07:00 DEBUG 98540 --- [qtp1244881865-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:45:06.331+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-10T10:45:06.352+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:45:06.353+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:45:06.353+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:45:06.353+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:45:06.354+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:45:06.357+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T10:45:06.358+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T10:45:06.359+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@10:45:06+0700
2025-07-10T10:45:06.368+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@10:45:00+0700 to 10/07/2025@11:00:00+0700
2025-07-10T10:45:06.368+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@10:45:00+0700 to 10/07/2025@11:00:00+0700
2025-07-10T10:45:07.423+07:00 DEBUG 98540 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T10:45:36.451+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:45:36.454+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:45:36.455+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:45:43.245+07:00 DEBUG 98540 --- [qtp1244881865-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:46:04.499+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:46:04.502+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:46:04.502+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:46:04.504+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:46:32.549+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:46:32.552+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:46:32.553+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:46:43.270+07:00 DEBUG 98540 --- [qtp1244881865-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:47:06.650+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-10T10:47:06.662+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:47:06.662+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:47:06.662+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:47:06.663+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:47:06.663+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:47:35.712+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:47:35.714+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:47:35.714+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:47:43.256+07:00 DEBUG 98540 --- [qtp1244881865-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:48:03.763+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:48:03.767+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:48:03.769+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:48:03.773+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:48:31.818+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:48:31.820+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:48:31.820+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:48:43.523+07:00 DEBUG 98540 --- [qtp1244881865-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:49:05.879+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:49:05.897+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:49:05.899+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:49:05.899+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:49:05.900+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:49:06.907+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:49:34.954+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:49:34.956+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05o86yxkre7vj1re0fb81qucmx1, remote user nhat.le
2025-07-10T10:49:34.956+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:49:48.934+07:00  INFO 98540 --- [Scheduler-1207847998-1] n.d.m.session.AppHttpSessionListener     : The session node05o86yxkre7vj1re0fb81qucmx1 is destroyed.
2025-07-10T10:50:03.006+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:50:03.006+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:50:03.007+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:50:03.008+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T10:50:36.073+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:50:36.074+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:50:37.541+07:00  INFO 98540 --- [qtp1244881865-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T10:51:06.150+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 4
2025-07-10T10:51:06.165+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:51:06.165+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:51:06.165+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:51:06.166+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:51:34.222+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:51:34.225+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:51:40.501+07:00  INFO 98540 --- [qtp1244881865-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0190iee6rlto4tgovaf6j4emuz0, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:51:40.525+07:00  INFO 98540 --- [qtp1244881865-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:51:40.529+07:00  INFO 98540 --- [qtp1244881865-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0190iee6rlto4tgovaf6j4emuz0, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:51:40.540+07:00  INFO 98540 --- [qtp1244881865-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:51:43.865+07:00 DEBUG 98540 --- [qtp1244881865-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:51:43.867+07:00 DEBUG 98540 --- [qtp1244881865-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:52:02.272+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:52:02.274+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:52:02.274+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:52:02.275+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:52:36.324+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:52:36.324+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:52:36.324+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:52:44.564+07:00 DEBUG 98540 --- [qtp1244881865-81] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:53:05.401+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-07-10T10:53:05.426+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:53:05.426+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:53:05.426+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:53:05.427+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:53:05.427+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:53:33.472+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:53:33.473+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:53:33.473+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:53:44.535+07:00 DEBUG 98540 --- [qtp1244881865-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:54:06.529+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:54:06.534+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:54:06.536+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:54:06.537+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:54:36.602+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:54:36.603+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:54:36.604+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:54:44.543+07:00 DEBUG 98540 --- [qtp1244881865-81] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:55:04.654+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 6
2025-07-10T10:55:04.662+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:55:04.663+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:55:04.663+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:55:04.664+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:55:04.664+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:55:04.666+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T10:55:32.712+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:55:32.716+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:55:32.716+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:55:44.562+07:00 DEBUG 98540 --- [qtp1244881865-75] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:56:05.769+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:56:05.770+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:56:05.770+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:56:06.776+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:56:35.839+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:56:35.841+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:56:35.841+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:56:43.896+07:00 DEBUG 98540 --- [qtp1244881865-81] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:57:03.894+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-10T10:57:03.901+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:57:03.902+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:57:03.902+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:57:03.902+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:57:03.903+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:57:31.947+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:57:31.949+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:57:31.949+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:57:43.911+07:00 DEBUG 98540 --- [qtp1244881865-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:58:06.005+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:58:06.006+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:58:06.007+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:58:07.011+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:58:35.056+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:58:35.057+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:58:35.058+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:58:43.889+07:00 DEBUG 98540 --- [qtp1244881865-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:59:03.110+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T10:59:03.122+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:59:03.122+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:59:03.123+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:59:03.123+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:59:03.124+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:59:36.174+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:59:36.176+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T10:59:36.176+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:59:43.933+07:00 DEBUG 98540 --- [qtp1244881865-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:00:06.233+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:00:06.233+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:00:06.234+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:00:06.234+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:00:06.234+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:00:06.235+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T11:00:06.235+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@11:00:06+0700
2025-07-10T11:00:06.252+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@11:00:00+0700 to 10/07/2025@11:15:00+0700
2025-07-10T11:00:06.252+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@11:00:00+0700 to 10/07/2025@11:15:00+0700
2025-07-10T11:00:06.254+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-07-10T11:00:12.265+07:00 DEBUG 98540 --- [botTaskExecutor-3] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T11:00:34.304+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:00:34.305+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:00:34.305+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:00:43.902+07:00 DEBUG 98540 --- [qtp1244881865-81] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:01:02.366+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T11:01:02.374+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:01:02.374+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:01:02.375+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:01:02.375+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:01:02.376+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:01:36.440+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:01:36.440+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:01:36.441+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:01:43.892+07:00 DEBUG 98540 --- [qtp1244881865-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:02:05.488+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:02:05.490+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:02:05.490+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:02:05.490+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:02:33.538+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:02:33.539+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:02:33.539+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:02:43.897+07:00 DEBUG 98540 --- [qtp1244881865-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:03:06.592+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 3
2025-07-10T11:03:06.599+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:03:06.599+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:03:06.599+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:03:06.599+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:03:06.599+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:03:36.656+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:03:36.658+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:03:36.659+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:03:43.914+07:00 DEBUG 98540 --- [qtp1244881865-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:04:04.706+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:04:04.707+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:04:04.708+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:04:04.708+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:04:32.764+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:04:32.766+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:04:32.766+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:04:44.514+07:00 DEBUG 98540 --- [qtp1244881865-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:05:05.816+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:05:05.818+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:05:05.819+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:05:05.819+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:05:05.819+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:05:06.823+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:05:06.823+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:05:35.883+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:05:35.884+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:05:35.885+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:05:43.902+07:00 DEBUG 98540 --- [qtp1244881865-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:06:03.936+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:06:03.937+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:06:03.938+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:06:03.938+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:06:31.979+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:06:31.982+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:06:31.983+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:06:43.940+07:00 DEBUG 98540 --- [qtp1244881865-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:07:06.048+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T11:07:06.062+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:07:06.062+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:07:06.062+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:07:06.063+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:07:06.063+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:07:35.105+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:07:35.106+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:07:35.107+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:07:43.890+07:00 DEBUG 98540 --- [qtp1244881865-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:08:03.162+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:08:03.163+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:08:03.163+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:08:03.163+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:08:36.198+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:08:36.201+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:08:36.202+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:08:44.513+07:00 DEBUG 98540 --- [qtp1244881865-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:09:06.256+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T11:09:06.259+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:09:06.259+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:09:06.259+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:09:06.260+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:09:06.260+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:09:34.303+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:09:34.304+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:09:34.305+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:10:02.351+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:10:02.353+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:10:02.354+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:10:02.355+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:10:02.356+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:10:36.416+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:10:36.417+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:10:36.417+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:10:37.541+07:00 DEBUG 98540 --- [qtp1244881865-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:11:05.475+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-10T11:11:05.481+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:11:05.481+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:11:05.481+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:11:05.482+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:11:05.483+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:11:33.537+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:11:33.538+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:11:33.538+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:11:37.553+07:00 DEBUG 98540 --- [qtp1244881865-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:12:06.581+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:12:06.583+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:12:06.583+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:12:06.583+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:12:36.636+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:12:36.636+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:12:36.636+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:12:37.509+07:00 DEBUG 98540 --- [qtp1244881865-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:13:04.707+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T11:13:04.715+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:13:04.715+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:13:04.715+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:13:04.715+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:13:04.716+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:13:32.769+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:13:32.770+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:13:32.770+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:13:37.507+07:00 DEBUG 98540 --- [qtp1244881865-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:14:05.825+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:14:05.826+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:14:05.827+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:14:06.833+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:14:35.880+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:14:35.881+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:14:35.881+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:14:37.514+07:00 DEBUG 98540 --- [qtp1244881865-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:15:03.924+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:15:03.933+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:15:03.934+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:15:03.935+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:15:03.936+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:15:03.939+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T11:15:03.941+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@11:15:03+0700
2025-07-10T11:15:03.969+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@11:15:00+0700 to 10/07/2025@11:30:00+0700
2025-07-10T11:15:03.969+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@11:15:00+0700 to 10/07/2025@11:30:00+0700
2025-07-10T11:15:03.970+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:15:03.970+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:15:09.976+07:00 DEBUG 98540 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T11:15:32.003+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:15:32.005+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:15:32.005+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:15:37.557+07:00 DEBUG 98540 --- [qtp1244881865-113] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:16:06.059+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:16:06.062+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:16:06.062+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:16:06.064+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:16:35.113+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:16:35.115+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:16:35.115+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:16:37.542+07:00 DEBUG 98540 --- [qtp1244881865-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:17:03.165+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T11:17:03.171+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:17:03.172+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:17:03.172+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:17:03.172+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:17:03.173+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:17:36.222+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:17:36.223+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:17:36.223+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:17:37.551+07:00 DEBUG 98540 --- [qtp1244881865-113] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:18:06.277+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:18:06.278+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:18:06.278+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:18:06.279+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:18:34.322+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:18:34.323+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:18:34.323+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:18:37.515+07:00 DEBUG 98540 --- [qtp1244881865-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:19:02.372+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:19:02.376+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:19:02.377+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:19:02.377+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:19:02.378+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:19:02.378+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:19:36.429+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:19:36.430+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:19:36.431+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:19:37.540+07:00 DEBUG 98540 --- [qtp1244881865-113] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:20:05.490+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:20:05.491+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:20:05.492+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:20:05.492+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:20:05.493+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:20:33.539+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:20:33.540+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:20:33.540+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:20:37.527+07:00 DEBUG 98540 --- [qtp1244881865-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:21:06.605+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T11:21:06.612+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:21:06.612+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:21:06.613+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:21:06.613+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:21:06.614+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:21:28.848+07:00 DEBUG 98540 --- [qtp1244881865-113] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:21:36.667+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:21:36.667+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:21:36.667+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:21:44.467+07:00 DEBUG 98540 --- [qtp1244881865-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:22:04.714+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:22:04.716+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:22:04.717+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:22:04.717+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:22:32.765+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:22:32.766+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:22:32.766+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:23:05.808+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:23:05.818+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:23:05.818+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:23:05.819+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:23:05.819+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:23:06.822+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:23:35.879+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:23:35.881+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:23:35.881+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:23:37.520+07:00 DEBUG 98540 --- [qtp1244881865-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:24:03.927+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:24:03.928+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:24:03.929+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:24:03.929+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:24:31.977+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:24:31.979+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:24:31.979+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:24:37.508+07:00 DEBUG 98540 --- [qtp1244881865-113] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:25:06.045+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T11:25:06.060+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:25:06.060+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:25:06.060+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:25:06.060+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:25:06.060+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:25:06.061+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:25:35.106+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:25:35.108+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:25:35.108+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:25:37.513+07:00 DEBUG 98540 --- [qtp1244881865-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:26:03.162+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:26:03.165+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:26:03.167+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:26:03.168+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:26:36.225+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:26:36.227+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:26:36.227+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:26:37.552+07:00 DEBUG 98540 --- [qtp1244881865-113] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:27:06.303+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T11:27:06.308+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:27:06.308+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:27:06.308+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:27:06.309+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:27:06.309+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:27:34.349+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:27:34.350+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:27:34.351+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:27:37.504+07:00 DEBUG 98540 --- [qtp1244881865-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:28:02.398+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:28:02.398+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:28:02.398+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:28:02.399+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:28:36.456+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:28:36.458+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:28:36.458+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:28:37.550+07:00 DEBUG 98540 --- [qtp1244881865-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:29:05.518+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:29:05.523+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:29:05.523+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:29:05.524+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:29:05.524+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:29:05.525+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:29:33.570+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:29:33.572+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:29:33.572+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:29:37.523+07:00 DEBUG 98540 --- [qtp1244881865-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:30:06.622+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:30:06.623+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:30:06.623+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:30:06.623+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:30:06.625+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:30:06.626+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@11:30:06+0700
2025-07-10T11:30:06.650+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@11:30:00+0700 to 10/07/2025@11:45:00+0700
2025-07-10T11:30:06.650+07:00  INFO 98540 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@11:30:00+0700 to 10/07/2025@11:45:00+0700
2025-07-10T11:30:06.651+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T11:30:07.658+07:00 DEBUG 98540 --- [botTaskExecutor-1] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T11:30:36.712+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:30:36.713+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:30:36.713+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:30:37.521+07:00 DEBUG 98540 --- [qtp1244881865-113] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:31:04.772+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T11:31:04.780+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:31:04.780+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:31:04.781+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:31:04.781+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:31:04.783+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:31:32.832+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:31:32.833+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:31:32.834+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:31:37.533+07:00 DEBUG 98540 --- [qtp1244881865-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:32:05.887+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:32:05.890+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:32:05.890+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:32:06.891+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:32:35.941+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:32:35.943+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:32:35.944+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:32:37.746+07:00 DEBUG 98540 --- [qtp1244881865-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:33:03.995+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:33:04.011+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:33:04.012+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:33:04.012+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:33:04.013+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:33:04.014+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:33:04.882+07:00 DEBUG 98540 --- [qtp1244881865-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:33:32.055+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:33:32.056+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:33:32.056+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:33:43.918+07:00 DEBUG 98540 --- [qtp1244881865-113] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:34:06.107+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:34:06.109+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:34:06.110+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:34:06.110+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:34:35.163+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:34:35.164+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:34:35.165+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:34:43.965+07:00 DEBUG 98540 --- [qtp1244881865-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:35:03.242+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T11:35:03.256+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:35:03.256+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:35:03.256+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:35:03.256+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T11:35:03.257+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:35:03.257+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:35:36.308+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:35:36.310+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:35:36.311+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:35:43.930+07:00 DEBUG 98540 --- [qtp1244881865-113] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:36:06.368+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:36:06.372+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:36:06.372+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:36:06.373+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:36:34.427+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:36:34.428+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:36:34.428+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:36:43.929+07:00 DEBUG 98540 --- [qtp1244881865-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:37:02.487+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-10T11:37:02.503+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:37:02.503+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:37:02.503+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:37:02.505+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T11:37:02.506+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:37:05.531+07:00 DEBUG 98540 --- [qtp1244881865-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:37:05.531+07:00 DEBUG 98540 --- [qtp1244881865-169] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:37:36.576+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:37:36.578+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:37:36.578+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:38:05.533+07:00 DEBUG 98540 --- [qtp1244881865-200] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:38:05.633+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:38:05.634+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:38:05.634+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:38:05.634+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:38:07.428+07:00  INFO 98540 --- [qtp1244881865-196] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-07-10T11:38:09.823+07:00  INFO 98540 --- [qtp1244881865-204] n.d.module.session.ClientSessionManager  : Add a client session id = node0190iee6rlto4tgovaf6j4emuz0, token = 50511f77f24f7ae042e390dac5c9c5a8
2025-07-10T11:38:09.882+07:00  INFO 98540 --- [qtp1244881865-204] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-07-10T11:38:10.919+07:00 DEBUG 98540 --- [qtp1244881865-207] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = jack.vnhph
2025-07-10T11:38:10.919+07:00 DEBUG 98540 --- [qtp1244881865-208] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = jack.vnhph
2025-07-10T11:38:33.688+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:38:33.689+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user jack.vnhph
2025-07-10T11:38:33.690+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:39:06.763+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-07-10T11:39:06.776+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:39:06.776+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user jack.vnhph
2025-07-10T11:39:06.776+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:39:06.783+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T11:39:06.785+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:39:10.921+07:00 DEBUG 98540 --- [qtp1244881865-208] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = jack.vnhph
2025-07-10T11:39:23.154+07:00  INFO 98540 --- [qtp1244881865-207] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph logout successfully 
2025-07-10T11:39:26.309+07:00 ERROR 98540 --- [qtp1244881865-200] net.datatp.module.account.AccountLogic   : User dan try to login into system, but fail
2025-07-10T11:39:26.315+07:00 ERROR 98540 --- [qtp1244881865-200] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:153)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:138)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:104)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:96)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-07-10T11:39:26.321+07:00  INFO 98540 --- [qtp1244881865-200] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/authenticate
2025-07-10T11:39:35.848+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:39:35.850+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:40:04.889+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:40:04.890+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:40:04.891+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:40:04.892+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T11:40:32.942+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:40:32.949+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:40:45.149+07:00  INFO 98540 --- [qtp1244881865-207] n.d.module.session.ClientSessionManager  : Add a client session id = node0190iee6rlto4tgovaf6j4emuz0, token = 268c90e74245ef2f39a80bbb0d073767
2025-07-10T11:40:45.165+07:00  INFO 98540 --- [qtp1244881865-207] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-07-10T11:40:46.213+07:00 DEBUG 98540 --- [qtp1244881865-204] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = minhtv
2025-07-10T11:40:46.213+07:00 DEBUG 98540 --- [qtp1244881865-200] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = minhtv
2025-07-10T11:40:55.814+07:00 ERROR 98540 --- [qtp1244881865-196] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-07-10T11:40:55.815+07:00 ERROR 98540 --- [qtp1244881865-196] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/beehph/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:94)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:138)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:96)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-07-10T11:40:55.828+07:00 ERROR 98540 --- [qtp1244881865-35] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-07-10T11:40:55.828+07:00 ERROR 98540 --- [qtp1244881865-35] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/beehph/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:94)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:138)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:96)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-07-10T11:40:57.993+07:00  INFO 98540 --- [qtp1244881865-140] n.d.m.c.a.CompanyAuthenticationService   : User minhtv logout successfully 
2025-07-10T11:41:01.145+07:00  INFO 98540 --- [qtp1244881865-140] n.d.module.session.ClientSessionManager  : Add a client session id = node0190iee6rlto4tgovaf6j4emuz0, token = c0f61fe313b9969078b8a26130ea2deb
2025-07-10T11:41:01.166+07:00  INFO 98540 --- [qtp1244881865-140] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T11:41:03.021+07:00 DEBUG 98540 --- [qtp1244881865-207] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:41:03.022+07:00 DEBUG 98540 --- [qtp1244881865-200] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T11:41:06.078+07:00  INFO 98540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 16, expire count 1
2025-07-10T11:41:06.083+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:41:06.083+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user nhat.le
2025-07-10T11:41:06.083+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:41:06.091+07:00  INFO 98540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T11:41:06.092+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:41:21.030+07:00  INFO 98540 --- [qtp1244881865-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-07-10T11:41:24.005+07:00  INFO 98540 --- [qtp1244881865-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0190iee6rlto4tgovaf6j4emuz0, token = 1693a710aef019a0a3d5c25d1b48d1d9
2025-07-10T11:41:24.021+07:00  INFO 98540 --- [qtp1244881865-67] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T11:41:25.062+07:00 DEBUG 98540 --- [qtp1244881865-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:41:25.064+07:00 DEBUG 98540 --- [qtp1244881865-140] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:41:36.169+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:41:36.171+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user louis.vnhph
2025-07-10T11:41:36.172+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:42:04.209+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:42:04.210+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user louis.vnhph
2025-07-10T11:42:04.211+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:42:04.212+07:00  INFO 98540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T11:42:25.084+07:00 DEBUG 98540 --- [qtp1244881865-200] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T11:42:32.263+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T11:42:32.264+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0190iee6rlto4tgovaf6j4emuz0, remote user louis.vnhph
2025-07-10T11:42:32.264+07:00 DEBUG 98540 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T11:42:55.545+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@31f4dfc4{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T11:42:55.546+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T11:42:55.546+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T11:42:55.546+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T11:42:55.546+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T11:42:55.547+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-10T11:42:55.547+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-10T11:42:55.547+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T11:42:55.547+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-10T11:42:55.547+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T11:42:55.547+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T11:42:55.547+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-10T11:42:55.547+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T11:42:55.547+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T11:42:55.547+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-10T11:42:55.547+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-10T11:42:55.582+07:00  INFO 98540 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T11:42:55.694+07:00  INFO 98540 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-10T11:42:55.699+07:00  INFO 98540 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-10T11:42:55.730+07:00  INFO 98540 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T11:42:55.732+07:00  INFO 98540 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T11:42:55.732+07:00  INFO 98540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-10T11:42:55.734+07:00  INFO 98540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-10T11:42:55.734+07:00  INFO 98540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-10T11:42:55.876+07:00  INFO 98540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-10T11:42:55.876+07:00  INFO 98540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-10T11:42:55.876+07:00  INFO 98540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-10T11:42:55.876+07:00  INFO 98540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-10T11:42:55.877+07:00  INFO 98540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-10T11:42:55.880+07:00  INFO 98540 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@6cc91654{STOPPING}[12.0.15,sto=0]
2025-07-10T11:42:55.886+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10T11:42:55.887+07:00  INFO 98540 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@3f19f197{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2164088700757137258/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@6757ed9e{STOPPED}}
