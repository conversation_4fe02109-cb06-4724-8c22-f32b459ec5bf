2025-07-10T15:42:04.308+07:00  INFO 36300 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 36300 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-10T15:42:04.308+07:00  INFO 36300 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-10T15:42:05.059+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.165+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 103 ms. Found 22 JPA repository interfaces.
2025-07-10T15:42:05.175+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.176+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T15:42:05.177+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.183+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-10T15:42:05.184+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.187+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T15:42:05.187+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.191+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T15:42:05.202+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.206+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-10T15:42:05.216+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.220+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-10T15:42:05.225+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.227+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T15:42:05.228+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.228+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:42:05.233+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.239+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T15:42:05.244+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.247+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T15:42:05.247+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.251+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T15:42:05.253+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.261+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-10T15:42:05.261+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.265+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-07-10T15:42:05.265+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.266+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:42:05.266+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.268+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T15:42:05.268+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.273+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-10T15:42:05.273+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.275+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T15:42:05.275+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.275+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:42:05.275+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.285+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-10T15:42:05.297+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.304+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-07-10T15:42:05.304+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.307+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T15:42:05.307+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.311+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-10T15:42:05.311+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.316+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T15:42:05.317+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.321+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T15:42:05.321+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.324+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-07-10T15:42:05.324+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.333+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-07-10T15:42:05.334+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.349+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 25 JPA repository interfaces.
2025-07-10T15:42:05.363+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.374+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-07-10T15:42:05.374+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.378+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T15:42:05.379+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.380+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T15:42:05.385+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.386+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:42:05.386+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.394+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-10T15:42:05.399+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.435+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 67 JPA repository interfaces.
2025-07-10T15:42:05.435+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.437+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T15:42:05.437+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:42:05.439+07:00  INFO 36300 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T15:42:05.668+07:00  INFO 36300 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-10T15:42:05.672+07:00  INFO 36300 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-10T15:42:05.953+07:00  WARN 36300 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-10T15:42:06.130+07:00  INFO 36300 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-10T15:42:06.132+07:00  INFO 36300 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-10T15:42:06.142+07:00  INFO 36300 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-10T15:42:06.142+07:00  INFO 36300 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1729 ms
2025-07-10T15:42:06.189+07:00  WARN 36300 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:42:06.189+07:00  INFO 36300 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-10T15:42:06.274+07:00  INFO 36300 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@62fee9c3
2025-07-10T15:42:06.275+07:00  INFO 36300 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-10T15:42:06.279+07:00  WARN 36300 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:42:06.279+07:00  INFO 36300 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-10T15:42:06.285+07:00  INFO 36300 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@7e38f55
2025-07-10T15:42:06.285+07:00  INFO 36300 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-10T15:42:06.285+07:00  WARN 36300 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:42:06.285+07:00  INFO 36300 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-10T15:42:06.701+07:00  INFO 36300 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2004f99
2025-07-10T15:42:06.701+07:00  INFO 36300 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-10T15:42:06.701+07:00  WARN 36300 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:42:06.701+07:00  INFO 36300 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-10T15:42:06.709+07:00  INFO 36300 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@3c92f2f9
2025-07-10T15:42:06.709+07:00  INFO 36300 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-10T15:42:06.709+07:00  INFO 36300 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-10T15:42:06.755+07:00  INFO 36300 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-10T15:42:06.757+07:00  INFO 36300 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@25c887ca{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17564930916794194850/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@647dcc5e{STARTED}}
2025-07-10T15:42:06.757+07:00  INFO 36300 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@25c887ca{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17564930916794194850/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@647dcc5e{STARTED}}
2025-07-10T15:42:06.758+07:00  INFO 36300 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7441eed3{STARTING}[12.0.15,sto=0] @2947ms
2025-07-10T15:42:06.809+07:00  INFO 36300 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T15:42:06.836+07:00  INFO 36300 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-10T15:42:06.850+07:00  INFO 36300 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T15:42:06.969+07:00  INFO 36300 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T15:42:06.991+07:00  WARN 36300 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T15:42:07.614+07:00  INFO 36300 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T15:42:07.622+07:00  INFO 36300 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6a355f4e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T15:42:07.650+07:00  INFO 36300 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:42:07.822+07:00  INFO 36300 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-10T15:42:07.824+07:00  INFO 36300 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-10T15:42:07.833+07:00  INFO 36300 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T15:42:07.834+07:00  INFO 36300 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T15:42:07.866+07:00  INFO 36300 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T15:42:07.870+07:00  WARN 36300 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T15:42:10.770+07:00  INFO 36300 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T15:42:10.771+07:00  INFO 36300 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@34604a4f] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T15:42:10.861+07:00  INFO 36300 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:42:10.893+07:00  INFO 36300 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-10T15:42:10.898+07:00  INFO 36300 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-10T15:42:10.898+07:00  INFO 36300 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T15:42:10.904+07:00  WARN 36300 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T15:42:11.033+07:00  INFO 36300 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10T15:42:11.475+07:00  INFO 36300 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T15:42:11.478+07:00  INFO 36300 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T15:42:11.512+07:00  INFO 36300 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-10T15:42:11.546+07:00  INFO 36300 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-10T15:42:11.589+07:00  INFO 36300 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-10T15:42:11.615+07:00  WARN 36300 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 86.3MB of free physical memory - some paging will therefore occur.
2025-07-10T15:42:11.615+07:00  INFO 36300 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T15:42:11.641+07:00  INFO 36300 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 24977224ms : this is harmless.
2025-07-10T15:42:11.648+07:00  INFO 36300 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-10T15:42:11.651+07:00  WARN 36300 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 79.7MB of free physical memory - some paging will therefore occur.
2025-07-10T15:42:11.652+07:00  INFO 36300 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T15:42:11.665+07:00  INFO 36300 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 24977211ms : this is harmless.
2025-07-10T15:42:11.666+07:00  INFO 36300 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-10T15:42:11.678+07:00  INFO 36300 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-10T15:42:11.679+07:00  INFO 36300 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-10T15:42:14.282+07:00  INFO 36300 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@15:30:00+0700 to 10/07/2025@15:45:00+0700
2025-07-10T15:42:14.283+07:00  INFO 36300 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@15:30:00+0700 to 10/07/2025@15:45:00+0700
2025-07-10T15:42:14.919+07:00  INFO 36300 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-10T15:42:14.919+07:00  INFO 36300 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T15:42:14.919+07:00  WARN 36300 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T15:42:15.188+07:00  INFO 36300 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-10T15:42:15.188+07:00  INFO 36300 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-10T15:42:15.188+07:00  INFO 36300 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-10T15:42:15.188+07:00  INFO 36300 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-10T15:42:15.188+07:00  INFO 36300 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-10T15:42:17.085+07:00  WARN 36300 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 4cdd056e-27d8-4f0e-b4a5-79ad4357286e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10T15:42:17.089+07:00  INFO 36300 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-10T15:42:17.460+07:00  INFO 36300 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-10T15:42:17.463+07:00  INFO 36300 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T15:42:17.463+07:00  INFO 36300 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T15:42:17.463+07:00  INFO 36300 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T15:42:17.523+07:00  INFO 36300 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10T15:42:17.523+07:00  INFO 36300 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-10T15:42:17.527+07:00  INFO 36300 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-07-10T15:42:17.547+07:00  INFO 36300 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@1c45d007{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T15:42:17.548+07:00  INFO 36300 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-10T15:42:17.549+07:00  INFO 36300 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-10T15:42:17.579+07:00  INFO 36300 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-10T15:42:17.580+07:00  INFO 36300 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-10T15:42:17.585+07:00  INFO 36300 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.58 seconds (process running for 13.774)
2025-07-10T15:42:18.915+07:00  INFO 36300 --- [qtp2072567128-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node02rh8jj5xjn4wt97tc910yk9h1
2025-07-10T15:42:18.915+07:00  INFO 36300 --- [qtp2072567128-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node016w1ctjnd2n28jlh4v0n9lhv30
2025-07-10T15:42:19.173+07:00  INFO 36300 --- [qtp2072567128-35] n.d.module.session.ClientSessionManager  : Add a client session id = node016w1ctjnd2n28jlh4v0n9lhv30, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T15:42:19.174+07:00  INFO 36300 --- [qtp2072567128-37] n.d.module.session.ClientSessionManager  : Add a client session id = node02rh8jj5xjn4wt97tc910yk9h1, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T15:42:19.601+07:00  INFO 36300 --- [qtp2072567128-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T15:42:19.601+07:00  INFO 36300 --- [qtp2072567128-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T15:42:21.599+07:00 DEBUG 36300 --- [qtp2072567128-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:42:21.599+07:00 DEBUG 36300 --- [qtp2072567128-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:42:26.419+07:00  INFO 36300 --- [qtp2072567128-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-07-10T15:42:29.270+07:00  INFO 36300 --- [qtp2072567128-40] n.d.module.session.ClientSessionManager  : Add a client session id = node02rh8jj5xjn4wt97tc910yk9h1, token = 6effcb979aa2929d883b6f43b3969e24
2025-07-10T15:42:29.275+07:00  INFO 36300 --- [qtp2072567128-40] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T15:42:30.305+07:00 DEBUG 36300 --- [qtp2072567128-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:42:30.305+07:00 DEBUG 36300 --- [qtp2072567128-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:43:06.586+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:43:20.660+07:00  INFO 36300 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-07-10T15:43:20.687+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:43:20.688+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:43:20.688+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:43:20.689+07:00  INFO 36300 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:43:30.314+07:00 DEBUG 36300 --- [qtp2072567128-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:43:53.744+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:43:53.745+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:43:53.745+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:44:02.769+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:44:23.803+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:44:23.804+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:44:23.805+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:44:30.315+07:00 DEBUG 36300 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:44:51.875+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:44:51.877+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:44:51.878+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:45:05.905+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:45:05.910+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T15:45:05.911+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:45:05.913+07:00  INFO 36300 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@15:45:05+0700
2025-07-10T15:45:05.928+07:00  INFO 36300 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@15:45:00+0700 to 10/07/2025@16:00:00+0700
2025-07-10T15:45:05.929+07:00  INFO 36300 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@15:45:00+0700 to 10/07/2025@16:00:00+0700
2025-07-10T15:45:11.964+07:00 DEBUG 36300 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T15:45:19.992+07:00  INFO 36300 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-10T15:45:20.002+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:45:20.002+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:45:20.002+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:45:20.002+07:00  INFO 36300 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:45:30.311+07:00 DEBUG 36300 --- [qtp2072567128-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:45:54.050+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:45:54.052+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:45:54.052+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:46:02.070+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:46:23.105+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:46:23.106+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:46:23.107+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:46:30.406+07:00 DEBUG 36300 --- [qtp2072567128-62] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:46:51.154+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:46:51.156+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:46:51.156+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:47:05.180+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:47:24.239+07:00  INFO 36300 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:47:24.251+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:47:24.252+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:47:24.252+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:47:24.252+07:00  INFO 36300 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:47:30.323+07:00 DEBUG 36300 --- [qtp2072567128-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:47:54.297+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:47:54.298+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:47:54.299+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:48:06.321+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:48:22.355+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:48:22.358+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:48:22.358+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:48:30.342+07:00 DEBUG 36300 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:48:50.402+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:48:50.404+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:48:50.404+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:49:04.428+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:49:24.473+07:00  INFO 36300 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:49:24.493+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:49:24.494+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:49:24.494+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:49:24.495+07:00  INFO 36300 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:49:30.329+07:00 DEBUG 36300 --- [qtp2072567128-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:49:53.545+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:49:53.548+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:49:53.548+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:50:06.569+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:50:06.571+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:50:21.599+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:50:21.601+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:50:21.601+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:50:30.325+07:00 DEBUG 36300 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:50:49.644+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:50:49.646+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:50:49.646+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:51:03.677+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:51:23.712+07:00  INFO 36300 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:51:23.716+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:51:23.717+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:51:23.717+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:51:23.717+07:00  INFO 36300 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:51:30.334+07:00 DEBUG 36300 --- [qtp2072567128-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:51:52.769+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:51:52.770+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:51:52.771+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:52:06.777+07:00  INFO 36300 --- [Scheduler-1145657508-1] n.d.m.session.AppHttpSessionListener     : The session node016w1ctjnd2n28jlh4v0n9lhv30 is destroyed.
2025-07-10T15:52:06.790+07:00  INFO 36300 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:52:20.819+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:52:20.820+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node02rh8jj5xjn4wt97tc910yk9h1, remote user louis.vnhph
2025-07-10T15:52:20.821+07:00 DEBUG 36300 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:52:28.491+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@1c45d007{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T15:52:28.494+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T15:52:28.494+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T15:52:28.494+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T15:52:28.494+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T15:52:28.495+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-10T15:52:28.495+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-10T15:52:28.495+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T15:52:28.495+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-10T15:52:28.495+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T15:52:28.495+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T15:52:28.495+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-10T15:52:28.495+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T15:52:28.495+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T15:52:28.495+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-10T15:52:28.495+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-10T15:52:28.510+07:00  INFO 36300 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:52:28.582+07:00  INFO 36300 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-10T15:52:28.588+07:00  INFO 36300 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-10T15:52:28.606+07:00  INFO 36300 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:52:28.609+07:00  INFO 36300 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:52:28.609+07:00  INFO 36300 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-10T15:52:28.610+07:00  INFO 36300 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-10T15:52:28.610+07:00  INFO 36300 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-10T15:52:28.748+07:00  INFO 36300 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-10T15:52:28.748+07:00  INFO 36300 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-10T15:52:28.749+07:00  INFO 36300 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-10T15:52:28.749+07:00  INFO 36300 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-10T15:52:28.749+07:00  INFO 36300 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-10T15:52:28.752+07:00  INFO 36300 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7441eed3{STOPPING}[12.0.15,sto=0]
2025-07-10T15:52:28.755+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10T15:52:28.757+07:00  INFO 36300 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@25c887ca{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17564930916794194850/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@647dcc5e{STOPPED}}
