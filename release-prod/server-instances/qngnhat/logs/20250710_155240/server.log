2025-07-10T15:52:40.626+07:00  INFO 38153 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 38153 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-10T15:52:40.626+07:00  INFO 38153 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-10T15:52:41.413+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.529+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 110 ms. Found 22 JPA repository interfaces.
2025-07-10T15:52:41.538+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.540+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T15:52:41.540+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.547+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 9 JPA repository interfaces.
2025-07-10T15:52:41.548+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.551+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T15:52:41.552+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.555+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T15:52:41.567+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.572+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-10T15:52:41.582+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.586+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-10T15:52:41.590+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.593+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T15:52:41.593+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.595+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:52:41.600+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.606+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T15:52:41.611+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.615+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T15:52:41.615+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.619+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T15:52:41.620+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.629+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-07-10T15:52:41.629+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.633+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 4 JPA repository interfaces.
2025-07-10T15:52:41.633+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.634+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:52:41.634+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.635+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-10T15:52:41.635+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.639+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-10T15:52:41.639+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.641+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T15:52:41.641+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.641+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:52:41.641+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.651+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-10T15:52:41.661+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.668+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-07-10T15:52:41.668+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.671+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T15:52:41.671+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.675+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-10T15:52:41.675+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.681+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T15:52:41.681+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.686+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T15:52:41.686+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.690+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-07-10T15:52:41.690+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.700+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-07-10T15:52:41.700+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.716+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 25 JPA repository interfaces.
2025-07-10T15:52:41.730+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.742+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-07-10T15:52:41.743+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.748+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T15:52:41.748+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.750+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T15:52:41.755+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.756+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:52:41.757+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.765+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-07-10T15:52:41.769+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.809+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 39 ms. Found 67 JPA repository interfaces.
2025-07-10T15:52:41.809+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.810+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T15:52:41.810+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:52:41.813+07:00  INFO 38153 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T15:52:42.021+07:00  INFO 38153 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-10T15:52:42.024+07:00  INFO 38153 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-10T15:52:42.304+07:00  WARN 38153 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-10T15:52:42.514+07:00  INFO 38153 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-10T15:52:42.517+07:00  INFO 38153 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-10T15:52:42.529+07:00  INFO 38153 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-10T15:52:42.530+07:00  INFO 38153 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1791 ms
2025-07-10T15:52:42.590+07:00  WARN 38153 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:52:42.591+07:00  INFO 38153 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-10T15:52:42.683+07:00  INFO 38153 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@3abe1c9e
2025-07-10T15:52:42.684+07:00  INFO 38153 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-10T15:52:42.689+07:00  WARN 38153 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:52:42.689+07:00  INFO 38153 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-10T15:52:42.694+07:00  INFO 38153 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@67253518
2025-07-10T15:52:42.694+07:00  INFO 38153 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-10T15:52:42.695+07:00  WARN 38153 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:52:42.695+07:00  INFO 38153 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-10T15:52:43.134+07:00  INFO 38153 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@39fbee74
2025-07-10T15:52:43.134+07:00  INFO 38153 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-10T15:52:43.134+07:00  WARN 38153 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:52:43.134+07:00  INFO 38153 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-10T15:52:43.143+07:00  INFO 38153 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@743a9292
2025-07-10T15:52:43.143+07:00  INFO 38153 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-10T15:52:43.143+07:00  INFO 38153 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-10T15:52:43.190+07:00  INFO 38153 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-10T15:52:43.193+07:00  INFO 38153 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@647dcc5e{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15780225169096614011/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3b6f7ab3{STARTED}}
2025-07-10T15:52:43.193+07:00  INFO 38153 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@647dcc5e{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15780225169096614011/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3b6f7ab3{STARTED}}
2025-07-10T15:52:43.194+07:00  INFO 38153 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@524250ec{STARTING}[12.0.15,sto=0] @3138ms
2025-07-10T15:52:43.246+07:00  INFO 38153 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T15:52:43.274+07:00  INFO 38153 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-10T15:52:43.291+07:00  INFO 38153 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T15:52:43.419+07:00  INFO 38153 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T15:52:43.442+07:00  WARN 38153 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T15:52:44.055+07:00  INFO 38153 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T15:52:44.063+07:00  INFO 38153 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@184db8d7] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T15:52:44.096+07:00  INFO 38153 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:52:44.274+07:00  INFO 38153 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "cloud.datatp.fforwarder.price", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-10T15:52:44.276+07:00  INFO 38153 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-10T15:52:44.284+07:00  INFO 38153 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T15:52:44.285+07:00  INFO 38153 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T15:52:44.315+07:00  INFO 38153 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T15:52:44.319+07:00  WARN 38153 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T15:52:47.181+07:00  INFO 38153 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T15:52:47.182+07:00  INFO 38153 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6798779b] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T15:52:47.284+07:00  INFO 38153 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:52:47.319+07:00  INFO 38153 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-10T15:52:47.323+07:00  INFO 38153 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-10T15:52:47.323+07:00  INFO 38153 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T15:52:47.330+07:00  WARN 38153 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T15:52:47.461+07:00  INFO 38153 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10T15:52:47.921+07:00  INFO 38153 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T15:52:47.923+07:00  INFO 38153 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T15:52:47.959+07:00  INFO 38153 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-10T15:52:47.996+07:00  INFO 38153 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-10T15:52:48.047+07:00  INFO 38153 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-10T15:52:48.082+07:00  INFO 38153 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T15:52:48.108+07:00  INFO 38153 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 25612079ms : this is harmless.
2025-07-10T15:52:48.116+07:00  INFO 38153 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-10T15:52:48.119+07:00  INFO 38153 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T15:52:48.131+07:00  INFO 38153 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 25612067ms : this is harmless.
2025-07-10T15:52:48.133+07:00  INFO 38153 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-10T15:52:48.146+07:00  INFO 38153 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-10T15:52:48.147+07:00  INFO 38153 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-10T15:52:50.870+07:00  INFO 38153 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@15:45:00+0700 to 10/07/2025@16:00:00+0700
2025-07-10T15:52:50.870+07:00  INFO 38153 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@15:45:00+0700 to 10/07/2025@16:00:00+0700
2025-07-10T15:52:51.536+07:00  INFO 38153 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-10T15:52:51.536+07:00  INFO 38153 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T15:52:51.537+07:00  WARN 38153 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T15:52:51.799+07:00  INFO 38153 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-10T15:52:51.799+07:00  INFO 38153 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-10T15:52:51.799+07:00  INFO 38153 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-10T15:52:51.799+07:00  INFO 38153 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-10T15:52:51.799+07:00  INFO 38153 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-10T15:52:53.506+07:00  WARN 38153 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 5db9eba2-0302-455c-a46b-c20339711c8e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10T15:52:53.509+07:00  INFO 38153 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-10T15:52:53.833+07:00  INFO 38153 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T15:52:53.833+07:00  INFO 38153 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-10T15:52:53.833+07:00  INFO 38153 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-10T15:52:53.833+07:00  INFO 38153 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T15:52:53.833+07:00  INFO 38153 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-10T15:52:53.833+07:00  INFO 38153 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T15:52:53.833+07:00  INFO 38153 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T15:52:53.833+07:00  INFO 38153 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-10T15:52:53.833+07:00  INFO 38153 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T15:52:53.834+07:00  INFO 38153 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T15:52:53.834+07:00  INFO 38153 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-10T15:52:53.834+07:00  INFO 38153 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-10T15:52:53.836+07:00  INFO 38153 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T15:52:53.844+07:00  INFO 38153 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T15:52:53.844+07:00  INFO 38153 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T15:52:53.933+07:00  INFO 38153 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10T15:52:53.933+07:00  INFO 38153 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-10T15:52:53.935+07:00  INFO 38153 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-10T15:52:53.942+07:00  INFO 38153 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@1a1727bd{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T15:52:53.943+07:00  INFO 38153 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-10T15:52:53.944+07:00  INFO 38153 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-10T15:52:53.971+07:00  INFO 38153 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-10T15:52:53.971+07:00  INFO 38153 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-10T15:52:53.977+07:00  INFO 38153 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.686 seconds (process running for 13.92)
2025-07-10T15:53:06.892+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:53:30.927+07:00  INFO 38153 --- [qtp483248328-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T15:53:57.008+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:53:57.026+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:53:57.026+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:53:57.026+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:54:04.039+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:54:30.076+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:54:30.077+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:54:32.678+07:00  INFO 38153 --- [qtp483248328-60] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0z2llwmvsxgwywus1igj1j0m20
2025-07-10T15:54:32.678+07:00  INFO 38153 --- [qtp483248328-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node05tbw6c5lf5271vr3hc3pbrljz1
2025-07-10T15:54:32.751+07:00  INFO 38153 --- [qtp483248328-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 6effcb979aa2929d883b6f43b3969e24
2025-07-10T15:54:32.751+07:00  INFO 38153 --- [qtp483248328-35] n.d.module.session.ClientSessionManager  : Add a client session id = node05tbw6c5lf5271vr3hc3pbrljz1, token = 6effcb979aa2929d883b6f43b3969e24
2025-07-10T15:54:32.832+07:00  INFO 38153 --- [qtp483248328-35] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T15:54:32.845+07:00  INFO 38153 --- [qtp483248328-60] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T15:54:34.699+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:54:34.721+07:00 DEBUG 38153 --- [qtp483248328-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:55:00.147+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:55:00.148+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05tbw6c5lf5271vr3hc3pbrljz1, remote user louis.vnhph
2025-07-10T15:55:00.150+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:55:06.164+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:55:06.222+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:55:28.268+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:55:28.269+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05tbw6c5lf5271vr3hc3pbrljz1, remote user louis.vnhph
2025-07-10T15:55:28.269+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:55:35.761+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:55:56.331+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-07-10T15:55:56.347+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:55:56.347+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05tbw6c5lf5271vr3hc3pbrljz1, remote user louis.vnhph
2025-07-10T15:55:56.347+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:55:56.347+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:56:03.358+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:56:30.410+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:56:30.412+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05tbw6c5lf5271vr3hc3pbrljz1, remote user louis.vnhph
2025-07-10T15:56:30.413+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:56:35.748+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:56:59.463+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:56:59.464+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05tbw6c5lf5271vr3hc3pbrljz1, remote user louis.vnhph
2025-07-10T15:56:59.464+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:57:06.473+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:57:27.509+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:57:27.510+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05tbw6c5lf5271vr3hc3pbrljz1, remote user louis.vnhph
2025-07-10T15:57:27.510+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:57:35.755+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:58:00.561+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:58:00.573+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:58:00.574+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05tbw6c5lf5271vr3hc3pbrljz1, remote user louis.vnhph
2025-07-10T15:58:00.574+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:58:00.575+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:58:02.578+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:58:30.619+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:58:30.620+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05tbw6c5lf5271vr3hc3pbrljz1, remote user louis.vnhph
2025-07-10T15:58:30.621+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:58:35.759+07:00 DEBUG 38153 --- [qtp483248328-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:58:58.668+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:58:58.668+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node05tbw6c5lf5271vr3hc3pbrljz1, remote user louis.vnhph
2025-07-10T15:58:58.668+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:58:59.365+07:00  INFO 38153 --- [qtp483248328-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 6effcb979aa2929d883b6f43b3969e24
2025-07-10T15:58:59.370+07:00  INFO 38153 --- [qtp483248328-60] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T15:58:59.415+07:00  INFO 38153 --- [qtp483248328-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 6effcb979aa2929d883b6f43b3969e24
2025-07-10T15:58:59.422+07:00  INFO 38153 --- [qtp483248328-34] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T15:59:00.436+07:00 DEBUG 38153 --- [qtp483248328-65] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:59:00.436+07:00 DEBUG 38153 --- [qtp483248328-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T15:59:05.683+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:59:26.717+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:59:26.718+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T15:59:26.718+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:00:00.417+07:00 DEBUG 38153 --- [qtp483248328-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T16:00:00.802+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-07-10T16:00:00.816+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:00:00.816+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:00:00.816+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:00:00.819+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 2, expire count 2
2025-07-10T16:00:06.829+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:00:06.830+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:00:06.831+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T16:00:06.832+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-07-10T16:00:06.833+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@16:00:06+0700
2025-07-10T16:00:06.845+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@16:00:00+0700 to 10/07/2025@16:15:00+0700
2025-07-10T16:00:06.845+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@16:00:00+0700 to 10/07/2025@16:15:00+0700
2025-07-10T16:00:07.881+07:00 DEBUG 38153 --- [botTaskExecutor-1] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T16:00:29.899+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:00:29.901+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:00:29.902+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:00:57.950+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:00:57.953+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:00:57.954+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:01:00.441+07:00 DEBUG 38153 --- [qtp483248328-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T16:01:04.964+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:01:31.005+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:01:31.007+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:01:31.008+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:02:00.113+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-07-10T16:02:00.135+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:02:00.135+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:02:00.135+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:02:00.144+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T16:02:00.717+07:00 DEBUG 38153 --- [qtp483248328-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T16:02:06.154+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:02:29.186+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:02:29.188+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:02:29.188+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:02:43.241+07:00  INFO 38153 --- [Scheduler-1046380364-1] n.d.m.session.AppHttpSessionListener     : The session node05tbw6c5lf5271vr3hc3pbrljz1 is destroyed.
2025-07-10T16:02:43.726+07:00  INFO 38153 --- [qtp483248328-60] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint TruckPriceService/searchTruckContainerTransportCharges
2025-07-10T16:02:43.726+07:00  INFO 38153 --- [qtp483248328-70] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint TruckPriceService/searchTruckContainerTransportCharges
2025-07-10T16:02:44.707+07:00  INFO 38153 --- [qtp483248328-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 6effcb979aa2929d883b6f43b3969e24
2025-07-10T16:02:44.707+07:00  INFO 38153 --- [qtp483248328-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 6effcb979aa2929d883b6f43b3969e24
2025-07-10T16:02:44.723+07:00  INFO 38153 --- [qtp483248328-78] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T16:02:44.723+07:00  INFO 38153 --- [qtp483248328-36] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T16:02:45.764+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T16:02:45.764+07:00 DEBUG 38153 --- [qtp483248328-65] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T16:02:57.237+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:02:57.238+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:02:57.239+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:03:04.251+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:03:10.195+07:00  INFO 38153 --- [qtp483248328-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 6effcb979aa2929d883b6f43b3969e24
2025-07-10T16:03:10.196+07:00  INFO 38153 --- [qtp483248328-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 6effcb979aa2929d883b6f43b3969e24
2025-07-10T16:03:10.213+07:00  INFO 38153 --- [qtp483248328-78] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T16:03:10.213+07:00  INFO 38153 --- [qtp483248328-36] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph is logged in successfully system
2025-07-10T16:03:11.255+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T16:03:11.255+07:00 DEBUG 38153 --- [qtp483248328-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T16:03:30.287+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:03:30.289+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:03:30.290+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:04:00.375+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-07-10T16:04:00.407+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:04:00.407+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:04:00.407+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:04:00.409+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 2, expire count 2
2025-07-10T16:04:06.420+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:04:11.267+07:00 DEBUG 38153 --- [qtp483248328-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T16:04:28.460+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:04:28.460+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:04:28.460+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:04:56.504+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:04:56.505+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:04:56.505+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:05:03.513+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:05:03.514+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:05:11.778+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T16:05:30.552+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:05:30.553+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:05:30.553+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:05:59.610+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T16:05:59.613+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:05:59.613+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:05:59.613+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:05:59.619+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-07-10T16:06:06.629+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:06:11.705+07:00 DEBUG 38153 --- [qtp483248328-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = louis.vnhph
2025-07-10T16:06:27.662+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:06:27.663+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user louis.vnhph
2025-07-10T16:06:27.663+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:06:57.901+07:00  INFO 38153 --- [qtp483248328-36] n.d.m.c.a.CompanyAuthenticationService   : User louis.vnhph logout successfully 
2025-07-10T16:07:00.713+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:07:00.713+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:07:02.721+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:07:04.158+07:00  INFO 38153 --- [qtp483248328-68] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = ce31c42738f25e8f37519223e425d153
2025-07-10T16:07:04.169+07:00  INFO 38153 --- [qtp483248328-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T16:07:05.462+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:07:05.462+07:00 DEBUG 38153 --- [qtp483248328-65] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:07:30.755+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:07:30.756+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:07:30.756+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:07:58.822+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-07-10T16:07:58.838+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:07:58.838+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:07:58.838+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:07:58.842+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T16:08:05.471+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:08:05.854+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:08:26.886+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:08:26.886+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:08:26.886+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:09:00.937+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:09:00.941+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:09:00.942+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:09:05.482+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:09:06.950+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:09:29.996+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:09:29.997+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:09:29.998+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:09:58.063+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 2
2025-07-10T16:09:58.068+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:09:58.068+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:09:58.069+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:09:58.069+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-10T16:10:05.081+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:10:05.083+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:10:05.475+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:10:26.123+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:10:26.124+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:10:26.124+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:11:00.169+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:11:00.170+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:11:00.170+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:11:05.460+07:00 DEBUG 38153 --- [qtp483248328-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:11:06.161+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:11:29.182+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:11:29.183+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:11:29.183+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:11:57.220+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T16:11:57.225+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:11:57.225+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:11:57.225+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:11:57.225+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:12:04.239+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:12:05.436+07:00 DEBUG 38153 --- [qtp483248328-73] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:12:30.288+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:12:30.289+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:12:30.290+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:13:00.384+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:13:00.386+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:13:00.386+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:13:05.434+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:13:06.398+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:13:28.473+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:13:28.474+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:13:28.475+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:13:56.538+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 11
2025-07-10T16:13:56.539+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:13:56.540+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:13:56.541+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:13:56.543+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:14:03.559+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:14:05.430+07:00 DEBUG 38153 --- [qtp483248328-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:14:30.598+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:14:30.599+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:14:30.600+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:14:59.652+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:14:59.654+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:14:59.654+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:15:05.466+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:15:06.669+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:15:06.670+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T16:15:06.671+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@16:15:06+0700
2025-07-10T16:15:06.698+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@16:15:00+0700 to 10/07/2025@16:30:00+0700
2025-07-10T16:15:06.698+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@16:15:00+0700 to 10/07/2025@16:30:00+0700
2025-07-10T16:15:06.698+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:15:12.708+07:00 DEBUG 38153 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T16:15:27.728+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:15:27.728+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:15:27.729+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:16:00.801+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-07-10T16:16:00.810+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:16:00.810+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:16:00.810+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:16:00.810+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:16:02.821+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:16:03.974+07:00  INFO 38153 --- [qtp483248328-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-07-10T16:16:06.300+07:00  INFO 38153 --- [qtp483248328-68] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 0e14cfff4f5510d7d046545c77bc03d6
2025-07-10T16:16:06.313+07:00  INFO 38153 --- [qtp483248328-68] n.d.m.c.a.CompanyAuthenticationService   : User mia.vnhph is logged in successfully system
2025-07-10T16:16:07.342+07:00 DEBUG 38153 --- [qtp483248328-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = mia.vnhph
2025-07-10T16:16:07.342+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = mia.vnhph
2025-07-10T16:16:30.863+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:16:30.864+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user mia.vnhph
2025-07-10T16:16:30.865+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:16:58.898+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:16:58.900+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user mia.vnhph
2025-07-10T16:16:58.900+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:17:05.912+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:17:07.348+07:00 DEBUG 38153 --- [qtp483248328-113] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = mia.vnhph
2025-07-10T16:17:24.227+07:00  INFO 38153 --- [qtp483248328-36] n.d.m.c.a.CompanyAuthenticationService   : User mia.vnhph logout successfully 
2025-07-10T16:17:25.810+07:00  INFO 38153 --- [qtp483248328-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 9345455b9d9374fa21af33c93c709e32
2025-07-10T16:17:25.833+07:00  INFO 38153 --- [qtp483248328-60] n.d.m.c.a.CompanyAuthenticationService   : User mia.vnhph is logged in successfully system
2025-07-10T16:17:26.865+07:00 DEBUG 38153 --- [qtp483248328-73] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = mia.vnhph
2025-07-10T16:17:26.867+07:00 DEBUG 38153 --- [qtp483248328-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = mia.vnhph
2025-07-10T16:17:26.944+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:17:26.944+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user mia.vnhph
2025-07-10T16:17:26.945+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:17:35.333+07:00  INFO 38153 --- [qtp483248328-60] n.d.m.c.a.CompanyAuthenticationService   : User mia.vnhph logout successfully 
2025-07-10T16:17:41.443+07:00  INFO 38153 --- [qtp483248328-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 8d4ed34117114ca771ce078e2639d8f8
2025-07-10T16:17:41.454+07:00  INFO 38153 --- [qtp483248328-67] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T16:17:43.429+07:00 DEBUG 38153 --- [qtp483248328-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:17:43.429+07:00 DEBUG 38153 --- [qtp483248328-113] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:18:01.022+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 15, expire count 6
2025-07-10T16:18:01.038+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:18:01.039+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:18:01.039+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:18:01.040+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:18:07.047+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:18:30.088+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:18:30.089+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:18:30.089+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:18:43.676+07:00 DEBUG 38153 --- [qtp483248328-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:18:45.600+07:00  INFO 38153 --- [qtp483248328-68] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 8d4ed34117114ca771ce078e2639d8f8
2025-07-10T16:18:45.619+07:00  INFO 38153 --- [qtp483248328-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T16:18:45.634+07:00  INFO 38153 --- [qtp483248328-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 8d4ed34117114ca771ce078e2639d8f8
2025-07-10T16:18:45.640+07:00  INFO 38153 --- [qtp483248328-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T16:18:47.099+07:00 DEBUG 38153 --- [qtp483248328-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:18:47.099+07:00 DEBUG 38153 --- [qtp483248328-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:18:58.136+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:18:58.136+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:18:58.136+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:19:05.146+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:19:26.262+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:19:26.265+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:19:26.270+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:19:43.706+07:00 DEBUG 38153 --- [qtp483248328-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:19:47.220+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:20:00.395+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 21, expire count 0
2025-07-10T16:20:00.403+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:20:00.404+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:20:00.404+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:20:00.404+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:20:06.415+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:20:06.416+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:20:29.454+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:20:29.461+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:20:29.462+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:20:43.691+07:00 DEBUG 38153 --- [qtp483248328-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:20:47.127+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:20:57.514+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:20:57.516+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:20:57.516+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:21:04.531+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:21:30.576+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:21:30.578+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:21:30.579+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:21:43.668+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:21:47.111+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:22:00.667+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-07-10T16:22:00.689+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:22:00.689+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:22:00.689+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:22:00.689+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:22:06.700+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:22:28.737+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:22:28.739+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:22:28.739+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:22:43.671+07:00 DEBUG 38153 --- [qtp483248328-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:22:47.113+07:00 DEBUG 38153 --- [qtp483248328-116] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:22:56.774+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:22:56.775+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:22:56.776+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:23:03.787+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:23:30.827+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:23:30.830+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:23:30.830+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:23:43.450+07:00 DEBUG 38153 --- [qtp483248328-78] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:23:47.673+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:23:59.903+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-07-10T16:23:59.918+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:23:59.918+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:23:59.919+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:23:59.919+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:24:05.062+07:00  INFO 38153 --- [qtp483248328-67] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-07-10T16:24:06.928+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:24:07.796+07:00  INFO 38153 --- [qtp483248328-115] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = 190ab85827414ecb9b5d5ed1d82573fd
2025-07-10T16:24:07.819+07:00  INFO 38153 --- [qtp483248328-115] n.d.m.c.a.CompanyAuthenticationService   : User tt1.exp.vnhph is logged in successfully system
2025-07-10T16:24:08.854+07:00 DEBUG 38153 --- [qtp483248328-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = tt1.exp.vnhph
2025-07-10T16:24:08.855+07:00 DEBUG 38153 --- [qtp483248328-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = tt1.exp.vnhph
2025-07-10T16:24:22.940+07:00  INFO 38153 --- [qtp483248328-115] n.d.m.c.a.CompanyAuthenticationService   : User tt1.exp.vnhph logout successfully 
2025-07-10T16:24:24.268+07:00  INFO 38153 --- [qtp483248328-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = d97f4619ac24da4c4b75a1bff4686479
2025-07-10T16:24:24.284+07:00  INFO 38153 --- [qtp483248328-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T16:24:25.933+07:00 DEBUG 38153 --- [qtp483248328-78] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:24:25.933+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:24:27.963+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:24:27.963+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:24:27.964+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:24:51.531+07:00 DEBUG 38153 --- [qtp483248328-78] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:24:51.535+07:00 DEBUG 38153 --- [qtp483248328-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:24:56.015+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:24:56.015+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:24:56.016+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:25:03.027+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:25:03.029+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:25:30.078+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:25:30.079+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:25:30.079+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:25:34.758+07:00  INFO 38153 --- [qtp483248328-35] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T16:25:51.607+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:25:59.198+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 8
2025-07-10T16:25:59.225+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:25:59.225+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:25:59.226+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:25:59.226+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:26:06.250+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:26:27.295+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:26:27.297+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:26:27.299+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:26:37.717+07:00  INFO 38153 --- [qtp483248328-68] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = d97f4619ac24da4c4b75a1bff4686479
2025-07-10T16:26:37.717+07:00  INFO 38153 --- [qtp483248328-115] n.d.module.session.ClientSessionManager  : Add a client session id = node0z2llwmvsxgwywus1igj1j0m20, token = d97f4619ac24da4c4b75a1bff4686479
2025-07-10T16:26:37.752+07:00  INFO 38153 --- [qtp483248328-115] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T16:26:37.752+07:00  INFO 38153 --- [qtp483248328-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T16:26:40.520+07:00 DEBUG 38153 --- [qtp483248328-75] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:26:40.520+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:26:51.564+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:27:00.352+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:27:00.355+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:27:00.355+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:27:02.366+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:27:30.417+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:27:30.419+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:27:30.419+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:27:40.706+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:27:51.562+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:27:58.511+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 23, expire count 16
2025-07-10T16:27:58.516+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:27:58.516+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:27:58.516+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:27:58.530+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T16:28:05.543+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:28:26.567+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:28:26.569+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:28:26.569+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:28:40.703+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:28:51.570+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:29:00.618+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:29:00.619+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:29:00.619+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:29:06.624+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:29:29.664+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:29:29.665+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:29:29.665+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:29:40.720+07:00 DEBUG 38153 --- [qtp483248328-116] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:29:51.563+07:00 DEBUG 38153 --- [qtp483248328-75] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:29:57.716+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:29:57.719+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:29:57.720+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:29:57.720+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:29:57.720+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:30:04.731+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:30:04.733+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:30:04.734+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T16:30:04.735+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@16:30:04+0700
2025-07-10T16:30:04.762+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@16:30:00+0700 to 10/07/2025@16:45:00+0700
2025-07-10T16:30:04.763+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@16:30:00+0700 to 10/07/2025@16:45:00+0700
2025-07-10T16:30:10.770+07:00 DEBUG 38153 --- [botTaskExecutor-3] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T16:30:30.801+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:30:30.808+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:30:30.808+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:30:40.704+07:00 DEBUG 38153 --- [qtp483248328-116] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:30:51.564+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:31:00.855+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:31:00.856+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:31:00.857+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:31:06.864+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:31:28.894+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:31:28.895+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:31:28.895+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:31:40.698+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:31:51.564+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:31:56.953+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 3
2025-07-10T16:31:56.964+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:31:56.964+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:31:56.964+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:31:56.965+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-10T16:32:03.978+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:32:30.021+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:32:30.022+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:32:30.022+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:32:51.568+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:33:00.073+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:33:00.075+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:33:00.075+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:33:06.081+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:33:28.118+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:33:28.120+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:33:28.120+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:33:34.700+07:00 DEBUG 38153 --- [qtp483248328-116] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:33:51.566+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:33:56.160+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 5
2025-07-10T16:33:56.161+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:33:56.162+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:33:56.162+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:33:56.162+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:34:03.171+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:34:30.214+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:34:30.216+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:34:30.216+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:34:34.704+07:00 DEBUG 38153 --- [qtp483248328-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:34:51.570+07:00 DEBUG 38153 --- [qtp483248328-75] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:34:59.266+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:34:59.267+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:34:59.267+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:35:06.273+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:35:06.275+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:35:27.315+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:35:27.317+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:35:27.318+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:35:34.709+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:35:51.563+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:36:00.369+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 22
2025-07-10T16:36:00.390+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:36:00.390+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:36:00.390+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:36:00.390+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:36:02.399+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:36:30.443+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:36:30.444+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:36:30.444+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:36:34.697+07:00 DEBUG 38153 --- [qtp483248328-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:36:51.703+07:00 DEBUG 38153 --- [qtp483248328-75] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:36:58.493+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:36:58.493+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:36:58.493+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:37:05.508+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:37:26.540+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:37:26.541+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:37:26.541+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:37:34.807+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:38:00.621+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 2
2025-07-10T16:38:00.635+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:38:00.636+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:38:00.637+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:38:00.638+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:38:06.649+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:38:29.691+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:38:29.692+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:38:29.693+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:38:34.718+07:00 DEBUG 38153 --- [qtp483248328-75] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:38:34.782+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:38:57.729+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:38:57.730+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:38:57.730+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:39:04.737+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:39:30.781+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:39:30.784+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:39:30.784+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:39:34.710+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:39:34.771+07:00 DEBUG 38153 --- [qtp483248328-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:40:00.861+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-07-10T16:40:00.867+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:40:00.867+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:40:00.867+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:40:00.867+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:40:06.876+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:40:06.877+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:40:28.913+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:40:28.914+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:40:28.914+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:40:34.701+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:40:34.721+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:40:56.950+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:40:56.952+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:40:56.952+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:41:03.959+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:41:31.003+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:41:31.005+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:41:31.005+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:41:34.768+07:00 DEBUG 38153 --- [qtp483248328-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:41:34.811+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:42:00.052+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 3
2025-07-10T16:42:00.056+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:42:00.057+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:42:00.057+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:42:00.058+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:42:06.067+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:42:28.106+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:42:28.107+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:42:28.108+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:42:34.706+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:42:34.719+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:42:56.155+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:42:56.156+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:42:56.156+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:43:03.166+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:43:30.202+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:43:30.203+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:43:30.203+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:43:34.720+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:43:34.754+07:00 DEBUG 38153 --- [qtp483248328-144] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:43:59.254+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T16:43:59.260+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:43:59.260+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:43:59.260+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:43:59.261+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:44:06.275+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:44:27.310+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:44:27.311+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:44:27.311+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:44:34.716+07:00 DEBUG 38153 --- [qtp483248328-144] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:44:34.720+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:45:00.365+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:45:00.366+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:45:00.366+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:45:02.375+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:45:02.375+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:45:02.376+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T16:45:02.376+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@16:45:02+0700
2025-07-10T16:45:02.390+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@16:45:00+0700 to 10/07/2025@17:00:00+0700
2025-07-10T16:45:02.391+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@16:45:00+0700 to 10/07/2025@17:00:00+0700
2025-07-10T16:45:08.404+07:00 DEBUG 38153 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T16:45:30.438+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:45:30.439+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:45:30.439+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:45:34.725+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:45:34.748+07:00 DEBUG 38153 --- [qtp483248328-148] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:45:58.480+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-07-10T16:45:58.485+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:45:58.485+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:45:58.485+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:45:58.486+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:46:05.496+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:46:26.519+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:46:26.521+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:46:26.521+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:46:34.805+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:46:34.850+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:47:00.575+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:47:00.577+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:47:00.577+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:47:06.584+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:47:29.628+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:47:29.629+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:47:29.629+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:47:34.804+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:47:34.804+07:00 DEBUG 38153 --- [qtp483248328-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:47:57.686+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 5
2025-07-10T16:47:57.692+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:47:57.693+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:47:57.693+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:47:57.693+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:48:04.701+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:48:30.753+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:48:30.754+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:48:30.754+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:48:34.711+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:48:34.711+07:00 DEBUG 38153 --- [qtp483248328-148] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:49:00.798+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:49:00.799+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:49:00.800+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:49:06.809+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:49:28.839+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:49:28.839+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:49:28.839+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:49:34.716+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:49:34.718+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:49:56.887+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:49:56.889+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:49:56.889+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:49:56.889+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:49:56.889+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:50:03.896+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:50:03.896+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:50:30.938+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:50:30.939+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:50:30.939+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:50:34.718+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:50:34.718+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:50:59.995+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:50:59.996+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:50:59.997+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:51:07.011+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:51:28.035+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:51:28.036+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:51:28.036+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:51:34.719+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:51:34.731+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:51:56.089+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T16:51:56.094+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:51:56.094+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:51:56.094+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:51:56.094+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:52:03.107+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:52:30.143+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:52:30.143+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:52:30.143+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:52:34.715+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:52:34.715+07:00 DEBUG 38153 --- [qtp483248328-115] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:52:59.184+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:52:59.186+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:52:59.186+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:53:06.194+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:53:27.232+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:53:27.233+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:53:27.233+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:53:34.716+07:00 DEBUG 38153 --- [qtp483248328-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:53:34.727+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:54:00.280+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T16:54:00.284+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:54:00.285+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:54:00.285+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:54:00.285+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:54:02.293+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:54:30.331+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:54:30.332+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:54:30.333+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:54:34.724+07:00 DEBUG 38153 --- [qtp483248328-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:54:34.738+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:54:58.370+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:54:58.371+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:54:58.371+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:55:05.381+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:55:05.382+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T16:55:26.416+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:55:26.416+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:55:26.417+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:55:34.718+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:55:34.720+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:56:00.487+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T16:56:00.494+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:56:00.494+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:56:00.494+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:56:00.495+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:56:06.505+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:56:29.532+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:56:29.533+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:56:29.533+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:56:34.715+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:56:34.750+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:56:57.579+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:56:57.579+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:56:57.579+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:57:04.589+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:57:30.628+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:57:30.629+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:57:30.629+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:57:34.722+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:57:34.724+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:58:00.680+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T16:58:00.688+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:58:00.688+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:58:00.688+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:58:00.689+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:58:06.694+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:58:28.727+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:58:28.728+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:58:28.729+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:58:34.755+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:58:34.789+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:58:56.772+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:58:56.773+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:58:56.773+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:59:03.785+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T16:59:30.820+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:59:30.821+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:59:30.821+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:59:34.714+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:59:34.714+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T16:59:59.874+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T16:59:59.878+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T16:59:59.879+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T16:59:59.879+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T16:59:59.879+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:00:06.893+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:00:06.894+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T17:00:06.894+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T17:00:06.895+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@17:00:06+0700
2025-07-10T17:00:06.912+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@17:00:00+0700 to 10/07/2025@17:15:00+0700
2025-07-10T17:00:06.913+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@17:00:00+0700 to 10/07/2025@17:15:00+0700
2025-07-10T17:00:06.913+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-07-10T17:00:12.922+07:00 DEBUG 38153 --- [botTaskExecutor-3] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T17:00:27.944+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:00:27.945+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:00:27.945+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:00:34.725+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:00:34.793+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:01:01.005+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:01:01.006+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:01:01.006+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:01:03.013+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:01:30.047+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:01:30.049+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:01:30.050+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:01:34.733+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:01:34.797+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:01:59.115+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T17:01:59.125+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:01:59.126+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:01:59.126+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:01:59.127+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:02:06.138+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:02:27.160+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:02:27.162+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:02:27.162+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:02:34.779+07:00 DEBUG 38153 --- [qtp483248328-202] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:02:34.813+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:03:00.215+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:03:00.215+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:03:00.216+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:03:02.221+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:03:30.265+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:03:30.267+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:03:30.267+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:03:34.728+07:00 DEBUG 38153 --- [qtp483248328-144] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:03:34.744+07:00 DEBUG 38153 --- [qtp483248328-202] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:03:58.311+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:03:58.320+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:03:58.320+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:03:58.320+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:03:58.320+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:04:05.332+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:04:26.359+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:04:26.360+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:04:26.360+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:04:34.726+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:04:34.726+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:05:00.408+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:05:00.409+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:05:00.409+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:05:06.415+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:05:06.416+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T17:05:29.453+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:05:29.454+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:05:29.454+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:05:34.733+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:05:34.748+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:05:57.501+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T17:05:57.505+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:05:57.506+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:05:57.506+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:05:57.506+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:06:04.514+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:06:30.551+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:06:30.552+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:06:30.552+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:06:34.725+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:06:34.728+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:07:00.604+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:07:00.605+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:07:00.605+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:07:06.607+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:07:28.642+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:07:28.643+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:07:28.643+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:07:34.733+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:07:34.770+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:07:47.772+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:07:56.688+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:07:56.693+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:07:56.693+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:07:56.693+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:07:56.694+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:08:03.709+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:08:30.746+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:08:30.747+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:08:30.747+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:08:34.734+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:08:40.742+07:00 DEBUG 38153 --- [qtp483248328-144] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:08:59.800+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:08:59.801+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:08:59.802+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:09:06.812+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:09:27.839+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:09:27.840+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:09:27.841+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:09:34.731+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:10:00.898+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T17:10:00.910+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:10:00.910+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:10:00.910+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:10:00.910+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:10:02.920+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:10:02.920+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T17:10:30.959+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:10:30.960+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:10:30.960+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:10:32.987+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:10:34.723+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:10:51.732+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:10:59.003+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:10:59.003+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:10:59.003+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:11:06.020+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:11:27.054+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:11:27.055+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:11:27.055+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:11:34.811+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:12:00.114+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T17:12:00.117+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:12:00.117+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:12:00.117+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:12:00.117+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:12:02.123+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:12:30.168+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:12:30.169+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:12:30.170+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:12:34.727+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:12:34.794+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:12:58.214+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:12:58.216+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:12:58.217+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:13:05.228+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:13:26.259+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:13:26.260+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:13:26.260+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:13:34.736+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:13:34.773+07:00 DEBUG 38153 --- [qtp483248328-266] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:14:00.321+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T17:14:00.328+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:14:00.329+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:14:00.329+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:14:00.329+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:14:06.341+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:14:29.370+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:14:29.370+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:14:29.370+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:14:34.730+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:14:34.742+07:00 DEBUG 38153 --- [qtp483248328-144] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:14:57.414+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:14:57.416+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:14:57.416+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:15:04.426+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:15:04.428+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T17:15:04.428+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T17:15:04.429+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@17:15:04+0700
2025-07-10T17:15:04.442+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@17:15:00+0700 to 10/07/2025@17:30:00+0700
2025-07-10T17:15:04.442+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@17:15:00+0700 to 10/07/2025@17:30:00+0700
2025-07-10T17:15:10.450+07:00 DEBUG 38153 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T17:15:30.479+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:15:30.480+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:15:30.480+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:15:34.739+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:15:34.739+07:00 DEBUG 38153 --- [qtp483248328-144] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:16:00.545+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T17:16:00.554+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:16:00.555+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:16:00.555+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:16:00.556+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:16:06.566+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:16:28.593+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:16:28.593+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:16:28.593+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:16:34.741+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:16:34.797+07:00 DEBUG 38153 --- [qtp483248328-144] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:16:56.626+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:16:56.627+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:16:56.628+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:17:03.641+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:17:30.697+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:17:30.697+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:17:30.697+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:17:34.733+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:17:34.749+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:17:59.748+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:17:59.750+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:17:59.751+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:17:59.751+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:17:59.751+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:18:06.763+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:18:27.804+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:18:27.806+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:18:27.806+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:18:34.772+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:18:34.772+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:19:00.844+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:19:00.846+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:19:00.846+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:19:02.852+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:19:30.902+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:19:30.904+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:19:30.904+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:19:34.838+07:00 DEBUG 38153 --- [qtp483248328-238] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:19:34.838+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:19:58.973+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T17:19:58.993+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:19:58.993+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:19:58.993+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:19:58.993+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:20:06.006+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:20:06.008+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T17:20:27.038+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:20:27.040+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:20:27.040+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:20:34.728+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:20:34.778+07:00 DEBUG 38153 --- [qtp483248328-298] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:21:00.057+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:21:00.059+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:21:00.059+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:21:02.065+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:21:30.107+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:21:30.108+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:21:30.108+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:21:34.731+07:00 DEBUG 38153 --- [qtp483248328-144] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:21:34.740+07:00 DEBUG 38153 --- [qtp483248328-298] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:21:58.157+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-10T17:21:58.162+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:21:58.163+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:21:58.163+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:21:58.164+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:22:05.172+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:22:26.207+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:22:26.209+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:22:26.209+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:22:34.783+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:22:34.788+07:00 DEBUG 38153 --- [qtp483248328-144] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:23:00.248+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:23:00.249+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:23:00.249+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:23:06.260+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:23:29.298+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:23:29.300+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:23:29.300+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:23:34.711+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:23:34.714+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:23:57.355+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T17:23:57.365+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:23:57.365+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:23:57.365+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:23:57.366+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:24:04.381+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:24:30.413+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:24:30.415+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:24:30.415+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:24:34.740+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:24:34.801+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:25:00.465+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:25:00.465+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:25:00.466+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:25:06.472+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T17:25:06.473+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:25:28.509+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:25:28.510+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:25:28.510+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:25:34.725+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:25:34.802+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:25:56.550+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-10T17:25:56.552+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:25:56.553+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:25:56.553+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:25:56.553+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:26:03.565+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:26:30.593+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:26:30.594+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:26:30.594+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:26:34.716+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:26:34.750+07:00 DEBUG 38153 --- [qtp483248328-275] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:26:59.641+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:26:59.642+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:26:59.642+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:27:06.655+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:27:27.690+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:27:27.690+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:27:27.691+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:27:34.730+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:27:34.730+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:28:00.787+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T17:28:00.798+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:28:00.798+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:28:00.798+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:28:00.799+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:28:02.809+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:28:30.851+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:28:30.853+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:28:30.853+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:28:34.814+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:28:34.814+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:28:58.896+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:28:58.897+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:28:58.897+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:29:05.904+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:29:26.941+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:29:26.942+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:29:26.943+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:29:34.809+07:00 DEBUG 38153 --- [qtp483248328-350] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:29:34.817+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:30:01.010+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T17:30:01.038+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:30:01.038+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:30:01.038+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:30:01.038+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:30:02.042+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T17:30:02.044+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@17:30:02+0700
2025-07-10T17:30:02.056+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@17:30:00+0700 to 10/07/2025@17:45:00+0700
2025-07-10T17:30:02.056+07:00  INFO 38153 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@17:30:00+0700 to 10/07/2025@17:45:00+0700
2025-07-10T17:30:07.062+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:30:07.062+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T17:30:08.070+07:00 DEBUG 38153 --- [botTaskExecutor-3] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T17:30:30.099+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:30:30.100+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:30:30.101+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:30:34.751+07:00 DEBUG 38153 --- [qtp483248328-343] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:30:34.795+07:00 DEBUG 38153 --- [qtp483248328-298] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:30:58.134+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:30:58.135+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:30:58.135+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:31:05.146+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:31:26.174+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:31:26.175+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:31:26.175+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:31:34.747+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:31:34.747+07:00 DEBUG 38153 --- [qtp483248328-343] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:32:00.247+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T17:32:00.255+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:32:00.255+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:32:00.256+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:32:00.256+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:32:06.261+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:32:29.300+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:32:29.302+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:32:29.305+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:32:34.726+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:32:34.726+07:00 DEBUG 38153 --- [qtp483248328-343] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:32:57.343+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:32:57.344+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:32:57.344+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:33:04.357+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:33:30.395+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:33:30.396+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:33:30.396+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:33:34.817+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:33:34.827+07:00 DEBUG 38153 --- [qtp483248328-343] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:34:00.456+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T17:34:00.461+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:34:00.461+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:34:00.461+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:34:00.462+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:34:06.471+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:34:28.511+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:34:28.512+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:34:28.512+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:34:34.736+07:00 DEBUG 38153 --- [qtp483248328-298] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:34:34.840+07:00 DEBUG 38153 --- [qtp483248328-349] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:34:56.549+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:34:56.550+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:34:56.550+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:35:03.558+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:35:03.559+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T17:35:30.599+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:35:30.599+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:35:30.600+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:35:34.807+07:00 DEBUG 38153 --- [qtp483248328-298] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:35:34.807+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:35:59.636+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:35:59.639+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:35:59.640+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:35:59.640+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:35:59.641+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:36:06.647+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:36:27.678+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:36:27.679+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:36:27.679+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:36:34.706+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:36:34.798+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:37:00.735+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:37:00.736+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:37:00.736+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:37:02.745+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:37:30.786+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:37:30.788+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:37:30.788+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:37:34.714+07:00 DEBUG 38153 --- [qtp483248328-231] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:37:34.794+07:00 DEBUG 38153 --- [qtp483248328-298] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:37:58.855+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T17:37:58.863+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:37:58.863+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:37:58.864+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:37:58.864+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:38:05.877+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:38:26.916+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:38:26.919+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:38:26.920+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:38:34.751+07:00 DEBUG 38153 --- [qtp483248328-298] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:38:34.751+07:00 DEBUG 38153 --- [qtp483248328-123] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:39:00.998+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:39:01.007+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:39:01.008+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:39:07.014+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:39:30.061+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:39:30.063+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:39:30.063+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:39:34.824+07:00 DEBUG 38153 --- [qtp483248328-298] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:39:34.824+07:00 DEBUG 38153 --- [qtp483248328-179] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:39:58.103+07:00  INFO 38153 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:39:58.107+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:39:58.107+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:39:58.108+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:39:58.108+07:00  INFO 38153 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:40:05.115+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T17:40:05.116+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:40:26.145+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:40:26.146+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:40:26.146+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:40:34.714+07:00 DEBUG 38153 --- [qtp483248328-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:40:34.831+07:00 DEBUG 38153 --- [qtp483248328-387] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:41:00.200+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:41:00.201+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:41:00.201+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:41:06.209+07:00  INFO 38153 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T17:41:29.248+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T17:41:29.248+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0z2llwmvsxgwywus1igj1j0m20, remote user nhat.le
2025-07-10T17:41:29.248+07:00 DEBUG 38153 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T17:41:34.715+07:00 DEBUG 38153 --- [qtp483248328-298] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:41:34.765+07:00 DEBUG 38153 --- [qtp483248328-386] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T17:41:49.873+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@1a1727bd{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T17:41:49.882+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T17:41:49.882+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T17:41:49.883+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T17:41:49.886+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T17:41:49.887+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-10T17:41:49.887+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-10T17:41:49.887+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T17:41:49.887+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-10T17:41:49.887+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T17:41:49.887+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T17:41:49.887+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-10T17:41:49.887+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T17:41:49.887+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T17:41:49.887+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-10T17:41:49.887+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-10T17:41:49.905+07:00  INFO 38153 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T17:41:50.038+07:00  INFO 38153 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-10T17:41:50.043+07:00  INFO 38153 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-10T17:41:50.084+07:00  INFO 38153 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T17:41:50.091+07:00  INFO 38153 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T17:41:50.092+07:00  INFO 38153 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-10T17:41:50.094+07:00  INFO 38153 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-10T17:41:50.094+07:00  INFO 38153 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-10T17:41:50.228+07:00  INFO 38153 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-10T17:41:50.228+07:00  INFO 38153 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-10T17:41:50.229+07:00  INFO 38153 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-10T17:41:50.229+07:00  INFO 38153 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-10T17:41:50.229+07:00  INFO 38153 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-10T17:41:50.231+07:00  INFO 38153 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@524250ec{STOPPING}[12.0.15,sto=0]
2025-07-10T17:41:50.236+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10T17:41:50.238+07:00  INFO 38153 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@647dcc5e{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15780225169096614011/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3b6f7ab3{STOPPED}}
