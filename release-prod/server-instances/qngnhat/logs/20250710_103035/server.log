2025-07-10T10:30:35.913+07:00  INFO 97609 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 97609 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-10T10:30:35.914+07:00  INFO 97609 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-10T10:30:36.642+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.739+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 93 ms. Found 22 JPA repository interfaces.
2025-07-10T10:30:36.748+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.749+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T10:30:36.749+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.756+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-10T10:30:36.756+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.759+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T10:30:36.760+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.763+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T10:30:36.774+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.778+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-10T10:30:36.788+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.793+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-10T10:30:36.797+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.800+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T10:30:36.800+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.801+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:30:36.806+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.812+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T10:30:36.816+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.819+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T10:30:36.819+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.823+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T10:30:36.824+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.832+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-10T10:30:36.832+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.836+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-07-10T10:30:36.836+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.837+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:30:36.837+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.838+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-10T10:30:36.838+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.843+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-10T10:30:36.843+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.844+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T10:30:36.844+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.845+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:30:36.845+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.855+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-10T10:30:36.865+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.871+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-07-10T10:30:36.872+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.874+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T10:30:36.875+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.878+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-10T10:30:36.879+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.884+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T10:30:36.884+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.889+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T10:30:36.889+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.892+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T10:30:36.892+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.902+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-07-10T10:30:36.902+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.916+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 25 JPA repository interfaces.
2025-07-10T10:30:36.930+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.941+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-07-10T10:30:36.942+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.945+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T10:30:36.946+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.947+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T10:30:36.952+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.953+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:30:36.953+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:36.960+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-10T10:30:36.964+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:37.000+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 67 JPA repository interfaces.
2025-07-10T10:30:37.000+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:37.001+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T10:30:37.001+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:30:37.004+07:00  INFO 97609 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T10:30:37.244+07:00  INFO 97609 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-10T10:30:37.248+07:00  INFO 97609 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-10T10:30:37.512+07:00  WARN 97609 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-10T10:30:37.705+07:00  INFO 97609 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-10T10:30:37.707+07:00  INFO 97609 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-10T10:30:37.718+07:00  INFO 97609 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-10T10:30:37.718+07:00  INFO 97609 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1697 ms
2025-07-10T10:30:37.766+07:00  WARN 97609 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:30:37.767+07:00  INFO 97609 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-10T10:30:37.871+07:00  INFO 97609 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@8a0447e
2025-07-10T10:30:37.871+07:00  INFO 97609 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-10T10:30:37.877+07:00  WARN 97609 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:30:37.877+07:00  INFO 97609 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-10T10:30:37.882+07:00  INFO 97609 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@e6d9c25
2025-07-10T10:30:37.883+07:00  INFO 97609 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-10T10:30:37.883+07:00  WARN 97609 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:30:37.883+07:00  INFO 97609 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-10T10:30:38.307+07:00  INFO 97609 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@60b721c9
2025-07-10T10:30:38.307+07:00  INFO 97609 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-10T10:30:38.307+07:00  WARN 97609 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:30:38.307+07:00  INFO 97609 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-10T10:30:38.317+07:00  INFO 97609 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@2004f99
2025-07-10T10:30:38.317+07:00  INFO 97609 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-10T10:30:38.318+07:00  INFO 97609 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-10T10:30:38.369+07:00  INFO 97609 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-10T10:30:38.371+07:00  INFO 97609 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@38dde7ec{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7933980433253864684/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3f19f197{STARTED}}
2025-07-10T10:30:38.372+07:00  INFO 97609 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@38dde7ec{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7933980433253864684/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3f19f197{STARTED}}
2025-07-10T10:30:38.373+07:00  INFO 97609 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@1320d929{STARTING}[12.0.15,sto=0] @2979ms
2025-07-10T10:30:38.429+07:00  INFO 97609 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T10:30:38.457+07:00  INFO 97609 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-10T10:30:38.473+07:00  INFO 97609 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T10:30:38.594+07:00  INFO 97609 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T10:30:38.618+07:00  WARN 97609 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T10:30:39.221+07:00  INFO 97609 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T10:30:39.274+07:00  INFO 97609 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@69a47ed3] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T10:30:39.302+07:00  INFO 97609 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T10:30:39.480+07:00  INFO 97609 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-10T10:30:39.483+07:00  INFO 97609 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-10T10:30:39.490+07:00  INFO 97609 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T10:30:39.492+07:00  INFO 97609 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T10:30:39.523+07:00  INFO 97609 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T10:30:39.527+07:00  WARN 97609 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T10:30:42.358+07:00  INFO 97609 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T10:30:42.359+07:00  INFO 97609 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6aa4bed9] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T10:30:42.442+07:00  INFO 97609 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T10:30:42.472+07:00  INFO 97609 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-10T10:30:42.476+07:00  INFO 97609 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-10T10:30:42.477+07:00  INFO 97609 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T10:30:42.482+07:00  WARN 97609 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T10:30:42.610+07:00  INFO 97609 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10T10:30:43.061+07:00  INFO 97609 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T10:30:43.064+07:00  INFO 97609 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T10:30:43.098+07:00  INFO 97609 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-10T10:30:43.136+07:00  INFO 97609 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-10T10:30:43.179+07:00  INFO 97609 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-10T10:30:43.207+07:00  WARN 97609 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 97.4MB of free physical memory - some paging will therefore occur.
2025-07-10T10:30:43.207+07:00  INFO 97609 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T10:30:43.228+07:00  INFO 97609 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 6287615ms : this is harmless.
2025-07-10T10:30:43.236+07:00  INFO 97609 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-10T10:30:43.239+07:00  WARN 97609 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 88.6MB of free physical memory - some paging will therefore occur.
2025-07-10T10:30:43.239+07:00  INFO 97609 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T10:30:43.254+07:00  INFO 97609 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 6287600ms : this is harmless.
2025-07-10T10:30:43.255+07:00  INFO 97609 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-10T10:30:43.267+07:00  INFO 97609 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-10T10:30:43.268+07:00  INFO 97609 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-10T10:30:46.000+07:00  INFO 97609 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@10:30:00+0700 to 10/07/2025@10:45:00+0700
2025-07-10T10:30:46.001+07:00  INFO 97609 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@10:30:00+0700 to 10/07/2025@10:45:00+0700
2025-07-10T10:30:46.730+07:00  INFO 97609 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-10T10:30:46.730+07:00  INFO 97609 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T10:30:46.731+07:00  WARN 97609 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T10:30:47.149+07:00  INFO 97609 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-10T10:30:47.149+07:00  INFO 97609 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-10T10:30:47.149+07:00  INFO 97609 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-10T10:30:47.149+07:00  INFO 97609 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-10T10:30:47.149+07:00  INFO 97609 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-10T10:30:48.600+07:00  WARN 97609 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: c6f3f55f-1072-492e-ba32-2943227b61b3

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10T10:30:48.604+07:00  INFO 97609 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-10T10:30:48.912+07:00  INFO 97609 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-10T10:30:48.915+07:00  INFO 97609 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T10:30:48.915+07:00  INFO 97609 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T10:30:48.915+07:00  INFO 97609 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T10:30:48.966+07:00  INFO 97609 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10T10:30:48.966+07:00  INFO 97609 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-10T10:30:48.967+07:00  INFO 97609 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-10T10:30:48.975+07:00  INFO 97609 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@64416bad{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T10:30:48.976+07:00  INFO 97609 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-10T10:30:48.977+07:00  INFO 97609 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-10T10:30:49.008+07:00  INFO 97609 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-10T10:30:49.008+07:00  INFO 97609 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-10T10:30:49.014+07:00  INFO 97609 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.398 seconds (process running for 13.617)
2025-07-10T10:30:55.940+07:00  INFO 97609 --- [qtp1272093185-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T10:30:57.363+07:00  INFO 97609 --- [qtp1272093185-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0o1xfohoi817kw4nahczt3vuz0
2025-07-10T10:30:57.363+07:00  INFO 97609 --- [qtp1272093185-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node09jkb46dp8sz0g64m4w95ykbj1
2025-07-10T10:30:57.432+07:00  INFO 97609 --- [qtp1272093185-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0o1xfohoi817kw4nahczt3vuz0, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:30:57.432+07:00  INFO 97609 --- [qtp1272093185-35] n.d.module.session.ClientSessionManager  : Add a client session id = node09jkb46dp8sz0g64m4w95ykbj1, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:30:57.862+07:00  INFO 97609 --- [qtp1272093185-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:30:57.862+07:00  INFO 97609 --- [qtp1272093185-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:30:59.284+07:00 DEBUG 97609 --- [qtp1272093185-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:30:59.318+07:00 DEBUG 97609 --- [qtp1272093185-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:31:02.950+07:00  INFO 97609 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:31:52.112+07:00  INFO 97609 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T10:31:52.142+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:31:52.142+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:31:52.143+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:31:52.143+07:00  INFO 97609 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:31:59.307+07:00 DEBUG 97609 --- [qtp1272093185-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:32:06.169+07:00  INFO 97609 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:32:25.201+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:32:25.204+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:32:25.204+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:32:55.260+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:32:55.263+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:32:55.265+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:32:59.328+07:00 DEBUG 97609 --- [qtp1272093185-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:33:02.282+07:00  INFO 97609 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:33:23.326+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:33:23.327+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:33:23.327+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:33:51.396+07:00  INFO 97609 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-07-10T10:33:51.419+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:33:51.420+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:33:51.420+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:33:51.428+07:00  INFO 97609 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T10:33:59.310+07:00 DEBUG 97609 --- [qtp1272093185-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:34:05.453+07:00  INFO 97609 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:34:25.478+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:34:25.479+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:34:25.480+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:34:54.522+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:34:54.523+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:34:54.524+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:34:59.295+07:00 DEBUG 97609 --- [qtp1272093185-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:35:06.534+07:00  INFO 97609 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:35:06.537+07:00  INFO 97609 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T10:35:22.568+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:35:22.570+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:35:22.570+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:35:55.645+07:00  INFO 97609 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T10:35:55.665+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:35:55.665+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:35:55.665+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:35:55.666+07:00  INFO 97609 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:35:59.336+07:00 DEBUG 97609 --- [qtp1272093185-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:36:04.683+07:00  INFO 97609 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:36:25.726+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:36:25.727+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:36:25.728+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:36:53.774+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:36:53.775+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:36:53.775+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:36:59.344+07:00 DEBUG 97609 --- [qtp1272093185-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:37:06.797+07:00  INFO 97609 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:37:21.828+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:37:21.830+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:37:21.830+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:37:55.884+07:00  INFO 97609 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T10:37:55.890+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:37:55.891+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:37:55.891+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:37:55.893+07:00  INFO 97609 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:37:59.296+07:00 DEBUG 97609 --- [qtp1272093185-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:38:03.915+07:00  INFO 97609 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:38:24.954+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:38:24.955+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:38:24.955+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:38:52.993+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:38:52.995+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:38:52.995+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:38:59.365+07:00 DEBUG 97609 --- [qtp1272093185-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:39:06.017+07:00  INFO 97609 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:39:21.047+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:39:21.048+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node09jkb46dp8sz0g64m4w95ykbj1, remote user nhat.le
2025-07-10T10:39:21.048+07:00 DEBUG 97609 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:39:34.369+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@64416bad{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T10:39:34.370+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-10T10:39:34.371+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-10T10:39:34.386+07:00  INFO 97609 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:39:34.444+07:00  INFO 97609 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-10T10:39:34.449+07:00  INFO 97609 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-10T10:39:34.475+07:00  INFO 97609 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T10:39:34.477+07:00  INFO 97609 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T10:39:34.477+07:00  INFO 97609 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-10T10:39:34.478+07:00  INFO 97609 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-10T10:39:34.478+07:00  INFO 97609 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-10T10:39:34.612+07:00  INFO 97609 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-10T10:39:34.613+07:00  INFO 97609 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-10T10:39:34.613+07:00  INFO 97609 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-10T10:39:34.613+07:00  INFO 97609 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-10T10:39:34.614+07:00  INFO 97609 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-10T10:39:34.616+07:00  INFO 97609 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@1320d929{STOPPING}[12.0.15,sto=0]
2025-07-10T10:39:34.618+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10T10:39:34.620+07:00  INFO 97609 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@38dde7ec{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7933980433253864684/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3f19f197{STOPPED}}
