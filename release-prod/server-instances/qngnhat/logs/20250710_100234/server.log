2025-07-10T10:02:35.465+07:00  INFO 94273 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 94273 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-10T10:02:35.466+07:00  INFO 94273 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data"
2025-07-10T10:02:36.370+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.497+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 123 ms. Found 22 JPA repository interfaces.
2025-07-10T10:02:36.507+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.509+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-07-10T10:02:36.510+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.518+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 9 JPA repository interfaces.
2025-07-10T10:02:36.519+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.522+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T10:02:36.523+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.527+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T10:02:36.541+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.548+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 1 JPA repository interface.
2025-07-10T10:02:36.560+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.567+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 5 JPA repository interfaces.
2025-07-10T10:02:36.571+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.574+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T10:02:36.574+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.575+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:02:36.580+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.588+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 9 JPA repository interfaces.
2025-07-10T10:02:36.594+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.598+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-07-10T10:02:36.598+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.602+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T10:02:36.604+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.613+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-07-10T10:02:36.613+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.616+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-07-10T10:02:36.617+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.617+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:02:36.617+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.618+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T10:02:36.619+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.623+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-10T10:02:36.623+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.625+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T10:02:36.625+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.625+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:02:36.625+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.637+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-07-10T10:02:36.648+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.655+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-07-10T10:02:36.655+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.661+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 5 JPA repository interfaces.
2025-07-10T10:02:36.661+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.666+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-10T10:02:36.666+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.672+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-10T10:02:36.672+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.677+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T10:02:36.677+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.680+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T10:02:36.680+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.690+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-07-10T10:02:36.690+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.705+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 25 JPA repository interfaces.
2025-07-10T10:02:36.720+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.733+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-07-10T10:02:36.734+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.738+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T10:02:36.738+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.740+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T10:02:36.745+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.746+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T10:02:36.746+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.754+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-10T10:02:36.759+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.801+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 42 ms. Found 67 JPA repository interfaces.
2025-07-10T10:02:36.802+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.803+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T10:02:36.803+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T10:02:36.806+07:00  INFO 94273 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T10:02:37.073+07:00  INFO 94273 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-10T10:02:37.077+07:00  INFO 94273 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-10T10:02:37.430+07:00  WARN 94273 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-10T10:02:37.703+07:00  INFO 94273 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-10T10:02:37.705+07:00  INFO 94273 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-10T10:02:37.717+07:00  INFO 94273 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-10T10:02:37.717+07:00  INFO 94273 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2105 ms
2025-07-10T10:02:37.769+07:00  WARN 94273 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:02:37.769+07:00  INFO 94273 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-10T10:02:37.993+07:00  INFO 94273 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@4e791e53
2025-07-10T10:02:37.994+07:00  INFO 94273 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-10T10:02:38.000+07:00  WARN 94273 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:02:38.000+07:00  INFO 94273 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-10T10:02:38.012+07:00  INFO 94273 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@1c6ab85
2025-07-10T10:02:38.012+07:00  INFO 94273 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-10T10:02:38.012+07:00  WARN 94273 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:02:38.012+07:00  INFO 94273 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-10T10:02:38.543+07:00  INFO 94273 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1243ad9a
2025-07-10T10:02:38.543+07:00  INFO 94273 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-10T10:02:38.543+07:00  WARN 94273 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T10:02:38.543+07:00  INFO 94273 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-10T10:02:38.585+07:00  INFO 94273 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@46f43f50
2025-07-10T10:02:38.585+07:00  INFO 94273 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-10T10:02:38.585+07:00  INFO 94273 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-10T10:02:38.646+07:00  INFO 94273 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-10T10:02:38.648+07:00  INFO 94273 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@2381a837{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8731664061529440369/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@2523730b{STARTED}}
2025-07-10T10:02:38.649+07:00  INFO 94273 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@2381a837{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8731664061529440369/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@2523730b{STARTED}}
2025-07-10T10:02:38.651+07:00  INFO 94273 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7441eed3{STARTING}[12.0.15,sto=0] @3828ms
2025-07-10T10:02:38.715+07:00  INFO 94273 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T10:02:38.743+07:00  INFO 94273 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-10T10:02:38.760+07:00  INFO 94273 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T10:02:38.888+07:00  INFO 94273 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T10:02:38.921+07:00  WARN 94273 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T10:02:39.553+07:00  INFO 94273 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T10:02:39.563+07:00  INFO 94273 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@614876bc] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T10:02:39.711+07:00  INFO 94273 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T10:02:39.896+07:00  INFO 94273 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "cloud.datatp.fforwarder.price", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-10T10:02:39.899+07:00  INFO 94273 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-10T10:02:39.908+07:00  INFO 94273 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T10:02:39.909+07:00  INFO 94273 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T10:02:39.945+07:00  INFO 94273 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T10:02:39.956+07:00  WARN 94273 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T10:02:42.789+07:00  INFO 94273 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T10:02:42.790+07:00  INFO 94273 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@337ddffe] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T10:02:43.086+07:00  WARN 94273 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-10T10:02:43.086+07:00  WARN 94273 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-10T10:02:43.097+07:00  WARN 94273 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-10T10:02:43.097+07:00  WARN 94273 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-10T10:02:43.111+07:00  WARN 94273 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-07-10T10:02:43.112+07:00  WARN 94273 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-07-10T10:02:43.812+07:00  INFO 94273 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T10:02:43.856+07:00  INFO 94273 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-10T10:02:43.860+07:00  INFO 94273 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-10T10:02:43.860+07:00  INFO 94273 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T10:02:43.869+07:00  WARN 94273 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T10:02:44.002+07:00  INFO 94273 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10T10:02:44.480+07:00  INFO 94273 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T10:02:44.483+07:00  INFO 94273 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T10:02:44.517+07:00  INFO 94273 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-10T10:02:44.562+07:00  INFO 94273 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-10T10:02:44.647+07:00  INFO 94273 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-10T10:02:44.677+07:00  INFO 94273 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T10:02:44.699+07:00  INFO 94273 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2695666ms : this is harmless.
2025-07-10T10:02:44.707+07:00  INFO 94273 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-10T10:02:44.709+07:00  INFO 94273 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T10:02:44.838+07:00  INFO 94273 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2695652ms : this is harmless.
2025-07-10T10:02:44.840+07:00  INFO 94273 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-10T10:02:44.852+07:00  INFO 94273 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-10T10:02:44.853+07:00  INFO 94273 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-10T10:02:48.241+07:00  INFO 94273 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@10:00:00+0700 to 10/07/2025@10:15:00+0700
2025-07-10T10:02:48.241+07:00  INFO 94273 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@10:00:00+0700 to 10/07/2025@10:15:00+0700
2025-07-10T10:02:48.988+07:00  INFO 94273 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-10T10:02:48.988+07:00  INFO 94273 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T10:02:48.989+07:00  WARN 94273 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T10:02:49.234+07:00  INFO 94273 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-10T10:02:49.235+07:00  INFO 94273 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-10T10:02:49.235+07:00  INFO 94273 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-10T10:02:49.235+07:00  INFO 94273 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-10T10:02:49.235+07:00  INFO 94273 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-10T10:02:50.693+07:00  WARN 94273 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 4b7da35f-0aeb-4395-9076-9135579b9bb4

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10T10:02:50.696+07:00  INFO 94273 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-10T10:02:51.025+07:00  INFO 94273 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T10:02:51.025+07:00  INFO 94273 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-10T10:02:51.025+07:00  INFO 94273 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-10T10:02:51.025+07:00  INFO 94273 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T10:02:51.025+07:00  INFO 94273 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-10T10:02:51.025+07:00  INFO 94273 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T10:02:51.026+07:00  INFO 94273 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T10:02:51.026+07:00  INFO 94273 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-10T10:02:51.026+07:00  INFO 94273 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T10:02:51.026+07:00  INFO 94273 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T10:02:51.026+07:00  INFO 94273 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-10T10:02:51.026+07:00  INFO 94273 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-10T10:02:51.029+07:00  INFO 94273 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T10:02:51.029+07:00  INFO 94273 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T10:02:51.029+07:00  INFO 94273 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T10:02:51.081+07:00  INFO 94273 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10T10:02:51.081+07:00  INFO 94273 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-10T10:02:51.084+07:00  INFO 94273 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-10T10:02:51.092+07:00  INFO 94273 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@7354cd37{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T10:02:51.093+07:00  INFO 94273 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-10T10:02:51.094+07:00  INFO 94273 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-10T10:02:51.124+07:00  INFO 94273 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-10T10:02:51.124+07:00  INFO 94273 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-10T10:02:51.131+07:00  INFO 94273 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 16.054 seconds (process running for 16.308)
2025-07-10T10:03:05.074+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:03:54.181+07:00  INFO 94273 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:03:54.187+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:03:54.188+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:03:54.189+07:00  INFO 94273 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:04:06.204+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:04:27.247+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:04:27.248+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:04:57.303+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:04:57.304+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:05:04.315+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:05:04.318+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T10:05:25.353+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:05:25.355+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:05:53.414+07:00  INFO 94273 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:05:53.426+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:05:53.427+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:05:53.428+07:00  INFO 94273 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:06:06.446+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:06:21.557+07:00  INFO 94273 --- [qtp2072567128-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01ne4qlmnkwpso1u97dt9oq5mxx0
2025-07-10T10:06:21.883+07:00  INFO 94273 --- [qtp2072567128-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01ne4qlmnkwpso1u97dt9oq5mxx0, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:06:21.975+07:00  INFO 94273 --- [qtp2072567128-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:06:23.594+07:00 DEBUG 94273 --- [qtp2072567128-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:06:23.594+07:00 DEBUG 94273 --- [qtp2072567128-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:06:27.490+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:06:27.491+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:06:27.492+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:06:32.297+07:00  INFO 94273 --- [qtp2072567128-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01ne4qlmnkwpso1u97dt9oq5mxx0, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:06:32.298+07:00  INFO 94273 --- [qtp2072567128-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01ne4qlmnkwpso1u97dt9oq5mxx0, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:06:32.303+07:00  INFO 94273 --- [qtp2072567128-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:06:32.304+07:00  INFO 94273 --- [qtp2072567128-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:06:33.928+07:00 DEBUG 94273 --- [qtp2072567128-64] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:06:33.962+07:00 DEBUG 94273 --- [qtp2072567128-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:06:49.578+07:00  INFO 94273 --- [qtp2072567128-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01ne4qlmnkwpso1u97dt9oq5mxx0, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:06:49.579+07:00  INFO 94273 --- [qtp2072567128-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01ne4qlmnkwpso1u97dt9oq5mxx0, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:06:49.587+07:00  INFO 94273 --- [qtp2072567128-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:06:49.589+07:00  INFO 94273 --- [qtp2072567128-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:06:50.736+07:00 DEBUG 94273 --- [qtp2072567128-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:06:50.738+07:00 DEBUG 94273 --- [qtp2072567128-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:06:54.930+07:00  INFO 94273 --- [qtp2072567128-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01ne4qlmnkwpso1u97dt9oq5mxx0, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:06:54.948+07:00  INFO 94273 --- [qtp2072567128-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:06:54.962+07:00  INFO 94273 --- [qtp2072567128-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01ne4qlmnkwpso1u97dt9oq5mxx0, token = 1ad7b4a47939d339280ce160ed133b0c
2025-07-10T10:06:54.968+07:00  INFO 94273 --- [qtp2072567128-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T10:06:56.169+07:00 DEBUG 94273 --- [qtp2072567128-64] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:06:56.199+07:00 DEBUG 94273 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:06:56.548+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:06:56.549+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:06:56.549+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:07:03.555+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:07:24.590+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:07:24.592+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:07:24.593+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:07:56.193+07:00 DEBUG 94273 --- [qtp2072567128-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:07:57.671+07:00  INFO 94273 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-07-10T10:07:57.704+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:07:57.704+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:07:57.704+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:07:57.704+07:00  INFO 94273 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:08:06.724+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:08:27.757+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:08:27.757+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:08:27.758+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:08:55.794+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:08:55.796+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:08:55.796+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:08:56.185+07:00 DEBUG 94273 --- [qtp2072567128-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:09:02.807+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:09:23.846+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:09:23.847+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:09:23.848+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:09:56.210+07:00 DEBUG 94273 --- [qtp2072567128-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:09:57.964+07:00  INFO 94273 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-07-10T10:09:57.968+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:09:57.968+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:09:57.968+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:09:57.985+07:00  INFO 94273 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T10:10:06.008+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:10:06.010+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T10:10:27.042+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:10:27.044+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:10:27.045+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:10:55.101+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:10:55.102+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:10:55.102+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:10:56.177+07:00 DEBUG 94273 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:11:02.118+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:11:23.149+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:11:23.153+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:11:23.154+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:11:56.221+07:00 DEBUG 94273 --- [qtp2072567128-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:11:57.218+07:00  INFO 94273 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:11:57.222+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:11:57.222+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:11:57.222+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:11:57.223+07:00  INFO 94273 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T10:12:05.241+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:12:26.284+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:12:26.287+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:12:26.288+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:12:54.326+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:12:54.327+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:12:54.327+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:12:56.176+07:00 DEBUG 94273 --- [qtp2072567128-74] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T10:13:06.348+07:00  INFO 94273 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T10:13:27.409+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T10:13:27.412+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ne4qlmnkwpso1u97dt9oq5mxx0, remote user nhat.le
2025-07-10T10:13:27.412+07:00 DEBUG 94273 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T10:13:37.706+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@7354cd37{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T10:13:37.708+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T10:13:37.709+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-10T10:13:37.709+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-10T10:13:37.726+07:00  INFO 94273 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-10T10:13:37.832+07:00  INFO 94273 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-10T10:13:37.839+07:00  INFO 94273 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-10T10:13:37.861+07:00  INFO 94273 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T10:13:37.867+07:00  INFO 94273 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T10:13:37.868+07:00  INFO 94273 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-10T10:13:37.869+07:00  INFO 94273 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-10T10:13:37.869+07:00  INFO 94273 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-10T10:13:38.019+07:00  INFO 94273 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-10T10:13:38.019+07:00  INFO 94273 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-10T10:13:38.020+07:00  INFO 94273 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-10T10:13:38.020+07:00  INFO 94273 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-10T10:13:38.021+07:00  INFO 94273 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-10T10:13:38.027+07:00  INFO 94273 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7441eed3{STOPPING}[12.0.15,sto=0]
2025-07-10T10:13:38.035+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10T10:13:38.037+07:00  INFO 94273 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@2381a837{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8731664061529440369/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@2523730b{STOPPED}}
