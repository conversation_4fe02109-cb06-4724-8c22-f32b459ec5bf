2025-07-10T14:44:57.839+07:00  INFO 27430 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 27430 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-10T14:44:57.840+07:00  INFO 27430 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-10T14:44:58.599+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.662+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 22 JPA repository interfaces.
2025-07-10T14:44:58.671+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.672+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T14:44:58.673+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.679+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-10T14:44:58.680+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.683+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T14:44:58.683+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.687+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T14:44:58.697+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.702+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-10T14:44:58.712+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.756+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 43 ms. Found 5 JPA repository interfaces.
2025-07-10T14:44:58.760+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.763+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T14:44:58.763+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.763+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:44:58.768+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.774+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T14:44:58.778+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.781+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T14:44:58.781+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.784+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T14:44:58.785+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.792+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-10T14:44:58.792+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.795+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T14:44:58.795+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.796+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:44:58.796+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.797+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-10T14:44:58.797+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.801+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-10T14:44:58.801+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.802+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T14:44:58.802+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.803+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:44:58.803+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.812+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-07-10T14:44:58.821+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.826+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-07-10T14:44:58.827+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.829+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T14:44:58.830+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.833+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-10T14:44:58.833+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.838+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-07-10T14:44:58.838+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.841+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T14:44:58.842+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.844+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T14:44:58.844+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.853+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-07-10T14:44:58.853+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.867+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 25 JPA repository interfaces.
2025-07-10T14:44:58.879+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.889+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-10T14:44:58.890+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.894+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T14:44:58.894+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.896+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T14:44:58.900+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.901+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:44:58.901+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.907+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-10T14:44:58.911+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.946+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 67 JPA repository interfaces.
2025-07-10T14:44:58.947+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.948+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-10T14:44:58.948+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:44:58.950+07:00  INFO 27430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T14:44:59.133+07:00  INFO 27430 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-10T14:44:59.137+07:00  INFO 27430 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-10T14:44:59.403+07:00  WARN 27430 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-10T14:44:59.588+07:00  INFO 27430 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-10T14:44:59.591+07:00  INFO 27430 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-10T14:44:59.602+07:00  INFO 27430 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-10T14:44:59.602+07:00  INFO 27430 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1612 ms
2025-07-10T14:44:59.647+07:00  WARN 27430 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:44:59.647+07:00  INFO 27430 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-10T14:44:59.735+07:00  INFO 27430 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@23138317
2025-07-10T14:44:59.736+07:00  INFO 27430 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-10T14:44:59.740+07:00  WARN 27430 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:44:59.741+07:00  INFO 27430 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-10T14:44:59.746+07:00  INFO 27430 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@2c5157c9
2025-07-10T14:44:59.746+07:00  INFO 27430 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-10T14:44:59.746+07:00  WARN 27430 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:44:59.746+07:00  INFO 27430 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-10T14:45:00.372+07:00  INFO 27430 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3d26c82d
2025-07-10T14:45:00.373+07:00  INFO 27430 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-10T14:45:00.373+07:00  WARN 27430 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:45:00.373+07:00  INFO 27430 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-10T14:45:00.388+07:00  INFO 27430 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@75abbb61
2025-07-10T14:45:00.388+07:00  INFO 27430 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-10T14:45:00.389+07:00  INFO 27430 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-10T14:45:00.453+07:00  INFO 27430 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-10T14:45:00.455+07:00  INFO 27430 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@160a8986{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3708835920546081923/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@2f24e1f3{STARTED}}
2025-07-10T14:45:00.456+07:00  INFO 27430 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@160a8986{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3708835920546081923/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@2f24e1f3{STARTED}}
2025-07-10T14:45:00.457+07:00  INFO 27430 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@4ec25389{STARTING}[12.0.15,sto=0] @3129ms
2025-07-10T14:45:00.513+07:00  INFO 27430 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T14:45:00.542+07:00  INFO 27430 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-10T14:45:00.557+07:00  INFO 27430 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T14:45:00.686+07:00  INFO 27430 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T14:45:00.709+07:00  WARN 27430 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T14:45:01.296+07:00  INFO 27430 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T14:45:01.304+07:00  INFO 27430 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@51b59c1a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T14:45:01.338+07:00  INFO 27430 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:45:01.526+07:00  INFO 27430 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "cloud.datatp.fforwarder.price", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.groovy", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-07-10T14:45:01.528+07:00  INFO 27430 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-10T14:45:01.536+07:00  INFO 27430 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T14:45:01.537+07:00  INFO 27430 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T14:45:01.567+07:00  INFO 27430 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T14:45:01.572+07:00  WARN 27430 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T14:45:04.293+07:00  INFO 27430 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T14:45:04.294+07:00  INFO 27430 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@fbeac6b] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T14:45:04.380+07:00  INFO 27430 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:45:04.411+07:00  INFO 27430 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-10T14:45:04.415+07:00  INFO 27430 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-10T14:45:04.415+07:00  INFO 27430 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T14:45:04.421+07:00  WARN 27430 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T14:45:04.548+07:00  INFO 27430 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10T14:45:04.987+07:00  INFO 27430 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T14:45:04.990+07:00  INFO 27430 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T14:45:05.022+07:00  INFO 27430 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-10T14:45:05.057+07:00  INFO 27430 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-10T14:45:05.119+07:00  INFO 27430 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-10T14:45:05.145+07:00  WARN 27430 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 85.9MB of free physical memory - some paging will therefore occur.
2025-07-10T14:45:05.145+07:00  INFO 27430 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T14:45:05.169+07:00  INFO 27430 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 21550488ms : this is harmless.
2025-07-10T14:45:05.176+07:00  INFO 27430 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-10T14:45:05.179+07:00  WARN 27430 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 79.8MB of free physical memory - some paging will therefore occur.
2025-07-10T14:45:05.179+07:00  INFO 27430 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T14:45:05.191+07:00  INFO 27430 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 21550473ms : this is harmless.
2025-07-10T14:45:05.193+07:00  INFO 27430 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-10T14:45:05.204+07:00  INFO 27430 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-10T14:45:05.205+07:00  INFO 27430 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-10T14:45:07.734+07:00  INFO 27430 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@14:45:00+0700 to 10/07/2025@15:00:00+0700
2025-07-10T14:45:07.734+07:00  INFO 27430 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@14:45:00+0700 to 10/07/2025@15:00:00+0700
2025-07-10T14:45:08.512+07:00  INFO 27430 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-10T14:45:08.513+07:00  INFO 27430 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T14:45:08.513+07:00  WARN 27430 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T14:45:08.749+07:00  INFO 27430 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-10T14:45:08.750+07:00  INFO 27430 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-10T14:45:08.750+07:00  INFO 27430 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-10T14:45:08.750+07:00  INFO 27430 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-10T14:45:08.750+07:00  INFO 27430 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-10T14:45:10.206+07:00  WARN 27430 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: ce9288cf-d26b-4cbd-b01e-4c6c7d7787e9

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10T14:45:10.209+07:00  INFO 27430 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-10T14:45:10.537+07:00  INFO 27430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-10T14:45:10.540+07:00  INFO 27430 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T14:45:10.540+07:00  INFO 27430 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T14:45:10.540+07:00  INFO 27430 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T14:45:10.629+07:00  INFO 27430 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10T14:45:10.630+07:00  INFO 27430 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-10T14:45:10.631+07:00  INFO 27430 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-10T14:45:10.639+07:00  INFO 27430 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@7d4b31e1{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T14:45:10.640+07:00  INFO 27430 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-10T14:45:10.641+07:00  INFO 27430 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-10T14:45:10.672+07:00  INFO 27430 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-10T14:45:10.672+07:00  INFO 27430 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-10T14:45:10.678+07:00  INFO 27430 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.139 seconds (process running for 13.349)
2025-07-10T14:45:15.684+07:00  INFO 27430 --- [qtp1586007994-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0l44p6726l8xxclie5u2b4nry0
2025-07-10T14:45:15.684+07:00  INFO 27430 --- [qtp1586007994-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01fbra3ohkebg9r928kbnw9vpd1
2025-07-10T14:45:16.039+07:00  INFO 27430 --- [qtp1586007994-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0l44p6726l8xxclie5u2b4nry0, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:45:16.045+07:00  INFO 27430 --- [qtp1586007994-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01fbra3ohkebg9r928kbnw9vpd1, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:45:16.434+07:00  INFO 27430 --- [qtp1586007994-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:45:16.434+07:00  INFO 27430 --- [qtp1586007994-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:45:17.928+07:00 DEBUG 27430 --- [qtp1586007994-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:45:17.955+07:00 DEBUG 27430 --- [qtp1586007994-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:45:25.200+07:00  INFO 27430 --- [qtp1586007994-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01fbra3ohkebg9r928kbnw9vpd1, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:45:25.216+07:00  INFO 27430 --- [qtp1586007994-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:45:25.230+07:00  INFO 27430 --- [qtp1586007994-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01fbra3ohkebg9r928kbnw9vpd1, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:45:25.238+07:00  INFO 27430 --- [qtp1586007994-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:45:26.984+07:00 DEBUG 27430 --- [qtp1586007994-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:45:26.994+07:00 DEBUG 27430 --- [qtp1586007994-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:45:39.284+07:00  INFO 27430 --- [qtp1586007994-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01fbra3ohkebg9r928kbnw9vpd1, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:45:39.305+07:00  INFO 27430 --- [qtp1586007994-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:45:39.308+07:00  INFO 27430 --- [qtp1586007994-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01fbra3ohkebg9r928kbnw9vpd1, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:45:39.318+07:00  INFO 27430 --- [qtp1586007994-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:45:40.765+07:00 DEBUG 27430 --- [qtp1586007994-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:45:40.765+07:00 DEBUG 27430 --- [qtp1586007994-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:46:06.712+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:46:13.728+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:46:13.730+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:46:13.730+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:46:13.732+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:46:13.733+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:46:40.781+07:00 DEBUG 27430 --- [qtp1586007994-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:46:46.784+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:46:46.785+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:46:46.785+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:47:02.809+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:47:16.841+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:47:16.843+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:47:16.843+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:47:40.771+07:00 DEBUG 27430 --- [qtp1586007994-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:47:44.899+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:47:44.900+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:47:44.900+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:48:05.939+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:48:12.974+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-07-10T14:48:12.990+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:48:12.990+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:48:12.990+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:48:12.994+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T14:48:40.772+07:00 DEBUG 27430 --- [qtp1586007994-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:48:47.047+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:48:47.048+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:48:47.048+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:49:02.075+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:49:16.107+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:49:16.108+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:49:16.108+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:49:40.817+07:00 DEBUG 27430 --- [qtp1586007994-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:49:44.161+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:49:44.162+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:49:44.162+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:50:05.199+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:50:05.203+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T14:50:17.230+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:50:17.240+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:50:17.240+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:50:17.240+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:50:17.241+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:50:40.806+07:00 DEBUG 27430 --- [qtp1586007994-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:50:47.294+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:50:47.295+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:50:47.295+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:51:06.325+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:51:15.354+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:51:15.355+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:51:15.355+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:51:40.842+07:00 DEBUG 27430 --- [qtp1586007994-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:51:43.401+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:51:43.401+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:51:43.401+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:52:04.439+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:52:17.467+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:52:17.476+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:52:17.476+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:52:17.476+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:52:17.477+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:52:41.707+07:00 DEBUG 27430 --- [qtp1586007994-65] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:52:46.524+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:52:46.528+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:52:46.528+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:53:06.565+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:53:14.582+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:53:14.583+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:53:14.583+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:53:47.630+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:53:47.630+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:53:47.631+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:54:03.661+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:54:17.700+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:54:17.722+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:54:17.722+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:54:17.722+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:54:17.723+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:54:34.788+07:00 DEBUG 27430 --- [qtp1586007994-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:54:45.771+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:54:45.772+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:54:45.772+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:55:00.496+07:00  INFO 27430 --- [Scheduler-1518728219-1] n.d.m.session.AppHttpSessionListener     : The session node0l44p6726l8xxclie5u2b4nry0 is destroyed.
2025-07-10T14:55:06.804+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:55:06.804+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T14:55:13.814+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:55:13.815+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:55:34.780+07:00  INFO 27430 --- [qtp1586007994-65] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T14:55:46.868+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:55:46.869+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:56:02.901+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:56:16.933+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 6
2025-07-10T14:56:16.943+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:56:16.943+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:56:16.945+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:56:37.674+07:00  INFO 27430 --- [qtp1586007994-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01fbra3ohkebg9r928kbnw9vpd1, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:56:37.674+07:00  INFO 27430 --- [qtp1586007994-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01fbra3ohkebg9r928kbnw9vpd1, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:56:37.693+07:00  INFO 27430 --- [qtp1586007994-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:56:37.693+07:00  INFO 27430 --- [qtp1586007994-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:56:40.911+07:00 DEBUG 27430 --- [qtp1586007994-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:56:40.949+07:00 DEBUG 27430 --- [qtp1586007994-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:56:44.981+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:56:44.981+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:56:44.982+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:57:06.009+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:57:13.024+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:57:13.025+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:57:13.025+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:57:41.712+07:00 DEBUG 27430 --- [qtp1586007994-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:57:47.080+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:57:47.081+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:57:47.081+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:58:02.108+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:58:16.166+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-07-10T14:58:16.182+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:58:16.182+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:58:16.182+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:58:16.184+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T14:58:41.718+07:00 DEBUG 27430 --- [qtp1586007994-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:58:44.232+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:58:44.232+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:58:44.232+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:59:05.264+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:59:17.279+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:59:17.281+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:59:17.281+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:59:40.950+07:00 DEBUG 27430 --- [qtp1586007994-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:59:47.331+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:59:47.331+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T14:59:47.331+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:00:06.359+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:00:06.362+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:00:06.366+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T15:00:06.368+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-07-10T15:00:06.371+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-07-10T15:00:06.372+07:00  INFO 27430 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@15:00:06+0700
2025-07-10T15:00:06.395+07:00  INFO 27430 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@15:00:00+0700 to 10/07/2025@15:15:00+0700
2025-07-10T15:00:06.395+07:00  INFO 27430 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@15:00:00+0700 to 10/07/2025@15:15:00+0700
2025-07-10T15:00:07.435+07:00 DEBUG 27430 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T15:00:15.438+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:00:15.448+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:00:15.448+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:00:15.448+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:00:15.449+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:00:40.958+07:00 DEBUG 27430 --- [qtp1586007994-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:00:43.497+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:00:43.497+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:00:43.497+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:01:04.533+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:01:17.561+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:01:17.562+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:01:17.563+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:01:40.937+07:00 DEBUG 27430 --- [qtp1586007994-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:01:46.593+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:01:46.597+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:01:46.597+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:02:06.627+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:02:14.657+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:02:14.682+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:02:14.682+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:02:14.682+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:02:14.682+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:02:40.948+07:00 DEBUG 27430 --- [qtp1586007994-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:02:42.721+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:02:42.722+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:02:42.722+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:03:03.755+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:03:16.783+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:03:16.788+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:03:16.789+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:03:40.946+07:00 DEBUG 27430 --- [qtp1586007994-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:03:45.838+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:03:45.838+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:03:45.838+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:04:06.867+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:04:13.887+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:04:13.889+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:04:13.889+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:04:13.889+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:04:13.890+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:04:40.960+07:00 DEBUG 27430 --- [qtp1586007994-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:04:46.924+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:04:46.925+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:04:46.925+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:05:02.953+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:05:02.957+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:05:16.981+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:05:16.984+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:05:16.985+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:05:40.959+07:00 DEBUG 27430 --- [qtp1586007994-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:05:45.038+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:05:45.039+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:05:45.039+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:06:06.074+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:06:13.106+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T15:06:13.117+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:06:13.118+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:06:13.118+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:06:13.120+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:06:40.939+07:00 DEBUG 27430 --- [qtp1586007994-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:06:47.176+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:06:47.176+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:06:47.176+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:07:02.207+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:07:16.235+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:07:16.238+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:07:16.239+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:07:40.943+07:00 DEBUG 27430 --- [qtp1586007994-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:07:44.292+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:07:44.292+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:07:44.292+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:08:05.330+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:08:17.353+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 6
2025-07-10T15:08:17.365+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:08:17.366+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:08:17.366+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:08:17.368+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:08:40.972+07:00 DEBUG 27430 --- [qtp1586007994-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:08:47.434+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:08:47.436+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:08:47.436+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:09:06.458+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:09:15.487+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:09:15.487+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:09:15.488+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:09:40.956+07:00 DEBUG 27430 --- [qtp1586007994-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:09:43.535+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:09:43.536+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:09:43.536+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:10:04.569+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:10:04.572+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:10:17.602+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T15:10:17.609+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:10:17.610+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:10:17.610+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:10:17.611+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:10:40.961+07:00 DEBUG 27430 --- [qtp1586007994-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:10:46.665+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:10:46.667+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:10:46.668+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:11:06.690+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:11:14.710+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:11:14.711+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:11:14.711+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:11:40.947+07:00 DEBUG 27430 --- [qtp1586007994-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:11:42.755+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:11:42.756+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:11:42.756+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:12:03.797+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:12:16.828+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:12:16.829+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:12:16.830+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:12:16.830+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:12:16.831+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:12:40.970+07:00 DEBUG 27430 --- [qtp1586007994-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:12:45.879+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:12:45.879+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:12:45.880+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:13:06.909+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:13:13.920+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:13:13.922+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:13:13.922+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:13:40.939+07:00 DEBUG 27430 --- [qtp1586007994-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:13:46.971+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:13:46.973+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:13:46.973+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:14:03.007+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:14:17.048+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:14:17.074+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:14:17.074+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:14:17.074+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:14:17.074+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:14:40.967+07:00 DEBUG 27430 --- [qtp1586007994-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:14:45.126+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:14:45.126+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:14:45.127+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:15:06.160+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:15:06.163+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:15:06.163+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T15:15:06.164+07:00  INFO 27430 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@15:15:06+0700
2025-07-10T15:15:06.184+07:00  INFO 27430 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@15:15:00+0700 to 10/07/2025@15:30:00+0700
2025-07-10T15:15:06.185+07:00  INFO 27430 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@15:15:00+0700 to 10/07/2025@15:30:00+0700
2025-07-10T15:15:12.194+07:00 DEBUG 27430 --- [botTaskExecutor-3] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T15:15:13.193+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:15:13.193+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:15:13.194+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:15:40.958+07:00 DEBUG 27430 --- [qtp1586007994-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:15:47.248+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:15:47.248+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:15:47.248+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:16:02.277+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:16:16.326+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:16:16.335+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:16:16.335+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:16:16.335+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:16:16.335+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:16:41.002+07:00 DEBUG 27430 --- [qtp1586007994-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:16:44.377+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:16:44.377+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:16:44.377+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:17:05.414+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:17:17.426+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:17:17.428+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:17:17.428+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:17:40.982+07:00 DEBUG 27430 --- [qtp1586007994-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:17:47.478+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:17:47.478+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:17:47.478+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:18:06.505+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:18:15.541+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:18:15.551+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:18:15.551+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:18:15.551+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:18:15.553+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:18:40.947+07:00 DEBUG 27430 --- [qtp1586007994-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:18:43.597+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:18:43.597+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:18:43.598+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:19:04.633+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:19:17.651+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:19:17.651+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:19:17.651+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:19:40.956+07:00 DEBUG 27430 --- [qtp1586007994-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:19:46.697+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:19:46.697+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:19:46.698+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:20:06.730+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:20:06.732+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:20:14.748+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:20:14.750+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:20:14.750+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:20:14.751+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:20:14.751+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:20:40.954+07:00 DEBUG 27430 --- [qtp1586007994-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:20:42.803+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:20:42.803+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:20:42.804+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:21:03.829+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:21:16.855+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:21:16.859+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:21:16.860+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:21:40.978+07:00 DEBUG 27430 --- [qtp1586007994-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:21:45.910+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:21:45.911+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:21:45.911+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:22:06.946+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:22:13.963+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:22:13.969+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:22:13.969+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:22:13.969+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:22:13.970+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:22:41.028+07:00 DEBUG 27430 --- [qtp1586007994-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:22:47.015+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:22:47.016+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:22:47.016+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:23:03.045+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:23:17.073+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:23:17.074+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:23:17.074+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:23:40.984+07:00 DEBUG 27430 --- [qtp1586007994-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:23:45.130+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:23:45.131+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:23:45.131+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:24:06.166+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:24:13.180+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:24:13.185+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:24:13.185+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:24:13.185+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:24:13.186+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:24:40.968+07:00 DEBUG 27430 --- [qtp1586007994-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:24:47.225+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:24:47.225+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:24:47.225+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:25:02.250+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:25:02.254+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:25:16.279+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:25:16.281+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:25:16.281+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:25:41.000+07:00 DEBUG 27430 --- [qtp1586007994-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:25:44.332+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:25:44.332+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:25:44.332+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:26:05.366+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:26:17.404+07:00  INFO 27430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T15:26:17.417+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:26:17.418+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:26:17.418+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:26:17.419+07:00  INFO 27430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:26:40.973+07:00 DEBUG 27430 --- [qtp1586007994-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:26:47.467+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:26:47.468+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:26:47.468+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:27:06.491+07:00  INFO 27430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:27:15.505+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:27:15.506+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:27:15.506+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:27:40.990+07:00 DEBUG 27430 --- [qtp1586007994-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:27:43.549+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:27:43.549+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01fbra3ohkebg9r928kbnw9vpd1, remote user nhat.le
2025-07-10T15:27:43.550+07:00 DEBUG 27430 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:27:55.309+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@7d4b31e1{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-10T15:27:55.310+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-10T15:27:55.324+07:00  INFO 27430 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:27:55.399+07:00  INFO 27430 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-10T15:27:55.403+07:00  INFO 27430 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-10T15:27:55.423+07:00  INFO 27430 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:27:55.425+07:00  INFO 27430 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:27:55.425+07:00  INFO 27430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-10T15:27:55.426+07:00  INFO 27430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-10T15:27:55.426+07:00  INFO 27430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-10T15:27:55.568+07:00  INFO 27430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-10T15:27:55.568+07:00  INFO 27430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-10T15:27:55.569+07:00  INFO 27430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-10T15:27:55.569+07:00  INFO 27430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-10T15:27:55.569+07:00  INFO 27430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-10T15:27:55.571+07:00  INFO 27430 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@4ec25389{STOPPING}[12.0.15,sto=0]
2025-07-10T15:27:55.574+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10T15:27:55.576+07:00  INFO 27430 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@160a8986{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3708835920546081923/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@2f24e1f3{STOPPED}}
