2025-07-10T08:45:26.198+07:00  INFO 87208 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 87208 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-10T08:45:26.199+07:00  INFO 87208 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data"
2025-07-10T08:45:27.070+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.175+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 101 ms. Found 22 JPA repository interfaces.
2025-07-10T08:45:27.185+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.187+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T08:45:27.187+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.194+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-10T08:45:27.195+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.199+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-07-10T08:45:27.200+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.204+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T08:45:27.216+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.221+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-07-10T08:45:27.232+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.237+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-10T08:45:27.241+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.243+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T08:45:27.244+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.244+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T08:45:27.249+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.255+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-10T08:45:27.260+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.263+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T08:45:27.263+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.267+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T08:45:27.269+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.276+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-10T08:45:27.277+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.280+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-07-10T08:45:27.281+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.281+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T08:45:27.281+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.282+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-10T08:45:27.282+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.287+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-10T08:45:27.287+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.288+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T08:45:27.288+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.289+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T08:45:27.289+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.300+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-07-10T08:45:27.310+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.316+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-07-10T08:45:27.316+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.319+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T08:45:27.320+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.323+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-10T08:45:27.324+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.330+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-10T08:45:27.330+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.335+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T08:45:27.335+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.338+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-07-10T08:45:27.339+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.348+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-07-10T08:45:27.349+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.366+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 16 ms. Found 25 JPA repository interfaces.
2025-07-10T08:45:27.377+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.390+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-07-10T08:45:27.391+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.396+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-10T08:45:27.396+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.398+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T08:45:27.403+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.404+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T08:45:27.404+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.411+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-10T08:45:27.415+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.454+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 39 ms. Found 67 JPA repository interfaces.
2025-07-10T08:45:27.455+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.457+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T08:45:27.457+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T08:45:27.460+07:00  INFO 87208 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T08:45:27.669+07:00  INFO 87208 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-10T08:45:27.675+07:00  INFO 87208 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-10T08:45:28.024+07:00  WARN 87208 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-10T08:45:28.297+07:00  INFO 87208 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-10T08:45:28.299+07:00  INFO 87208 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-10T08:45:28.315+07:00  INFO 87208 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-10T08:45:28.316+07:00  INFO 87208 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1984 ms
2025-07-10T08:45:28.394+07:00  WARN 87208 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T08:45:28.395+07:00  INFO 87208 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-10T08:45:28.527+07:00  INFO 87208 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@4e791e53
2025-07-10T08:45:28.528+07:00  INFO 87208 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-10T08:45:28.533+07:00  WARN 87208 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T08:45:28.533+07:00  INFO 87208 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-10T08:45:28.541+07:00  INFO 87208 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@1c6ab85
2025-07-10T08:45:28.541+07:00  INFO 87208 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-10T08:45:28.541+07:00  WARN 87208 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T08:45:28.541+07:00  INFO 87208 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-10T08:45:29.094+07:00  INFO 87208 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1243ad9a
2025-07-10T08:45:29.094+07:00  INFO 87208 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-10T08:45:29.095+07:00  WARN 87208 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T08:45:29.095+07:00  INFO 87208 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-10T08:45:29.106+07:00  INFO 87208 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@46f43f50
2025-07-10T08:45:29.106+07:00  INFO 87208 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-10T08:45:29.106+07:00  INFO 87208 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-10T08:45:29.166+07:00  INFO 87208 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-10T08:45:29.170+07:00  INFO 87208 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@2381a837{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5747435328460908252/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@2523730b{STARTED}}
2025-07-10T08:45:29.170+07:00  INFO 87208 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@2381a837{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5747435328460908252/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@2523730b{STARTED}}
2025-07-10T08:45:29.172+07:00  INFO 87208 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7441eed3{STARTING}[12.0.15,sto=0] @3655ms
2025-07-10T08:45:29.239+07:00  INFO 87208 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T08:45:29.275+07:00  INFO 87208 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-10T08:45:29.294+07:00  INFO 87208 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T08:45:29.450+07:00  INFO 87208 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T08:45:29.487+07:00  WARN 87208 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T08:45:30.442+07:00  INFO 87208 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T08:45:30.452+07:00  INFO 87208 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@614876bc] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T08:45:30.713+07:00  INFO 87208 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T08:45:31.018+07:00  INFO 87208 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "cloud.datatp.fforwarder.price", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-10T08:45:31.021+07:00  INFO 87208 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-10T08:45:31.029+07:00  INFO 87208 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T08:45:31.031+07:00  INFO 87208 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T08:45:31.064+07:00  INFO 87208 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T08:45:31.067+07:00  WARN 87208 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T08:45:34.306+07:00  INFO 87208 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T08:45:34.307+07:00  INFO 87208 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1e68fc23] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T08:45:34.597+07:00  WARN 87208 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-10T08:45:34.597+07:00  WARN 87208 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-10T08:45:34.612+07:00  WARN 87208 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-10T08:45:34.613+07:00  WARN 87208 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-10T08:45:34.627+07:00  WARN 87208 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-07-10T08:45:34.627+07:00  WARN 87208 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-07-10T08:45:35.506+07:00  INFO 87208 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T08:45:35.579+07:00  INFO 87208 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-10T08:45:35.585+07:00  INFO 87208 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-10T08:45:35.585+07:00  INFO 87208 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T08:45:35.595+07:00  WARN 87208 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T08:45:35.751+07:00  INFO 87208 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10T08:45:36.214+07:00  INFO 87208 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T08:45:36.216+07:00  INFO 87208 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T08:45:36.254+07:00  INFO 87208 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-10T08:45:36.303+07:00  INFO 87208 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-10T08:45:36.425+07:00  INFO 87208 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-10T08:45:36.456+07:00  INFO 87208 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T08:45:36.501+07:00  INFO 87208 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-10T08:45:36.504+07:00  INFO 87208 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T08:45:36.520+07:00  INFO 87208 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-10T08:45:36.540+07:00  INFO 87208 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-10T08:45:36.541+07:00  INFO 87208 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-10T08:45:39.456+07:00  INFO 87208 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@08:45:00+0700 to 10/07/2025@09:00:00+0700
2025-07-10T08:45:39.457+07:00  INFO 87208 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@08:45:00+0700 to 10/07/2025@09:00:00+0700
2025-07-10T08:45:40.055+07:00  INFO 87208 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-10T08:45:40.055+07:00  INFO 87208 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T08:45:40.056+07:00  WARN 87208 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T08:45:40.345+07:00  INFO 87208 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-10T08:45:40.346+07:00  INFO 87208 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-10T08:45:40.346+07:00  INFO 87208 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-10T08:45:40.346+07:00  INFO 87208 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-10T08:45:40.346+07:00  INFO 87208 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-10T08:45:42.158+07:00  WARN 87208 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 9baa2b97-2a9e-4a68-a8ec-676bb053258d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10T08:45:42.161+07:00  INFO 87208 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-10T08:45:42.693+07:00  INFO 87208 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T08:45:42.693+07:00  INFO 87208 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-10T08:45:42.694+07:00  INFO 87208 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-10T08:45:42.694+07:00  INFO 87208 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T08:45:42.694+07:00  INFO 87208 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-10T08:45:42.694+07:00  INFO 87208 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T08:45:42.694+07:00  INFO 87208 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T08:45:42.694+07:00  INFO 87208 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-10T08:45:42.694+07:00  INFO 87208 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T08:45:42.694+07:00  INFO 87208 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T08:45:42.694+07:00  INFO 87208 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-10T08:45:42.694+07:00  INFO 87208 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-10T08:45:42.699+07:00  INFO 87208 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T08:45:42.699+07:00  INFO 87208 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T08:45:42.700+07:00  INFO 87208 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T08:45:42.757+07:00  INFO 87208 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10T08:45:42.758+07:00  INFO 87208 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-10T08:45:42.759+07:00  INFO 87208 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-10T08:45:42.775+07:00  INFO 87208 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@6b12f3d{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T08:45:42.776+07:00  INFO 87208 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-10T08:45:42.778+07:00  INFO 87208 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-10T08:45:45.096+07:00  INFO 87208 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-10T08:45:45.097+07:00  INFO 87208 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-10T08:45:45.120+07:00  INFO 87208 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 19.274 seconds (process running for 19.603)
2025-07-10T08:46:01.861+07:00  INFO 87208 --- [qtp2072567128-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0fqgkgzpr39fbx7k0j7dfeaou0
2025-07-10T08:46:02.533+07:00  INFO 87208 --- [qtp2072567128-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0fqgkgzpr39fbx7k0j7dfeaou0, token = 281628a9e7577469ad2c955e2392b305
2025-07-10T08:46:03.292+07:00  INFO 87208 --- [qtp2072567128-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T08:46:03.740+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:46:04.478+07:00 DEBUG 87208 --- [qtp2072567128-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:46:04.507+07:00 DEBUG 87208 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:46:51.815+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T08:46:51.827+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:46:51.828+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:46:51.834+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:46:51.834+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T08:47:04.539+07:00 DEBUG 87208 --- [qtp2072567128-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:47:06.859+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:47:20.897+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:47:20.903+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:47:20.904+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:47:48.942+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:47:48.943+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:47:48.943+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:48:02.962+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:48:04.483+07:00 DEBUG 87208 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:48:21.991+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:48:21.993+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:48:21.993+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:48:52.092+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-07-10T08:48:52.094+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:48:52.094+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:48:52.094+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:48:52.095+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T08:49:04.475+07:00 DEBUG 87208 --- [qtp2072567128-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:49:06.113+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:49:20.144+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:49:20.145+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:49:20.145+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:49:48.191+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:49:48.197+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:49:48.199+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:50:02.226+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:50:02.232+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T08:50:04.474+07:00 DEBUG 87208 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:50:21.259+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:50:21.261+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:50:21.261+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:50:51.311+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T08:50:51.317+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:50:51.318+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:50:51.319+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:50:51.319+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T08:51:05.480+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:51:05.492+07:00 DEBUG 87208 --- [qtp2072567128-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:51:19.509+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:51:19.511+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:51:19.511+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:51:47.577+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:51:47.579+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:51:47.579+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:52:06.604+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:52:21.629+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:52:21.631+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:52:21.631+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:52:37.337+07:00 DEBUG 87208 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:52:50.731+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T08:52:50.758+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:52:50.758+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:52:50.758+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:52:50.758+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T08:53:04.780+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:53:18.805+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:53:18.806+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:53:18.806+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:53:37.316+07:00 DEBUG 87208 --- [qtp2072567128-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:53:51.851+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:53:51.851+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:53:51.851+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:54:06.884+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:54:21.908+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:54:21.910+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:54:21.910+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:54:37.314+07:00 DEBUG 87208 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:54:49.962+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T08:54:49.964+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:54:49.965+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:54:49.966+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:54:49.966+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T08:55:03.988+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T08:55:03.989+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:55:18.014+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:55:18.015+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:55:18.015+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:55:37.332+07:00 DEBUG 87208 --- [qtp2072567128-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:55:52.073+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:55:52.074+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:55:52.075+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:56:06.089+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:56:21.122+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:56:21.123+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:56:21.123+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:56:37.336+07:00 DEBUG 87208 --- [qtp2072567128-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:56:49.174+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-07-10T08:56:49.180+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:56:49.180+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:56:49.180+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:56:49.181+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T08:57:03.203+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:57:17.229+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:57:17.229+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:57:17.229+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:57:37.356+07:00 DEBUG 87208 --- [qtp2072567128-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:57:51.277+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:57:51.277+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:57:51.277+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:58:06.305+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:58:20.330+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:58:20.331+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:58:20.331+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:58:37.315+07:00 DEBUG 87208 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:58:48.377+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T08:58:48.380+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:58:48.380+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:58:48.380+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:58:48.381+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T08:59:02.405+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T08:59:21.435+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:59:21.435+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:59:21.436+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T08:59:37.315+07:00 DEBUG 87208 --- [qtp2072567128-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T08:59:51.493+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T08:59:51.494+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T08:59:51.495+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:00:05.519+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-07-10T09:00:05.521+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T09:00:05.522+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-07-10T09:00:05.524+07:00  INFO 87208 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@09:00:05+0700
2025-07-10T09:00:05.555+07:00  INFO 87208 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@09:00:00+0700 to 10/07/2025@09:15:00+0700
2025-07-10T09:00:05.555+07:00  INFO 87208 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@09:00:00+0700 to 10/07/2025@09:15:00+0700
2025-07-10T09:00:05.555+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T09:00:05.556+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:00:11.606+07:00 DEBUG 87208 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T09:00:19.601+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:00:19.603+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:00:19.604+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:00:37.308+07:00 DEBUG 87208 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:00:47.652+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T09:00:47.656+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:00:47.657+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:00:47.657+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:00:47.657+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:01:06.685+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:01:21.718+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:01:21.718+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:01:21.718+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:01:37.314+07:00 DEBUG 87208 --- [qtp2072567128-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:01:50.770+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:01:50.772+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:01:50.772+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:02:04.796+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:02:18.823+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:02:18.824+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:02:18.824+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:02:37.331+07:00 DEBUG 87208 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:02:51.905+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T09:02:51.918+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:02:51.919+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:02:51.919+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:02:51.919+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:03:06.943+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:03:21.971+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:03:21.972+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:03:21.972+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:03:37.319+07:00 DEBUG 87208 --- [qtp2072567128-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:03:50.015+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:03:50.016+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:03:50.016+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:04:04.032+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:04:18.051+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:04:18.052+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:04:18.053+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:04:37.313+07:00 DEBUG 87208 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:04:52.114+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T09:04:52.122+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:04:52.123+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:04:52.123+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:04:52.123+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:05:06.147+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T09:05:06.149+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:05:21.175+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:05:21.176+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:05:21.177+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:05:37.317+07:00 DEBUG 87208 --- [qtp2072567128-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:05:49.222+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:05:49.222+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:05:49.223+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:06:03.241+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:06:17.264+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:06:17.265+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:06:17.266+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:06:37.327+07:00 DEBUG 87208 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:06:51.318+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:06:51.319+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:06:51.320+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:06:51.320+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:06:51.320+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:07:06.349+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:07:20.369+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:07:20.373+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:07:20.374+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:07:37.302+07:00 DEBUG 87208 --- [qtp2072567128-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:07:48.405+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:07:48.406+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:07:48.406+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:08:02.421+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:08:21.453+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:08:21.454+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:08:21.455+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:08:37.321+07:00 DEBUG 87208 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:08:51.528+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T09:08:51.540+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:08:51.540+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:08:51.540+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:08:51.541+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:09:05.566+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:09:19.588+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:09:19.589+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:09:19.589+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:09:37.294+07:00 DEBUG 87208 --- [qtp2072567128-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:09:47.640+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:09:47.640+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:09:47.641+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:10:06.667+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T09:10:06.670+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:10:21.699+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:10:21.700+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:10:21.700+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:10:37.298+07:00 DEBUG 87208 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:10:50.743+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-10T09:10:50.746+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:10:50.746+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:10:50.747+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:10:50.747+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:11:04.763+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:11:18.786+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:11:18.787+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:11:18.787+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:11:37.294+07:00 DEBUG 87208 --- [qtp2072567128-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:11:51.831+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:11:51.832+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:11:51.832+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:12:06.861+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:12:21.882+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:12:21.883+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:12:21.883+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:12:37.293+07:00 DEBUG 87208 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:12:49.933+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T09:12:49.941+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:12:49.941+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:12:49.941+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:12:49.941+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:13:03.965+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:13:17.989+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:13:17.989+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:13:17.989+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:13:37.298+07:00 DEBUG 87208 --- [qtp2072567128-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:13:52.040+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:13:52.041+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:13:52.042+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:14:06.061+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:14:21.087+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:14:21.088+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:14:21.088+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:14:37.296+07:00 DEBUG 87208 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:14:49.137+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:14:49.140+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:14:49.140+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:14:49.140+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:14:49.140+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:15:03.169+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T09:15:03.171+07:00  INFO 87208 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@09:15:03+0700
2025-07-10T09:15:03.188+07:00  INFO 87208 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@09:15:00+0700 to 10/07/2025@09:30:00+0700
2025-07-10T09:15:03.188+07:00  INFO 87208 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@09:15:00+0700 to 10/07/2025@09:30:00+0700
2025-07-10T09:15:03.189+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T09:15:03.189+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:15:09.197+07:00 DEBUG 87208 --- [botTaskExecutor-1] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T09:15:17.212+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:15:17.213+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:15:17.214+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:15:37.299+07:00 DEBUG 87208 --- [qtp2072567128-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:15:51.269+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:15:51.271+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:15:51.271+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:16:06.302+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:16:20.327+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:16:20.328+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:16:20.328+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:16:37.331+07:00 DEBUG 87208 --- [qtp2072567128-97] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:16:48.386+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T09:16:48.402+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:16:48.403+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:16:48.403+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:16:48.404+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:17:02.429+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:17:21.457+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:17:21.457+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:17:21.458+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:17:37.316+07:00 DEBUG 87208 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:17:51.516+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:17:51.517+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:17:51.517+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:18:05.543+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:18:19.569+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:18:19.570+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:18:19.570+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:18:37.329+07:00 DEBUG 87208 --- [qtp2072567128-100] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:18:47.613+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:18:47.615+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:18:47.616+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:18:47.617+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:18:47.617+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:19:06.648+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:19:21.679+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:19:21.679+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:19:21.679+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:19:37.288+07:00 DEBUG 87208 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:19:50.733+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:19:50.734+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:19:50.734+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:20:04.755+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T09:20:04.757+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:20:18.776+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:20:18.777+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:20:18.777+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:20:37.293+07:00 DEBUG 87208 --- [qtp2072567128-97] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:20:51.824+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T09:20:51.830+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:20:51.830+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:20:51.830+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:20:51.830+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:21:04.455+07:00 DEBUG 87208 --- [qtp2072567128-100] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:21:06.972+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:21:21.998+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:21:21.999+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:21:22.000+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:21:31.053+07:00  INFO 87208 --- [qtp2072567128-41] n.d.m.c.BotSendMessageBotHandler         : Process send message You have new message from CRM system !. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-07-10T09:21:31.054+07:00  INFO 87208 --- [qtp2072567128-41] n.d.m.c.BotSendMessageBotHandler         : Process send message You have new message from CRM system !. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-07-10T09:21:31.839+07:00  WARN 87208 --- [ForkJoinPool.commonPool-worker-1] c.m.a.m.ConfidentialClientApplication    : [Correlation ID: 670c71df-876b-4e5a-bb5f-6a33b6f4762e] Execution of class com.microsoft.aad.msal4j.AcquireTokenSilentSupplier failed: Token not found in the cache
2025-07-10T09:21:32.083+07:00  INFO 87208 --- [qtp2072567128-41] c.azure.identity.ClientSecretCredential  : Azure Identity => getToken() result for scopes [https://graph.microsoft.com/.default]: SUCCESS
2025-07-10T09:21:50.048+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:21:50.052+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:21:50.053+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:22:04.082+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:22:05.301+07:00 DEBUG 87208 --- [qtp2072567128-116] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:22:18.099+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:22:18.100+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:22:18.100+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:22:51.160+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T09:22:51.172+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:22:51.172+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:22:51.172+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:22:51.173+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:23:06.206+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:23:21.235+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:23:21.235+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:23:21.236+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:23:37.320+07:00 DEBUG 87208 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:23:49.282+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:23:49.283+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:23:49.283+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:24:03.305+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:24:17.330+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:24:17.331+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:24:17.332+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:24:37.305+07:00 DEBUG 87208 --- [qtp2072567128-116] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:24:51.409+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-07-10T09:24:51.412+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:24:51.412+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:24:51.412+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:24:51.412+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:25:06.441+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T09:25:06.442+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:25:20.517+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:25:20.518+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:25:20.518+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:25:37.360+07:00 DEBUG 87208 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:25:48.566+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:25:48.567+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:25:48.567+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:26:02.594+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:26:21.627+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:26:21.627+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:26:21.628+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:26:37.380+07:00 DEBUG 87208 --- [qtp2072567128-124] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:26:51.680+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T09:26:51.688+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:26:51.688+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:26:51.688+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:26:51.688+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:27:05.715+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:27:19.745+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:27:19.746+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:27:19.746+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:27:37.365+07:00 DEBUG 87208 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:27:47.783+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:27:47.784+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:27:47.784+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:28:06.807+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:28:21.845+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:28:21.848+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:28:21.848+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:28:37.350+07:00 DEBUG 87208 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:28:50.906+07:00  INFO 87208 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:28:50.908+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:28:50.908+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:28:50.908+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:28:50.908+07:00  INFO 87208 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:29:04.934+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:29:18.955+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:29:18.955+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:29:18.955+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:29:37.352+07:00 DEBUG 87208 --- [qtp2072567128-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T09:29:51.993+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:29:51.997+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:29:51.998+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:30:06.025+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T09:30:07.034+07:00  INFO 87208 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@09:30:07+0700
2025-07-10T09:30:07.050+07:00  INFO 87208 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@09:30:00+0700 to 10/07/2025@09:45:00+0700
2025-07-10T09:30:07.051+07:00  INFO 87208 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@09:30:00+0700 to 10/07/2025@09:45:00+0700
2025-07-10T09:30:07.053+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T09:30:07.053+07:00  INFO 87208 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T09:30:07.059+07:00 DEBUG 87208 --- [botTaskExecutor-3] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T09:30:22.084+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T09:30:22.085+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0fqgkgzpr39fbx7k0j7dfeaou0, remote user nhat.le
2025-07-10T09:30:22.085+07:00 DEBUG 87208 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T09:30:32.052+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@6b12f3d{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T09:30:32.053+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-10T09:30:32.054+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-10T09:30:32.117+07:00  INFO 87208 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T09:30:32.168+07:00  INFO 87208 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-10T09:30:32.172+07:00  INFO 87208 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-10T09:30:32.189+07:00  INFO 87208 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T09:30:32.190+07:00  INFO 87208 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T09:30:32.190+07:00  INFO 87208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-10T09:30:32.191+07:00  INFO 87208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-10T09:30:32.191+07:00  INFO 87208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-10T09:30:32.334+07:00  INFO 87208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-10T09:30:32.334+07:00  INFO 87208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-10T09:30:32.335+07:00  INFO 87208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-10T09:30:32.335+07:00  INFO 87208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-10T09:30:32.335+07:00  INFO 87208 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-10T09:30:32.337+07:00  INFO 87208 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7441eed3{STOPPING}[12.0.15,sto=0]
2025-07-10T09:30:32.340+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10T09:30:32.341+07:00  INFO 87208 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@2381a837{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5747435328460908252/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@2523730b{STOPPED}}
