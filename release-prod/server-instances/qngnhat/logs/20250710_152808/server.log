2025-07-10T15:28:08.622+07:00  INFO 33964 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 33964 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-10T15:28:08.623+07:00  INFO 33964 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-10T15:28:09.357+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.458+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 97 ms. Found 22 JPA repository interfaces.
2025-07-10T15:28:09.466+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.468+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T15:28:09.468+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.475+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-10T15:28:09.476+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.479+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T15:28:09.479+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.483+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T15:28:09.494+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.499+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-10T15:28:09.509+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.513+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-07-10T15:28:09.516+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.519+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T15:28:09.519+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.519+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:28:09.524+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.529+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T15:28:09.534+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.536+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T15:28:09.536+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.540+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T15:28:09.541+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.548+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-10T15:28:09.548+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.551+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T15:28:09.551+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.552+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:28:09.552+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.553+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T15:28:09.553+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.557+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-10T15:28:09.558+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.559+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T15:28:09.559+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.559+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:28:09.559+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.569+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-07-10T15:28:09.579+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.585+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-07-10T15:28:09.585+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.588+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T15:28:09.588+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.593+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-10T15:28:09.593+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.598+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T15:28:09.598+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.602+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T15:28:09.602+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.605+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T15:28:09.605+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.614+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-07-10T15:28:09.615+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.629+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 25 JPA repository interfaces.
2025-07-10T15:28:09.644+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.656+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-07-10T15:28:09.656+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.660+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T15:28:09.660+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.662+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T15:28:09.666+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.667+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T15:28:09.667+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.674+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-10T15:28:09.678+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.714+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 67 JPA repository interfaces.
2025-07-10T15:28:09.715+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.716+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T15:28:09.716+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T15:28:09.718+07:00  INFO 33964 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T15:28:09.903+07:00  INFO 33964 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-10T15:28:09.906+07:00  INFO 33964 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-10T15:28:10.176+07:00  WARN 33964 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-10T15:28:10.366+07:00  INFO 33964 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-10T15:28:10.368+07:00  INFO 33964 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-10T15:28:10.379+07:00  INFO 33964 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-10T15:28:10.379+07:00  INFO 33964 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1637 ms
2025-07-10T15:28:10.430+07:00  WARN 33964 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:28:10.430+07:00  INFO 33964 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-10T15:28:10.525+07:00  INFO 33964 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@62fee9c3
2025-07-10T15:28:10.526+07:00  INFO 33964 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-10T15:28:10.531+07:00  WARN 33964 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:28:10.531+07:00  INFO 33964 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-10T15:28:10.536+07:00  INFO 33964 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@7e38f55
2025-07-10T15:28:10.537+07:00  INFO 33964 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-10T15:28:10.537+07:00  WARN 33964 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:28:10.537+07:00  INFO 33964 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-10T15:28:11.017+07:00  INFO 33964 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2004f99
2025-07-10T15:28:11.017+07:00  INFO 33964 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-10T15:28:11.017+07:00  WARN 33964 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T15:28:11.017+07:00  INFO 33964 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-10T15:28:11.029+07:00  INFO 33964 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@3c92f2f9
2025-07-10T15:28:11.029+07:00  INFO 33964 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-10T15:28:11.029+07:00  INFO 33964 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-10T15:28:11.081+07:00  INFO 33964 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-10T15:28:11.083+07:00  INFO 33964 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@25c887ca{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12801533051051675932/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@647dcc5e{STARTED}}
2025-07-10T15:28:11.087+07:00  INFO 33964 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@25c887ca{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12801533051051675932/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@647dcc5e{STARTED}}
2025-07-10T15:28:11.089+07:00  INFO 33964 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7441eed3{STARTING}[12.0.15,sto=0] @3058ms
2025-07-10T15:28:11.145+07:00  INFO 33964 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T15:28:11.176+07:00  INFO 33964 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-10T15:28:11.192+07:00  INFO 33964 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T15:28:11.323+07:00  INFO 33964 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T15:28:11.350+07:00  WARN 33964 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T15:28:11.982+07:00  INFO 33964 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T15:28:11.991+07:00  INFO 33964 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6a355f4e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T15:28:12.020+07:00  INFO 33964 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:28:12.207+07:00  INFO 33964 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-10T15:28:12.210+07:00  INFO 33964 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-10T15:28:12.218+07:00  INFO 33964 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T15:28:12.219+07:00  INFO 33964 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T15:28:12.250+07:00  INFO 33964 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T15:28:12.253+07:00  WARN 33964 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T15:28:15.281+07:00  INFO 33964 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T15:28:15.282+07:00  INFO 33964 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@79dde7d9] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T15:28:15.386+07:00  INFO 33964 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:28:15.422+07:00  INFO 33964 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-10T15:28:15.427+07:00  INFO 33964 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-10T15:28:15.428+07:00  INFO 33964 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T15:28:15.435+07:00  WARN 33964 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T15:28:15.568+07:00  INFO 33964 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10T15:28:16.049+07:00  INFO 33964 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T15:28:16.053+07:00  INFO 33964 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T15:28:16.087+07:00  INFO 33964 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-10T15:28:16.130+07:00  INFO 33964 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-10T15:28:16.221+07:00  INFO 33964 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-10T15:28:16.253+07:00  INFO 33964 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T15:28:16.277+07:00  INFO 33964 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 24138896ms : this is harmless.
2025-07-10T15:28:16.286+07:00  INFO 33964 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-10T15:28:16.290+07:00  INFO 33964 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T15:28:16.303+07:00  INFO 33964 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 24138882ms : this is harmless.
2025-07-10T15:28:16.305+07:00  INFO 33964 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-10T15:28:16.320+07:00  INFO 33964 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-10T15:28:16.321+07:00  INFO 33964 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-10T15:28:18.948+07:00  INFO 33964 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@15:15:00+0700 to 10/07/2025@15:30:00+0700
2025-07-10T15:28:18.949+07:00  INFO 33964 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@15:15:00+0700 to 10/07/2025@15:30:00+0700
2025-07-10T15:28:19.546+07:00  INFO 33964 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-10T15:28:19.546+07:00  INFO 33964 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T15:28:19.547+07:00  WARN 33964 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T15:28:19.793+07:00  INFO 33964 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-10T15:28:19.793+07:00  INFO 33964 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-10T15:28:19.793+07:00  INFO 33964 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-10T15:28:19.793+07:00  INFO 33964 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-10T15:28:19.793+07:00  INFO 33964 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-10T15:28:21.419+07:00  WARN 33964 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: a53fb3b0-c68c-4d61-a980-b09ada66a827

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10T15:28:21.423+07:00  INFO 33964 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-10T15:28:21.739+07:00  INFO 33964 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T15:28:21.740+07:00  INFO 33964 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-10T15:28:21.740+07:00  INFO 33964 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-10T15:28:21.740+07:00  INFO 33964 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T15:28:21.740+07:00  INFO 33964 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-10T15:28:21.740+07:00  INFO 33964 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T15:28:21.740+07:00  INFO 33964 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T15:28:21.740+07:00  INFO 33964 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-10T15:28:21.740+07:00  INFO 33964 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T15:28:21.740+07:00  INFO 33964 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T15:28:21.740+07:00  INFO 33964 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-10T15:28:21.740+07:00  INFO 33964 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-10T15:28:21.743+07:00  INFO 33964 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T15:28:21.743+07:00  INFO 33964 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T15:28:21.743+07:00  INFO 33964 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T15:28:21.804+07:00  INFO 33964 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10T15:28:21.804+07:00  INFO 33964 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-10T15:28:21.807+07:00  INFO 33964 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-07-10T15:28:21.815+07:00  INFO 33964 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@7833bd7c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T15:28:21.816+07:00  INFO 33964 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-10T15:28:21.817+07:00  INFO 33964 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-10T15:28:21.845+07:00  INFO 33964 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-10T15:28:21.845+07:00  INFO 33964 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-10T15:28:21.851+07:00  INFO 33964 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.579 seconds (process running for 13.821)
2025-07-10T15:28:41.178+07:00  INFO 33964 --- [qtp2072567128-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T15:29:03.823+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:29:24.887+07:00  INFO 33964 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:29:24.906+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:29:24.906+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:29:24.906+07:00  INFO 33964 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:29:42.215+07:00  INFO 33964 --- [qtp2072567128-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01mlbmd3biypfnozj7q8c5avfo0
2025-07-10T15:29:42.215+07:00  INFO 33964 --- [qtp2072567128-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0dyh6f6wb1n6ahbxz4a7ts1sb1
2025-07-10T15:29:42.281+07:00  INFO 33964 --- [qtp2072567128-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01mlbmd3biypfnozj7q8c5avfo0, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T15:29:42.282+07:00  INFO 33964 --- [qtp2072567128-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0dyh6f6wb1n6ahbxz4a7ts1sb1, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T15:29:42.364+07:00  INFO 33964 --- [qtp2072567128-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T15:29:42.364+07:00  INFO 33964 --- [qtp2072567128-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T15:29:43.849+07:00 DEBUG 33964 --- [qtp2072567128-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:29:43.865+07:00 DEBUG 33964 --- [qtp2072567128-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:29:57.961+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:29:57.962+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:29:57.963+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:30:06.975+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:30:06.977+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:30:06.978+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-10T15:30:06.979+07:00  INFO 33964 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 10/07/2025@15:30:06+0700
2025-07-10T15:30:06.989+07:00  INFO 33964 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@15:30:00+0700 to 10/07/2025@15:45:00+0700
2025-07-10T15:30:06.990+07:00  INFO 33964 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@15:30:00+0700 to 10/07/2025@15:45:00+0700
2025-07-10T15:30:13.020+07:00 DEBUG 33964 --- [botTaskExecutor-1] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "10/07/2025 00:00:00 GMT+0700",
      "toDate" : "10/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-10T15:30:28.039+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:30:28.040+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:30:28.041+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:30:43.883+07:00 DEBUG 33964 --- [qtp2072567128-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:30:56.090+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:30:56.091+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:30:56.091+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:31:03.100+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:31:24.140+07:00  INFO 33964 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-07-10T15:31:24.153+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:31:24.153+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:31:24.153+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:31:24.159+07:00  INFO 33964 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T15:31:43.848+07:00 DEBUG 33964 --- [qtp2072567128-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:31:58.218+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:31:58.218+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:31:58.219+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:32:06.229+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:32:27.267+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:32:27.269+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:32:27.269+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:32:43.851+07:00 DEBUG 33964 --- [qtp2072567128-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:32:55.314+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:32:55.315+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:32:55.317+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:33:02.329+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:33:28.373+07:00  INFO 33964 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T15:33:28.381+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:33:28.381+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:33:28.381+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:33:28.381+07:00  INFO 33964 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:33:43.857+07:00 DEBUG 33964 --- [qtp2072567128-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:33:58.433+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:33:58.434+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:33:58.434+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:34:05.442+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:34:26.474+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:34:26.474+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:34:26.474+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:34:43.848+07:00 DEBUG 33964 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:34:54.474+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:34:54.476+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:34:54.476+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:35:06.497+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:35:06.503+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:35:28.579+07:00  INFO 33964 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-07-10T15:35:28.606+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:35:28.611+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:35:28.611+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:35:28.612+07:00  INFO 33964 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:35:43.837+07:00 DEBUG 33964 --- [qtp2072567128-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:35:57.670+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:35:57.671+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:35:57.671+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:36:04.681+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:36:25.708+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:36:25.709+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:36:25.709+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:36:43.801+07:00 DEBUG 33964 --- [qtp2072567128-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:36:58.764+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:36:58.774+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:36:58.775+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:37:06.790+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:37:27.823+07:00  INFO 33964 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:37:27.828+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:37:27.828+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:37:27.828+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:37:27.829+07:00  INFO 33964 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:37:43.833+07:00 DEBUG 33964 --- [qtp2072567128-69] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:37:56.878+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:37:56.879+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0dyh6f6wb1n6ahbxz4a7ts1sb1, remote user nhat.le
2025-07-10T15:37:56.879+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:38:03.888+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:38:11.052+07:00  INFO 33964 --- [Scheduler-1145657508-1] n.d.m.session.AppHttpSessionListener     : The session node0dyh6f6wb1n6ahbxz4a7ts1sb1 is destroyed.
2025-07-10T15:38:24.924+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:38:24.926+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:38:43.848+07:00  INFO 33964 --- [qtp2072567128-41] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T15:38:57.980+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:38:57.981+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:39:07.000+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:39:28.050+07:00  INFO 33964 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-10T15:39:28.061+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:39:28.062+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:39:28.062+07:00  INFO 33964 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:39:44.913+07:00  INFO 33964 --- [qtp2072567128-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01mlbmd3biypfnozj7q8c5avfo0, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T15:39:44.913+07:00  INFO 33964 --- [qtp2072567128-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01mlbmd3biypfnozj7q8c5avfo0, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T15:39:44.941+07:00  INFO 33964 --- [qtp2072567128-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T15:39:44.942+07:00  INFO 33964 --- [qtp2072567128-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T15:39:46.280+07:00 DEBUG 33964 --- [qtp2072567128-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:39:46.311+07:00 DEBUG 33964 --- [qtp2072567128-76] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T15:39:56.127+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T15:39:56.129+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01mlbmd3biypfnozj7q8c5avfo0, remote user nhat.le
2025-07-10T15:39:56.129+07:00 DEBUG 33964 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T15:40:03.144+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T15:40:03.147+07:00  INFO 33964 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T15:40:15.537+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@7833bd7c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T15:40:15.540+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T15:40:15.540+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T15:40:15.540+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-10T15:40:15.541+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-10T15:40:15.559+07:00  INFO 33964 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T15:40:15.642+07:00  INFO 33964 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-10T15:40:15.658+07:00  INFO 33964 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-10T15:40:15.690+07:00  INFO 33964 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:40:15.693+07:00  INFO 33964 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T15:40:15.693+07:00  INFO 33964 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-10T15:40:15.694+07:00  INFO 33964 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-10T15:40:15.694+07:00  INFO 33964 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-10T15:40:15.834+07:00  INFO 33964 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-10T15:40:15.834+07:00  INFO 33964 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-10T15:40:15.835+07:00  INFO 33964 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-10T15:40:15.835+07:00  INFO 33964 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-10T15:40:15.836+07:00  INFO 33964 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-10T15:40:15.838+07:00  INFO 33964 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7441eed3{STOPPING}[12.0.15,sto=0]
2025-07-10T15:40:15.844+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10T15:40:15.845+07:00  INFO 33964 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@25c887ca{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12801533051051675932/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@647dcc5e{STOPPED}}
