2025-07-10T14:14:54.927+07:00  INFO 20219 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 20219 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-10T14:14:54.928+07:00  INFO 20219 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-10T14:14:55.735+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.846+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 107 ms. Found 22 JPA repository interfaces.
2025-07-10T14:14:55.855+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.857+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-10T14:14:55.857+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.864+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 9 JPA repository interfaces.
2025-07-10T14:14:55.866+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.868+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T14:14:55.869+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.872+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T14:14:55.883+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.888+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-10T14:14:55.898+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.902+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-10T14:14:55.906+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.908+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 3 JPA repository interfaces.
2025-07-10T14:14:55.908+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.909+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:14:55.913+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.919+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-10T14:14:55.924+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.926+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-10T14:14:55.926+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.930+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T14:14:55.931+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.938+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-10T14:14:55.938+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.941+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T14:14:55.941+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.942+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:14:55.942+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.942+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-10T14:14:55.943+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.947+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-10T14:14:55.947+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.948+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T14:14:55.948+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.948+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:14:55.948+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.958+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-07-10T14:14:55.968+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.974+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-07-10T14:14:55.974+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.977+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T14:14:55.977+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.981+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-10T14:14:55.981+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.986+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-07-10T14:14:55.986+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.989+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T14:14:55.990+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:55.993+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-10T14:14:55.993+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:56.005+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 14 JPA repository interfaces.
2025-07-10T14:14:56.006+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:56.022+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 25 JPA repository interfaces.
2025-07-10T14:14:56.037+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:56.048+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-10T14:14:56.048+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:56.052+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-10T14:14:56.052+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:56.053+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-10T14:14:56.059+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:56.059+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-10T14:14:56.060+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:56.066+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-10T14:14:56.071+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:56.105+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 67 JPA repository interfaces.
2025-07-10T14:14:56.106+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:56.107+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-10T14:14:56.107+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10T14:14:56.109+07:00  INFO 20219 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-10T14:14:56.300+07:00  INFO 20219 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-10T14:14:56.303+07:00  INFO 20219 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-10T14:14:56.598+07:00  WARN 20219 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-10T14:14:56.942+07:00  INFO 20219 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-10T14:14:56.946+07:00  INFO 20219 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-10T14:14:56.965+07:00  INFO 20219 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-10T14:14:56.966+07:00  INFO 20219 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1907 ms
2025-07-10T14:14:57.028+07:00  WARN 20219 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:14:57.028+07:00  INFO 20219 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-10T14:14:57.151+07:00  INFO 20219 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@43941806
2025-07-10T14:14:57.151+07:00  INFO 20219 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-10T14:14:57.156+07:00  WARN 20219 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:14:57.156+07:00  INFO 20219 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-10T14:14:57.164+07:00  INFO 20219 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@71e6bac0
2025-07-10T14:14:57.164+07:00  INFO 20219 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-10T14:14:57.164+07:00  WARN 20219 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:14:57.164+07:00  INFO 20219 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-10T14:14:57.672+07:00  INFO 20219 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@447cdbaa
2025-07-10T14:14:57.673+07:00  INFO 20219 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-10T14:14:57.673+07:00  WARN 20219 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-10T14:14:57.673+07:00  INFO 20219 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-10T14:14:57.683+07:00  INFO 20219 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@20395afe
2025-07-10T14:14:57.683+07:00  INFO 20219 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-10T14:14:57.683+07:00  INFO 20219 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-10T14:14:57.737+07:00  INFO 20219 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-10T14:14:57.739+07:00  INFO 20219 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@6757ed9e{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15228805020117511942/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7b0eb7e{STARTED}}
2025-07-10T14:14:57.740+07:00  INFO 20219 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@6757ed9e{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15228805020117511942/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7b0eb7e{STARTED}}
2025-07-10T14:14:57.743+07:00  INFO 20219 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@290ebbbe{STARTING}[12.0.15,sto=0] @3376ms
2025-07-10T14:14:57.814+07:00  INFO 20219 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T14:14:57.844+07:00  INFO 20219 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-10T14:14:57.860+07:00  INFO 20219 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T14:14:57.989+07:00  INFO 20219 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T14:14:58.018+07:00  WARN 20219 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T14:14:58.626+07:00  INFO 20219 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T14:14:58.636+07:00  INFO 20219 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@75f929a0] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T14:14:58.683+07:00  INFO 20219 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:14:58.930+07:00  INFO 20219 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-10T14:14:58.932+07:00  INFO 20219 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-10T14:14:58.941+07:00  INFO 20219 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10T14:14:58.942+07:00  INFO 20219 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-10T14:14:58.973+07:00  INFO 20219 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10T14:14:58.979+07:00  WARN 20219 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-10T14:15:01.744+07:00  INFO 20219 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10T14:15:01.745+07:00  INFO 20219 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@49af0552] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-10T14:15:01.843+07:00  INFO 20219 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:15:01.877+07:00  INFO 20219 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-10T14:15:01.882+07:00  INFO 20219 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-10T14:15:01.882+07:00  INFO 20219 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T14:15:01.889+07:00  WARN 20219 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T14:15:02.020+07:00  INFO 20219 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10T14:15:02.497+07:00  INFO 20219 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T14:15:02.500+07:00  INFO 20219 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-10T14:15:02.536+07:00  INFO 20219 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-10T14:15:02.580+07:00  INFO 20219 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-10T14:15:02.663+07:00  INFO 20219 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-10T14:15:02.691+07:00  INFO 20219 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T14:15:02.715+07:00  INFO 20219 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 19577555ms : this is harmless.
2025-07-10T14:15:02.723+07:00  INFO 20219 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-10T14:15:02.726+07:00  INFO 20219 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-10T14:15:02.753+07:00  INFO 20219 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 19577542ms : this is harmless.
2025-07-10T14:15:02.755+07:00  INFO 20219 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-10T14:15:02.768+07:00  INFO 20219 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-10T14:15:02.769+07:00  INFO 20219 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-10T14:15:05.326+07:00  INFO 20219 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 10/07/2025@14:15:00+0700 to 10/07/2025@14:30:00+0700
2025-07-10T14:15:05.327+07:00  INFO 20219 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 10/07/2025@14:15:00+0700 to 10/07/2025@14:30:00+0700
2025-07-10T14:15:06.160+07:00  INFO 20219 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-10T14:15:06.160+07:00  INFO 20219 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-10T14:15:06.161+07:00  WARN 20219 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-10T14:15:06.422+07:00  INFO 20219 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-10T14:15:06.423+07:00  INFO 20219 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-10T14:15:06.423+07:00  INFO 20219 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-10T14:15:06.423+07:00  INFO 20219 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-10T14:15:06.423+07:00  INFO 20219 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-10T14:15:07.936+07:00  WARN 20219 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 07046ca3-da45-41d0-87b2-cad6d60bd986

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-10T14:15:07.940+07:00  INFO 20219 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-10T14:15:08.257+07:00  INFO 20219 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-10T14:15:08.260+07:00  INFO 20219 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T14:15:08.261+07:00  INFO 20219 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T14:15:08.261+07:00  INFO 20219 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T14:15:08.345+07:00  INFO 20219 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10T14:15:08.345+07:00  INFO 20219 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-10T14:15:08.347+07:00  INFO 20219 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-10T14:15:08.355+07:00  INFO 20219 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@1a71bbbe{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T14:15:08.356+07:00  INFO 20219 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-10T14:15:08.357+07:00  INFO 20219 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-10T14:15:08.388+07:00  INFO 20219 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-10T14:15:08.388+07:00  INFO 20219 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-10T14:15:08.394+07:00  INFO 20219 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.772 seconds (process running for 14.027)
2025-07-10T14:15:25.699+07:00  INFO 20219 --- [qtp1007876221-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-10T14:16:04.366+07:00  INFO 20219 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:16:11.412+07:00  INFO 20219 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:16:11.429+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:16:11.429+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:16:11.429+07:00  INFO 20219 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:16:26.679+07:00  INFO 20219 --- [qtp1007876221-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node04mncndq8n4rw16wvf3sop26ex0
2025-07-10T14:16:26.679+07:00  INFO 20219 --- [qtp1007876221-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node019z4mt3wxwkud1xxyyl2l6km911
2025-07-10T14:16:26.746+07:00  INFO 20219 --- [qtp1007876221-40] n.d.module.session.ClientSessionManager  : Add a client session id = node019z4mt3wxwkud1xxyyl2l6km911, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:16:26.746+07:00  INFO 20219 --- [qtp1007876221-35] n.d.module.session.ClientSessionManager  : Add a client session id = node04mncndq8n4rw16wvf3sop26ex0, token = 1f9d7aad5f64922e15670196c1ab7074
2025-07-10T14:16:26.823+07:00  INFO 20219 --- [qtp1007876221-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:16:26.823+07:00  INFO 20219 --- [qtp1007876221-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-10T14:16:28.353+07:00 DEBUG 20219 --- [qtp1007876221-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:16:28.374+07:00 DEBUG 20219 --- [qtp1007876221-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:16:44.490+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:16:44.491+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:16:44.493+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:17:06.525+07:00  INFO 20219 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:17:14.548+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:17:14.550+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:17:14.551+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:17:28.408+07:00 DEBUG 20219 --- [qtp1007876221-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:17:42.597+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:17:42.598+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:17:42.599+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:18:03.640+07:00  INFO 20219 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:18:10.670+07:00  INFO 20219 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-07-10T14:18:10.685+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:18:10.685+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:18:10.685+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:18:10.689+07:00  INFO 20219 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-10T14:18:28.367+07:00 DEBUG 20219 --- [qtp1007876221-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:18:44.755+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:18:44.756+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:18:44.756+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:19:06.788+07:00  INFO 20219 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:19:13.804+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:19:13.805+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:19:13.805+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:19:28.377+07:00 DEBUG 20219 --- [qtp1007876221-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:19:41.855+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:19:41.857+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:19:41.857+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:20:02.886+07:00  INFO 20219 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:20:02.893+07:00  INFO 20219 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-10T14:20:14.923+07:00  INFO 20219 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:20:14.927+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:20:14.928+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:20:14.928+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:20:14.929+07:00  INFO 20219 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:20:28.410+07:00 DEBUG 20219 --- [qtp1007876221-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:20:44.986+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:20:44.986+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:20:44.986+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:21:06.020+07:00  INFO 20219 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:21:13.033+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:21:13.033+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:21:13.034+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:21:28.366+07:00 DEBUG 20219 --- [qtp1007876221-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-10T14:21:41.093+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:21:41.096+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:21:41.097+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:22:02.139+07:00  INFO 20219 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-10T14:22:15.181+07:00  INFO 20219 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-10T14:22:15.196+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-10T14:22:15.197+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04mncndq8n4rw16wvf3sop26ex0, remote user nhat.le
2025-07-10T14:22:15.197+07:00 DEBUG 20219 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-10T14:22:15.197+07:00  INFO 20219 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:22:26.471+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@1a71bbbe{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-10T14:22:26.473+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-10T14:22:26.473+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-10T14:22:26.473+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-10T14:22:26.474+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-10T14:22:26.491+07:00  INFO 20219 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-10T14:22:26.564+07:00  INFO 20219 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-10T14:22:26.569+07:00  INFO 20219 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-10T14:22:26.667+07:00  INFO 20219 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:22:26.669+07:00  INFO 20219 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10T14:22:26.669+07:00  INFO 20219 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-10T14:22:26.671+07:00  INFO 20219 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-10T14:22:26.671+07:00  INFO 20219 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-10T14:22:26.808+07:00  INFO 20219 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-10T14:22:26.808+07:00  INFO 20219 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-10T14:22:26.809+07:00  INFO 20219 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-10T14:22:26.809+07:00  INFO 20219 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-10T14:22:26.809+07:00  INFO 20219 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-10T14:22:26.810+07:00  INFO 20219 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@290ebbbe{STOPPING}[12.0.15,sto=0]
2025-07-10T14:22:26.814+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10T14:22:26.816+07:00  INFO 20219 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@6757ed9e{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15228805020117511942/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7b0eb7e{STOPPED}}
